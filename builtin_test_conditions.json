{"errcode": 0, "errmsg": null, "data": [{"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "id_card_minor", "builtInName": "中华人民共和国网络安全法-公民个人信息类[中国大陆不满14岁未成年人身份证号]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\d|\\d\\.)(?:(?:[16][1-5]|21|22|23|3[1-7]|4[1-6]|5[0-4])\\d{4})(?:(?:[1|2]\\d{3}(?:0[1-9]|1[012])(?:0[1-9]|[12]\\d|3[01])\\d{3}[xX\\d])|(?:\\d{2}(?:0[1-9]|1[012])(?:0[1-9]|[12]\\d|3[01])\\d{3}))(?!\\d|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    str = string.upper(str)\n    -- check length\n    local length = string.len(str)\n    if 18 ~= length then\n        return 0\n    end\n\n    -- check province\n    local province = string.sub(str, 1, 6)\n    -- print( province )\n\n    -- check birthday\n    local birthday = string.sub(str, 7, 14)\n    local y = tonumber(string.sub(birthday, 1, 4))\n    local m = tonumber(string.sub(birthday, 5, 6))\n    local d = tonumber(string.sub(birthday, 7, 8))\n    local curYear = tonumber(os.date(\"%Y\", os.time()))\n    if (curYear - y >= 14 or curYear - y < 0 or m < 1 or m > 12 or d < 1 or d > 32) then\n        return 0\n    end\n\n    checksum = 0;\n    local weights = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 }\n\n    local i = 1\n    while i < length do\n        local digit = tonumber(string.sub(str, i, i)) * weights[i]\n        checksum = checksum + digit\n        i = i + 1\n    end\n\n    checksum = checksum % 11\n\n    local checksums = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' }\n    if checksums[checksum + 1] ~= string.sub(str, length) then\n        return 0\n    end\n\n    -- success\n    return 1\nend", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "phone_num_sc", "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（中国大陆）]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\+86)?[- ]?(?:13[0-9]|14[5-9]|15[0-35-9]|166|17[1-8]|18[0-9]|19[89])[- ]?\\d{4}[- ]?\\d{4}(?!\\w|\\.\\d)|(?<![Ａ-Ｚａ-ｚ０-９])(?:\\+８６)?[- ]?(?:１３[０-９]|１４[５-９]|１５[０-３５-９]|１６６|１７[１-８]|１８[０-９]|１９[８９])[- ]?[０-９]{4}[- ]?[０-９]{4}(?![Ａ-Ｚａ-ｚ０-９])"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "phone_num_hk", "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（香港）]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\+852)?[- ]?[5689]\\d{7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "phone_num_tw", "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（台湾）]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\+886[- ]?9\\d{8}|09\\d{8})(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "phone_num_sc_simple", "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（中国大陆）-宽]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<![０-９])(?:\\+８６)?[- ]?(?:１３[０-９]|１４[５-９]|１５[０-３５-９]|１６６|１７[１-８]|１８[０-９]|１９[１８９])[- ]?[０-９]{4}[- ]?[０-９]{4}(?![０-９])|(?<!\\d)(?:\\+86)?[- ]?(?:13[0-9]|14[5-9]|15[0-35-9]|166|17[1-8]|18[0-9]|19[189])[- ]?\\d{4}[- ]?\\d{4}(?!\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "phone_num_fixed_line", "builtInName": "中华人民共和国网络安全法-公民个人信息类[座机号码]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:010|02\\d|0[3-9]\\d{2})-[2-9]\\d{6,7}(?!\\w|\\.\\d)|(?<!\\w|\\d\\.)\\((?:010|02\\d|0[3-9]\\d{2})\\)[2-9]\\d{6,7}(?!\\w|\\.\\d)|(?<!\\w|\\d\\.|[\\)*\\+-/])[2-9]\\d{6,7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "address", "builtInName": "中华人民共和国网络安全法-公民个人信息类[通讯地址]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?:北京|天津|上海|重庆|河北|河南|云南|辽宁|黑龙江|湖南|湖北|江苏|山东|新疆|安徽|浙江|江西|广西|甘肃|陕西|山西|内蒙古|吉林|福建|贵州|广东|青海|四川|宁夏|海南|西藏|香港|澳门|台湾)[^，。：\"\"、]{1,15}?[盟州市区县][^，。：\"\"、]{1,15}?(?:[镇旗路村弄楼]|小区)[^，。：\"\"、]{1,15}?(?:\\d|[一二三四五六七八九十])[号室组]"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "hum_name", "builtInName": "中华人民共和国网络安全法-公民个人信息类[姓名（误报较高，请慎用）]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?:赵|钱|孙|李|周|吴|郑|王|冯|陈|褚|卫|蒋|沈|韩|杨|朱|秦|尤|许|何|吕|施|张|孔|曹|严|华|金|魏|陶|姜|戚|谢|邹|喻|柏|水|窦|章|云|苏|潘|葛|奚|范|彭|郎|鲁|韦|昌|马|苗|凤|花|方|俞|任|袁|柳|酆|鲍|史|唐|费|廉|岑|薛|雷|贺|倪|汤|滕|殷|罗|毕|郝|邬|安|常|乐|于|时|傅|皮|卞|齐|康|伍|元|余|卜|顾|孟|平|黄|和|穆|萧|尹|姚|邵|湛|汪|祁|毛|禹|狄|米|贝|明|臧|计|伏|成|戴|谈|宋|茅|庞|熊|纪|舒|屈|项|祝|董|梁|杜|阮|蓝|闵|席|季|麻|强|贾|路|娄|危|江|童|颜|郭|梅|盛|林|刁|钟|徐|邱|骆|高|夏|蔡|田|樊|胡|凌|霍|虞|万|支|柯|昝|管|卢|莫|白|房|裘|缪|干|解|应|宗|丁|宣|贲|邓|郁|单|杭|洪|包|诸|左|石|崔|吉|钮|龚|程|嵇|邢|滑|裴|陆|荣|翁|荀|羊|于|惠|甄|曲|家|封|芮|羿|储|靳|汲|邴|糜|松|井|段|富|巫|乌|焦|巴|弓|牧|隗|山|谷|车|侯|宓|蓬|全|郗|班|仰|秋|仲|伊|宫|宁|仇|栾|暴|甘|钭|历|戎|祖|武|符|刘|景|詹|束|龙|叶|幸|司|韶|郜|黎|蓟|溥|印|宿|白|怀|蒲|邰|从|鄂|索|咸|籍|赖|卓|蔺|屠|蒙|池|乔|阳|郁|胥|能|苍|双|闻|莘|党|翟|谭|贡|劳|逄|姬|申|扶|堵|冉|宰|郦|雍|却|璩|桑|桂|濮|牛|寿|通|边|扈|燕|冀|姓|浦|尚|农|温|别|庄|晏|柴|瞿|阎|充|慕|连|茹|习|宦|艾|鱼|容|向|古|易|慎|戈|廖|庾|终|暨|居|衡|步|都|耿|满|弘|匡|国|文|寇|广|禄|阙|东|欧|殳|沃|利|蔚|越|夔|隆|师|巩|厍|聂|晁|勾|敖|融|冷|訾|辛|阚|那|简|饶|空|曾|毋|沙|乜|养|鞠|须|丰|巢|关|蒯|相|查|后|荆|红|游|竺|权|逮|盍|益|桓|公|万俟|司马|上官|欧阳|夏侯|诸葛|闻人|东方|赫连|皇甫|尉迟|公羊|澹台|公冶|宗政|濮阳|淳于|单于|太叔|申屠|公孙|仲孙|轩辕|令狐|徐离|宇文|长孙|慕容|司徒|司空)[一-龥]{1,2}"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "vat_num", "builtInName": "其他[增值税发票号]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:11|12|13|14|15|21|22|23|31|32|33|34|35|36|37|41|42|43|44|45|46|50|51|52|53|54|61|62|63|64|65)\\d{5}[0-4][347][01234]|[012](?:11|12|13|14|15|21|22|23|31|32|33|34|35|36|37|41|42|43|44|45|46|50|51|52|53|54|61|62|63|64|65)\\d{7}11(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "china_rpc_num", "builtInName": "中华人民共和国网络安全法-公民个人信息类[大陆护照号]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w)(?:[GgEeSs]\\d{8}|[PpSs]\\d{7})(?!\\w)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "mac", "builtInName": "其他[MAC地址]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[A-F0-9a-f]{2}(?:[-:][A-Fa-f0-9]{2}){5}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "mail_addr", "builtInName": "中华人民共和国网络安全法-公民个人信息类[邮件地址]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["[\\w-]{1,32}@[\\w\\.-]+(?:\\.[a-zA-Z]{2,3})"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "ev_car_license_plate_num", "builtInName": "其他[新能源车牌号]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w)[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z]{1}(?:[DF]{1}[0-9]{5}|[0-9]{5}[DF]{1})(?!\\w)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "hk_macao_pass_num", "builtInName": "中华人民共和国网络安全法-公民个人信息类[港澳通行证号]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w)[CcWw]\\d{8}(?!\\w)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "ipv4", "builtInName": "其他[IPv4]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\.)(?:25[0-4]|2[0-4]\\d|1\\d{2}|[1-9]\\d|[1-9])\\.(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[0-9]\\d|[0-9])\\.){2}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|[1-9])(?!\\w|\\.)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_cn_passport_normal", "builtInName": "国际-护照[中国-普通-深红色]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)E[ABCDEFGHJKLMNPQRSTUVWXYZ][0-9]{7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_us_passport", "builtInName": "国际-护照[美国]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[A-Z]{1}[0-9]{8}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_us_green_card", "builtInName": "国际-美国绿卡", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[A-Z]{1}\\d{7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_car_license_plate_us_ny", "builtInName": "国际-驾照[美国-纽约]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)([A-Za-z0-9]{8,15}|[A-Za-z0-9](?:[- ]?[A-Za-z0-9]){7,14})(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_birth_cert_us_ny", "builtInName": "国际-出生证明[美国-纽约]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)([A-Z][0-9]{7,14}|\\d{8,15})(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_phone_uk_london_local", "builtInName": "国际-手机号码[英国-伦敦-本地]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)07[1-5|7-9]\\d{8}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_phone_uk_london_intl", "builtInName": "国际-手机号码[英国-伦敦-国际]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)\\+?44\\s*7[1-5|7-9]\\d{2}\\s*\\d{6}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_uk_nino", "builtInName": "国际-英国-国民保险号码", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[A-CEGHJ-PR-TW-Z][A-NP-Z](?: ?\\d{2}){3} ?[A-D ](?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_address_uk_london", "builtInName": "国际-通讯地址[英国-伦敦]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(^|\\s+)London.*(EC|WC|N|NW|E|W|SE|SW)\\d[A-Z0-9 ]{3,50}"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_phone_num_us_ny", "builtInName": "国际-手机号码[美国-纽约]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:(?:\\+1|1)[ -.]?)?(?:\\(?\\s*(212|646|718|347|917|332|929)\\s*\\)?[ -]?)?(?:[2-9]\\d{2})[ -]?\\d{4}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_address_us_ny", "builtInName": "国际-通讯地址[美国-纽约]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["\\d+\\s+[a-zA-Z\\s]+,\\s*(?:brooklyn|manhattan|queens|bronx|staten island|new york),\\s*ny\\s*\\d{5}"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_phone_num_hk_local", "builtInName": "国际-手机号码[香港-本地]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[569]\\d{7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_phone_num_hk_intl", "builtInName": "国际-手机号码[香港-国际]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(\\+852|852)[ -.]?[569]\\d{7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_id_card_hk", "builtInName": "国际-身份证[香港]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[A-Z]{1,2}\\d{6}(?:\\([A-Z0-9]\\))?(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_address_hk", "builtInName": "国际-通讯地址[香港]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["((?:香港|Kowloon|新界|中环|湾仔|尖沙咀|沙田)\\s*[^\\d\\s]{2,}\\s*\\d{1,5}[A-Za-z]?\\s*(?:Building|Tower|大厦|邨?|大廈)?\\s*(?:[Gg]|[1-9]?\\d*|(\\d+楼))?\\s*(?:Flat\\s*\\d+|Room\\s*\\d+|室\\s*\\d+|舖\\s*\\d*)?\\s*(\\d{1,4}[号楼]?)?)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_phone_mo_local", "builtInName": "国际-手机号码[澳门-本地]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(6|8|28)\\d{7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_phone_mo_intl", "builtInName": "国际-手机号码[澳门-国际]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)\\+853(6|8|28)\\d{7}(?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_id_card_mo", "builtInName": "国际-身份证[澳门]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[125679]\\d{6}[0-9X](?!\\w|\\.\\d)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_address_mo", "builtInName": "国际-通讯地址[澳门]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["((?:澳門|澳门|[澳MO]|Macau|Macao)\\s*(?:氹仔|路環|路环)?\\s*\\S{2,}(?:\\S*?\\s+)?(?:道|街|巷|圍|里|橫街|馬路|大馬路|台|径|前地|广場|廣場|堤岸|建築|大廈|大厦|中心|花園|花园|商業中心|商业中心|苑|村|樓|座|Road|Street|Square|Building|Centre|Garden|Village|Court|Plaza|\\d+[期樓座]|\\d+[A-Z]座)\\s*(?:\\d{1,4}[\\s柏le的]?[號樓]?|\\d{1,4}[A-Z][號樓]?|\\d{1,4}[A-Z]?\\s?[室舖]?)?)"], "regexCheckLuaScript": null, "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_uk_car_license_plate_num", "builtInName": "国际-驾照[英国]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[A-Za-z0-9]{16}(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    if #str ~= 16 then\n        logDebug(\"长度不正确\")\n        return 0\n    end\n\n    local surname = str:sub(1, 5)\n    if #surname < 5 then\n        surname = surname .. string.rep(\"9\", 5 - #surname)\n    end\n\n    local year_tens = str:sub(6, 6)\n    if not year_tens:match(\"%d\") then\n        logDebug(\"年份十位无效\")\n        return 0\n    end\n\n    local month = tonumber(str:sub(7, 8))\n    local is_female = false\n\n    if month > 12 then\n        is_female = true\n        month = month - 50\n    end\n\n    if month < 1 or month > 12 then\n        logDebug(\"月份无效\")\n        return 0\n    end\n\n    local day = tonumber(str:sub(9, 10))\n    if day < 1 or day > 31 then\n        logDebug(\"日期无效: \" .. day)\n        return 0\n    end\n\n    local year_units = str:sub(11, 11)\n    if not year_units:match(\"%d\") then\n        logDebug(\"年份个位无效\")\n        return 0\n    end\n\n    local random_digit = str:sub(14, 14)\n    if not random_digit:match(\"%d\") then\n        logDebug(\"随机数字无效\")\n        return 0\n    end\n\n    local checksum = str:sub(15, 16)\n    if not checksum:match(\"^[A-Za-z0-9]*$\") then\n        logDebug(\"校验位无效\")\n        return 0\n    end\n\n    return 1\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_simple", "builtInName": "中华人民共和国网络安全法-公民个人信息类[银行卡-全部（宽松）]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\d)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{19}|\\d{16}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"30\", \"35\", \"36\", \"37\", \"40\", \"41\", \"42\", \"43\", \"45\", \"48\", \"49\", \"51\", \"52\", \"53\", \"54\", \"55\", \"60\", \"62\", \"64\", \"65\", \"68\", \"91\", \"95\", \"99\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_bank_number_vesa", "builtInName": "国际-银行卡[Visa]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)\\d{4}[- ]?\\d{4}[- ]?\\d{4}[- ]?\\d{4}|\\d{4}[- ]?\\d{6}[- ]?\\d{5}(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"40\",\"41\",\"42\",\"43\",\"44\",\"45\",\"46\",\"47\",\"48\",\"49\" }\n    return checkBankNumber(str, prefixes, 13, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_bank_number_master_card", "builtInName": "国际-银行卡[MasterCard]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)\\d{4}[- ]?\\d{4}[- ]?\\d{4}[- ]?\\d{4}|\\d{4}[- ]?\\d{6}[- ]?\\d{5}(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"51\",\"55\" }\n    return checkBankNumber(str, prefixes, 13, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_bank_number_american_express", "builtInName": "国际-银行卡[AmericanExpress]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)\\d{4}[- ]?\\d{4}[- ]?\\d{4}[- ]?\\d{4}|\\d{4}[- ]?\\d{6}[- ]?\\d{5}(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"34\",\"37\" }\n    return checkBankNumber(str, prefixes, 13, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "intl_bank_number_discover", "builtInName": "国际-银行卡[Discover]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)\\d{4}[- ]?\\d{4}[- ]?\\d{4}[- ]?\\d{4}|\\d{4}[- ]?\\d{6}[- ]?\\d{5}(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"6011\",\"622\",\"644\",\"649\",\"65\" }\n    return checkBankNumber(str, prefixes, 13, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "car_vin", "builtInName": "其他[车辆VIN码]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)[AJ-MPSTV-Z1-9][1468ABFGHJLMNSTVYZ][A-HJ-NPR-Z\\d]{6}[\\dX][A-HJ-NPRSTV-Z\\d][A-HJ-NPR-Z\\d][A-HJ-NPR-Z\\d]{1}\\d{5}(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    str = string.upper(str)\n    -- check length\n    local length = string.len(str)\n    if 17 ~= length then\n        return 0\n    end\n\n    local ch_check = string.sub(str, 9, 9)\n    checksum = 0;\n    local weights = { 8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2 }\n    local map_ch = { [\"A\"] = 1, [\"B\"] = 2, [\"C\"] = 3, [\"D\"] = 4, [\"E\"] = 5, [\"F\"] = 6, [\"G\"] = 7, [\"H\"] = 8, [\"J\"] = 1, [\"K\"] = 2, [\"L\"] = 3, [\"M\"] = 4, [\"N\"] = 5, [\"P\"] = 7, [\"R\"] = 9, [\"S\"] = 2, [\"T\"] = 3, [\"U\"] = 4, [\"V\"] = 5, [\"W\"] = 6, [\"X\"] = 7, [\"Y\"] = 8, [\"Z\"] = 9 }\n\n    local i = 1\n    while i < length + 1 do\n        if i ~= 9 then\n            local ch = string.sub(str, i, i)\n            local digit = tonumber(ch)\n            if digit then\n                checksum = checksum + digit * weights[i]\n            else\n                for k, v in pairs(map_ch) do\n                    if k == ch then\n                        checksum = v * weights[i] + checksum\n                    end\n                end\n            end\n        end\n        i = i + 1\n    end\n\n    checksum = checksum % 11\n    if ch_check == 'X' and checksum == 10 then\n        return 1\n    end\n\n    if tonumber(ch_check) == checksum then\n        return 1\n    end\n    -- failed\n    return 0\nend", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_unionpay", "builtInName": "中华人民共和国网络安全法-公民个人信息类[银联卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"62\", \"60\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_boc", "builtInName": "中华人民共和国网络安全法-公民个人信息类[中国银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"621294\",\"621293\",\"621342\",\"621343\",\"621394\",\"621364\",\"621648\",\"621248\",\"621249\",\"621638\",\"621334\",\"621395\",\"524865\",\"525745\",\"525746\",\"524864\",\"622759\",\"622761\",\"622762\",\"622763\",\"622752\",\"622753\",\"622755\",\"622757\",\"622758\",\"622756\",\"622754\",\"512315\",\"512316\",\"512411\",\"512412\",\"514957\",\"558868\",\"514958\",\"518378\",\"518379\",\"518474\",\"518475\",\"518476\",\"547766\",\"620026\",\"620025\",\"620531\",\"620019\",\"620035\",\"409672\",\"552742\",\"553131\",\"622771\",\"622770\",\"622772\",\"409668\",\"409669\",\"409667\",\"625141\",\"625143\",\"620211\",\"620210\",\"621661\",\"409666\",\"621787\",\"621785\",\"622751\",\"622750\",\"621756\",\"621757\",\"621330\",\"621331\",\"621758\",\"621759\",\"621332\",\"621333\",\"621663\",\"409665\",\"456351\",\"601382\",\"620202\",\"620203\",\"625908\",\"625907\",\"625909\",\"625906\",\"625910\",\"625905\",\"625040\",\"625042\",\"622789\",\"622788\",\"622480\",\"621212\",\"620514\",\"409670\",\"621662\",\"621297\",\"621741\",\"623040\",\"621788\",\"621786\",\"621790\",\"621789\",\"621215\",\"621725\",\"621666\",\"621668\",\"621667\",\"621669\",\"621660\",\"621672\",\"622346\",\"625055\",\"558869\",\"621231\",\"621620\",\"622347\",\"622479\",\"621256\",\"622274\",\"628388\",\"620061\",\"622760\",\"621041\",\"622273\",\"377677\",\"621665\",\"622765\",\"622764\",\"621568\",\"621569\",\"620513\",\"620040\",\"625140\",\"628313\",\"628312\",\"623208\",\"356833\",\"356835\",\"438088\",\"622348\",\"625333\",\"518377\",\"409671\",\"625145\",\"621283\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_icbc", "builtInName": "中华人民共和国网络安全法-公民个人信息类[工商银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"620302\",\"620402\",\"620403\",\"620404\",\"620406\",\"620407\",\"620409\",\"620410\",\"620411\",\"620412\",\"620502\",\"620503\",\"620405\",\"620408\",\"620512\",\"620602\",\"620604\",\"620607\",\"620611\",\"620612\",\"620704\",\"620706\",\"620707\",\"620708\",\"620709\",\"620710\",\"620609\",\"620712\",\"620713\",\"620714\",\"620802\",\"620711\",\"620904\",\"620905\",\"621001\",\"620902\",\"621103\",\"621105\",\"621106\",\"621107\",\"621102\",\"621203\",\"621204\",\"621205\",\"621206\",\"621207\",\"621208\",\"621209\",\"621210\",\"621302\",\"621303\",\"621202\",\"621305\",\"621306\",\"621307\",\"621309\",\"621311\",\"621313\",\"621211\",\"621315\",\"621404\",\"621405\",\"621406\",\"621407\",\"621408\",\"621409\",\"621410\",\"621502\",\"621317\",\"621511\",\"621602\",\"621603\",\"621604\",\"621605\",\"621608\",\"621609\",\"621610\",\"621611\",\"621612\",\"621613\",\"621614\",\"621615\",\"621616\",\"621617\",\"621607\",\"621606\",\"621804\",\"621807\",\"621813\",\"621814\",\"621817\",\"621901\",\"621904\",\"621905\",\"621906\",\"621907\",\"621908\",\"621909\",\"621910\",\"621911\",\"621912\",\"621913\",\"621915\",\"622002\",\"621903\",\"622004\",\"622005\",\"622006\",\"622007\",\"622008\",\"622010\",\"622011\",\"622012\",\"621914\",\"622015\",\"622016\",\"622003\",\"622018\",\"622019\",\"622020\",\"622102\",\"622103\",\"620200\",\"622013\",\"622111\",\"622114\",\"622017\",\"622110\",\"622303\",\"622304\",\"622305\",\"622306\",\"622307\",\"622308\",\"622309\",\"622314\",\"622315\",\"622317\",\"622302\",\"622402\",\"622403\",\"622404\",\"622313\",\"622504\",\"622505\",\"622509\",\"622513\",\"622517\",\"622502\",\"622604\",\"622605\",\"622606\",\"622510\",\"622703\",\"622715\",\"622806\",\"622902\",\"622903\",\"622706\",\"621304\",\"621402\",\"623008\",\"623011\",\"623012\",\"622904\",\"623015\",\"623100\",\"623202\",\"623301\",\"623400\",\"623500\",\"623602\",\"623803\",\"623901\",\"623014\",\"624100\",\"624200\",\"624301\",\"624402\",\"623700\",\"624000\",\"622104\",\"622105\",\"623002\",\"623006\",\"623062\",\"621376\",\"621423\",\"621428\",\"621434\",\"621761\",\"621749\",\"621300\",\"621378\",\"621379\",\"621720\",\"621762\",\"621240\",\"621724\",\"621371\",\"621414\",\"621375\",\"621734\",\"621433\",\"621370\",\"621733\",\"621732\",\"621764\",\"621372\",\"621765\",\"621369\",\"621750\",\"621377\",\"621367\",\"621374\",\"621781\",\"625929\",\"625927\",\"625930\",\"625114\",\"625021\",\"625022\",\"622159\",\"625932\",\"625931\",\"625113\",\"625914\",\"625928\",\"625986\",\"625925\",\"625921\",\"625926\",\"625917\",\"622158\",\"625922\",\"625933\",\"625920\",\"625924\",\"620054\",\"620142\",\"620114\",\"620146\",\"620143\",\"620149\",\"620187\",\"620124\",\"620183\",\"620094\",\"620186\",\"620046\",\"620148\",\"620185\",\"427018\",\"427020\",\"427029\",\"427030\",\"427039\",\"438125\",\"427062\",\"427064\",\"622202\",\"622203\",\"622926\",\"622927\",\"622928\",\"622210\",\"622211\",\"622212\",\"622213\",\"622214\",\"526836\",\"513685\",\"543098\",\"458441\",\"402791\",\"427028\",\"427038\",\"548259\",\"622200\",\"621722\",\"621723\",\"621721\",\"356879\",\"356880\",\"356881\",\"356882\",\"62451804\",\"62451810\",\"62451811\",\"62458071\",\"451804\",\"451810\",\"451811\",\"458071\",\"622231\",\"622232\",\"622233\",\"622234\",\"622929\",\"622930\",\"622931\",\"528856\",\"524374\",\"550213\",\"622237\",\"622239\",\"622238\",\"622223\",\"622229\",\"622224\",\"489734\",\"489735\",\"489736\",\"530970\",\"530990\",\"558360\",\"370249\",\"370246\",\"370248\",\"625330\",\"625331\",\"625332\",\"621558\",\"621559\",\"625916\",\"625915\",\"427010\",\"427019\",\"374738\",\"374739\",\"544210\",\"548943\",\"621225\",\"621226\",\"6245806\",\"6253098\",\"45806\",\"53098\",\"625939\",\"625987\",\"622944\",\"622949\",\"625900\",\"622889\",\"625019\",\"628286\",\"622235\",\"622230\",\"622245\",\"622240\",\"621730\",\"621288\",\"621731\",\"620030\",\"621763\",\"625934\",\"524091\",\"525498\",\"622236\",\"625018\",\"622208\",\"621227\",\"438126\",\"622206\",\"9558\",\"900010\",\"900000\",\"524047\",\"510529\",\"370267\",\"370247\",\"625017\",\"621670\",\"621618\",\"620058\",\"622171\",\"620516\",\"620086\",\"628288\",\"621281\",\"622246\",\"622215\",\"622225\",\"622220\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_ccb", "builtInName": "中华人民共和国网络安全法-公民个人信息类[建设银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"5453242\",\"5491031\",\"553242\",\"5544033\",\"524094\",\"526410\",\"53243\",\"436738\",\"558895\",\"552801\",\"622675\",\"622676\",\"622677\",\"621082\",\"621083\",\"434062\",\"552245\",\"621466\",\"622966\",\"621499\",\"622988\",\"628316\",\"628317\",\"625363\",\"625362\",\"53242\",\"489592\",\"491031\",\"453242\",\"621488\",\"621598\",\"621487\",\"621081\",\"589970\",\"621084\",\"434061\",\"421349\",\"625966\",\"625965\",\"625964\",\"356899\",\"356896\",\"356895\",\"436742\",\"622700\",\"436718\",\"531693\",\"532458\",\"436748\",\"532450\",\"436745\",\"436728\",\"622708\",\"622168\",\"622166\",\"621700\",\"557080\",\"544887\",\"559051\",\"628366\",\"628266\",\"622725\",\"622728\",\"622382\",\"622381\",\"621467\",\"621621\",\"620060\",\"622280\",\"621080\",\"620107\",\"621284\",\"544033\",\"622707\",\"625956\",\"625955\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_cmbchina", "builtInName": "中华人民共和国网络安全法-公民个人信息类[招商银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"356885\",\"356886\",\"356887\",\"356888\",\"356890\",\"439188\",\"479228\",\"479229\",\"356889\",\"552534\",\"552587\",\"622575\",\"622576\",\"622577\",\"622578\",\"622579\",\"622581\",\"622582\",\"545620\",\"545621\",\"545947\",\"545948\",\"545619\",\"545623\",\"410062\",\"468203\",\"512425\",\"524011\",\"622580\",\"622588\",\"95555\",\"439225\",\"439226\",\"625802\",\"625803\",\"690755\",\"518718\",\"518710\",\"439227\",\"620520\",\"622598\",\"622609\",\"621286\",\"402658\",\"370286\",\"370285\",\"370289\",\"370287\",\"521302\",\"621299\",\"621485\",\"621483\",\"628262\",\"628362\",\"621486\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_citic", "builtInName": "中华人民共和国网络安全法-公民个人信息类[中信银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"518212\",\"556617\",\"558916\",\"433666\",\"520108\",\"403393\",\"514906\",\"433669\",\"433668\",\"433667\",\"404157\",\"404174\",\"404173\",\"404172\",\"400360\",\"403391\",\"403392\",\"404158\",\"404159\",\"404171\",\"621773\",\"621767\",\"621768\",\"621770\",\"621772\",\"622689\",\"622688\",\"622680\",\"622679\",\"622678\",\"356392\",\"356391\",\"356390\",\"376968\",\"376969\",\"376966\",\"433670\",\"433671\",\"433680\",\"968807\",\"968808\",\"968809\",\"628208\",\"628209\",\"628206\",\"442729\",\"442730\",\"622690\",\"622691\",\"622998\",\"622999\",\"621771\",\"620082\",\"622692\",\"622698\",\"622696\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_ceb", "builtInName": "中华人民共和国网络安全法-公民个人信息类[光大银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"622663\",\"622664\",\"622665\",\"622666\",\"622667\",\"622669\",\"622670\",\"622671\",\"622672\",\"622668\",\"622661\",\"622674\",\"90030\",\"622673\",\"622660\",\"622662\",\"356837\",\"356838\",\"356839\",\"356840\",\"486497\",\"425862\",\"625978\",\"625980\",\"625979\",\"625981\",\"625977\",\"625976\",\"625975\",\"628201\",\"628202\",\"406254\",\"406252\",\"622655\",\"620518\",\"621489\",\"621492\",\"303\",\"481699\",\"524090\",\"543159\",\"620085\",\"622161\",\"622570\",\"622650\",\"622658\",\"622685\",\"622659\",\"622687\",\"622657\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_cmbc", "builtInName": "中华人民共和国网络安全法-公民个人信息类[民生银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"407405\",\"421869\",\"421870\",\"421871\",\"512466\",\"528948\",\"552288\",\"517636\",\"556610\",\"545392\",\"545393\",\"545431\",\"545447\",\"622617\",\"622622\",\"622615\",\"622619\",\"472067\",\"421393\",\"472068\",\"622600\",\"622601\",\"628258\",\"464580\",\"356858\",\"356857\",\"356856\",\"356859\",\"464581\",\"427571\",\"427570\",\"421865\",\"415599\",\"553161\",\"545217\",\"523952\",\"622602\",\"622621\",\"622616\",\"622603\",\"377155\",\"377153\",\"377152\",\"377158\",\"622620\",\"622623\",\"625913\",\"625912\",\"625911\",\"622618\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_cgbc", "builtInName": "中华人民共和国网络安全法-公民个人信息类[广发银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"436768\",\"436769\",\"436770\",\"491032\",\"491033\",\"491034\",\"491035\",\"491036\",\"491037\",\"491038\",\"436771\",\"518364\",\"541709\",\"541710\",\"548844\",\"493427\",\"520152\",\"520382\",\"552794\",\"528931\",\"685800\",\"558894\",\"406365\",\"487013\",\"406366\",\"6858000\",\"6858001\",\"6858009\",\"625072\",\"625809\",\"625071\",\"625808\",\"9111\",\"622568\",\"622558\",\"622555\",\"622556\",\"622557\",\"622559\",\"622560\",\"621462\",\"625810\",\"628260\",\"628259\",\"625807\",\"625806\",\"625805\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_bocom", "builtInName": "中华人民共和国网络安全法-公民个人信息类[交通银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"458123\",\"458124\",\"520169\",\"552853\",\"521899\",\"955593\",\"955592\",\"955591\",\"955590\",\"622258\",\"622259\",\"622261\",\"622260\",\"622250\",\"622251\",\"622253\",\"622252\",\"6653783\",\"49104\",\"53783\",\"6649104\",\"622254\",\"622255\",\"622256\",\"622257\",\"625029\",\"625028\",\"434910\",\"522964\",\"601428\",\"********\",\"622656\",\"620013\",\"620021\",\"621069\",\"628218\",\"628216\",\"622262\",\"620521\",\"621436\",\"622284\",\"********\",\"405512\",\"621335\",\"621002\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_pingan", "builtInName": "中华人民共和国网络安全法-公民个人信息类[平安银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"526855\",\"622156\",\"622155\",\"528020\",\"622157\",\"531659\",\"412962\",\"412963\",\"998802\",\"998801\",\"415753\",\"415752\",\"622989\",\"622986\",\"435745\",\"435744\",\"356869\",\"356868\",\"483536\",\"998800\",\"622538\",\"620010\",\"622536\",\"622539\",\"622535\",\"622983\",\"623058\",\"625361\",\"625360\",\"622525\",\"622526\",\"622298\",\"602907\",\"621626\",\"627068\",\"627067\",\"627066\",\"627069\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_cib", "builtInName": "中华人民共和国网络安全法-公民个人信息类[兴业银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"549633\",\"552398\",\"548738\",\"625087\",\"625086\",\"625085\",\"625082\",\"625083\",\"625084\",\"451290\",\"451289\",\"524070\",\"523036\",\"622902\",\"622901\",\"461982\",\"486494\",\"486493\",\"486861\",\"528057\",\"527414\",\"90592\",\"622909\",\"966666\",\"625962\",\"625961\",\"625960\",\"625963\",\"438589\",\"438588\",\"622908\",\"622922\",\"628212\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_abc", "builtInName": "中华人民共和国网络安全法-公民个人信息类[农业银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"95595\",\"95596\",\"95597\",\"95598\",\"95599\",\"622840\",\"622844\",\"622847\",\"622848\",\"622845\",\"622826\",\"622827\",\"622841\",\"103\",\"622824\",\"622825\",\"622823\",\"622846\",\"622843\",\"622849\",\"622821\",\"622822\",\"621671\",\"620501\",\"622828\",\"621619\",\"620059\",\"623018\",\"623206\",\"621282\",\"621336\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_hxb", "builtInName": "中华人民共和国网络安全法-公民个人信息类[华夏银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"621222\",\"623022\",\"622638\",\"625969\",\"625968\",\"625967\",\"622630\",\"999999\",\"539867\",\"539868\",\"528709\",\"528708\",\"628318\",\"623021\",\"622632\",\"623020\",\"523959\",\"622633\",\"622637\",\"622636\",\"622631\",\"623023\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_nbcb", "builtInName": "中华人民共和国网络安全法-公民个人信息类[宁波银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"622778\",\"622282\",\"625903\",\"628207\",\"512431\",\"520194\",\"622281\",\"621418\",\"622316\",\"622318\",\"940022\",\"621279\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_spdb", "builtInName": "中华人民共和国网络安全法-公民个人信息类[浦发银行卡]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"418152\",\"456418\",\"622521\",\"404738\",\"404739\",\"498451\",\"622517\",\"622518\",\"515672\",\"517650\",\"525998\",\"365850\",\"365851\",\"365852\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "bank_number_common", "builtInName": "中华人民共和国网络安全法-公民个人信息类[银行卡-全部（严格）]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:\\d{4} \\d{4} \\d{4} \\d{4} \\d{3}|\\d{6} \\d{4} \\d{4} \\d{5}|\\d{16}|\\d{17}|\\d{18}|\\d{19}|\\d{4} \\d{4} \\d{4} \\d{4})(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local prefixes = { \"621294\",\"621293\",\"621342\",\"621343\",\"621394\",\"621364\",\"621648\",\"621248\",\"621249\",\"621638\",\"621334\",\"621395\",\"524865\",\"525745\",\"525746\",\"524864\",\"622759\",\"622761\",\"622762\",\"622763\",\"622752\",\"622753\",\"622755\",\"622757\",\"622758\",\"622756\",\"622754\",\"512315\",\"512316\",\"512411\",\"512412\",\"514957\",\"558868\",\"514958\",\"518378\",\"518379\",\"518474\",\"518475\",\"518476\",\"547766\",\"620026\",\"620025\",\"620531\",\"620019\",\"620035\",\"409672\",\"552742\",\"553131\",\"622771\",\"622770\",\"622772\",\"409668\",\"409669\",\"409667\",\"625141\",\"625143\",\"620211\",\"620210\",\"621661\",\"409666\",\"621787\",\"621785\",\"622751\",\"622750\",\"621756\",\"621757\",\"621330\",\"621331\",\"621758\",\"621759\",\"621332\",\"621333\",\"621663\",\"409665\",\"456351\",\"601382\",\"620202\",\"620203\",\"625908\",\"625907\",\"625909\",\"625906\",\"625910\",\"625905\",\"625040\",\"625042\",\"622789\",\"622788\",\"622480\",\"621212\",\"620514\",\"409670\",\"621662\",\"621297\",\"621741\",\"623040\",\"621788\",\"621786\",\"621790\",\"621789\",\"621215\",\"621725\",\"621666\",\"621668\",\"621667\",\"621669\",\"621660\",\"621672\",\"622346\",\"625055\",\"558869\",\"621231\",\"621620\",\"622347\",\"622479\",\"621256\",\"622274\",\"628388\",\"620061\",\"622760\",\"621041\",\"622273\",\"377677\",\"621665\",\"622765\",\"622764\",\"621568\",\"621569\",\"620513\",\"620040\",\"625140\",\"628313\",\"628312\",\"623208\",\"356833\",\"356835\",\"438088\",\"622348\",\"625333\",\"518377\",\"409671\",\"625145\",\"621283\",\"620302\",\"620402\",\"620403\",\"620404\",\"620406\",\"620407\",\"620409\",\"620410\",\"620411\",\"620412\",\"620502\",\"620503\",\"620405\",\"620408\",\"620512\",\"620602\",\"620604\",\"620607\",\"620611\",\"620612\",\"620704\",\"620706\",\"620707\",\"620708\",\"620709\",\"620710\",\"620609\",\"620712\",\"620713\",\"620714\",\"620802\",\"620711\",\"620904\",\"620905\",\"621001\",\"620902\",\"621103\",\"621105\",\"621106\",\"621107\",\"621102\",\"621203\",\"621204\",\"621205\",\"621206\",\"621207\",\"621208\",\"621209\",\"621210\",\"621302\",\"621303\",\"621202\",\"621305\",\"621306\",\"621307\",\"621309\",\"621311\",\"621313\",\"621211\",\"621315\",\"621404\",\"621405\",\"621406\",\"621407\",\"621408\",\"621409\",\"621410\",\"621502\",\"621317\",\"621511\",\"621602\",\"621603\",\"621604\",\"621605\",\"621608\",\"621609\",\"621610\",\"621611\",\"621612\",\"621613\",\"621614\",\"621615\",\"621616\",\"621617\",\"621607\",\"621606\",\"621804\",\"621807\",\"621813\",\"621814\",\"621817\",\"621901\",\"621904\",\"621905\",\"621906\",\"621907\",\"621908\",\"621909\",\"621910\",\"621911\",\"621912\",\"621913\",\"621915\",\"622002\",\"621903\",\"622004\",\"622005\",\"622006\",\"622007\",\"622008\",\"622010\",\"622011\",\"622012\",\"621914\",\"622015\",\"622016\",\"622003\",\"622018\",\"622019\",\"622020\",\"622102\",\"622103\",\"620200\",\"622013\",\"622111\",\"622114\",\"622017\",\"622110\",\"622303\",\"622304\",\"622305\",\"622306\",\"622307\",\"622308\",\"622309\",\"622314\",\"622315\",\"622317\",\"622302\",\"622402\",\"622403\",\"622404\",\"622313\",\"622504\",\"622505\",\"622509\",\"622513\",\"622517\",\"622502\",\"622604\",\"622605\",\"622606\",\"622510\",\"622703\",\"622715\",\"622806\",\"622902\",\"622903\",\"622706\",\"621304\",\"621402\",\"623008\",\"623011\",\"623012\",\"622904\",\"623015\",\"623100\",\"623202\",\"623301\",\"623400\",\"623500\",\"623602\",\"623803\",\"623901\",\"623014\",\"624100\",\"624200\",\"624301\",\"624402\",\"623700\",\"624000\",\"622104\",\"622105\",\"623002\",\"623006\",\"623062\",\"621376\",\"621423\",\"621428\",\"621434\",\"621761\",\"621749\",\"621300\",\"621378\",\"621379\",\"621720\",\"621762\",\"621240\",\"621724\",\"621371\",\"621414\",\"621375\",\"621734\",\"621433\",\"621370\",\"621733\",\"621732\",\"621764\",\"621372\",\"621765\",\"621369\",\"621750\",\"621377\",\"621367\",\"621374\",\"621781\",\"625929\",\"625927\",\"625930\",\"625114\",\"625021\",\"625022\",\"622159\",\"625932\",\"625931\",\"625113\",\"625914\",\"625928\",\"625986\",\"625925\",\"625921\",\"625926\",\"625917\",\"622158\",\"625922\",\"625933\",\"625920\",\"625924\",\"620054\",\"620142\",\"620114\",\"620146\",\"620143\",\"620149\",\"620187\",\"620124\",\"620183\",\"620094\",\"620186\",\"620046\",\"620148\",\"620185\",\"427018\",\"427020\",\"427029\",\"427030\",\"427039\",\"438125\",\"427062\",\"427064\",\"622202\",\"622203\",\"622926\",\"622927\",\"622928\",\"622210\",\"622211\",\"622212\",\"622213\",\"622214\",\"526836\",\"513685\",\"543098\",\"458441\",\"402791\",\"427028\",\"427038\",\"548259\",\"622200\",\"621722\",\"621723\",\"621721\",\"356879\",\"356880\",\"356881\",\"356882\",\"62451804\",\"62451810\",\"62451811\",\"62458071\",\"451804\",\"451810\",\"451811\",\"458071\",\"622231\",\"622232\",\"622233\",\"622234\",\"622929\",\"622930\",\"622931\",\"528856\",\"524374\",\"550213\",\"622237\",\"622239\",\"622238\",\"622223\",\"622229\",\"622224\",\"489734\",\"489735\",\"489736\",\"530970\",\"530990\",\"558360\",\"370249\",\"370246\",\"370248\",\"625330\",\"625331\",\"625332\",\"621558\",\"621559\",\"625916\",\"625915\",\"427010\",\"427019\",\"374738\",\"374739\",\"544210\",\"548943\",\"621225\",\"621226\",\"6245806\",\"6253098\",\"45806\",\"53098\",\"625939\",\"625987\",\"622944\",\"622949\",\"625900\",\"622889\",\"625019\",\"628286\",\"622235\",\"622230\",\"622245\",\"622240\",\"621730\",\"621288\",\"621731\",\"620030\",\"621763\",\"625934\",\"524091\",\"525498\",\"622236\",\"625018\",\"622208\",\"621227\",\"438126\",\"622206\",\"9558\",\"900010\",\"900000\",\"524047\",\"510529\",\"370267\",\"370247\",\"625017\",\"621670\",\"621618\",\"620058\",\"622171\",\"620516\",\"620086\",\"628288\",\"621281\",\"622246\",\"622215\",\"622225\",\"622220\",\"5453242\",\"5491031\",\"553242\",\"5544033\",\"524094\",\"526410\",\"53243\",\"436738\",\"558895\",\"552801\",\"622675\",\"622676\",\"622677\",\"621082\",\"621083\",\"434062\",\"552245\",\"621466\",\"622966\",\"621499\",\"622988\",\"628316\",\"628317\",\"625363\",\"625362\",\"53242\",\"489592\",\"491031\",\"453242\",\"621488\",\"621598\",\"621487\",\"621081\",\"589970\",\"621084\",\"434061\",\"421349\",\"625966\",\"625965\",\"625964\",\"356899\",\"356896\",\"356895\",\"436742\",\"622700\",\"436718\",\"531693\",\"532458\",\"436748\",\"532450\",\"436745\",\"436728\",\"622708\",\"622168\",\"622166\",\"621700\",\"557080\",\"544887\",\"559051\",\"628366\",\"628266\",\"622725\",\"622728\",\"622382\",\"622381\",\"621467\",\"621621\",\"620060\",\"622280\",\"621080\",\"620107\",\"621284\",\"544033\",\"622707\",\"625956\",\"625955\",\"356885\",\"356886\",\"356887\",\"356888\",\"356890\",\"439188\",\"479228\",\"479229\",\"356889\",\"552534\",\"552587\",\"622575\",\"622576\",\"622577\",\"622578\",\"622579\",\"622581\",\"622582\",\"545620\",\"545621\",\"545947\",\"545948\",\"545619\",\"545623\",\"410062\",\"468203\",\"512425\",\"524011\",\"622580\",\"622588\",\"95555\",\"439225\",\"439226\",\"625802\",\"625803\",\"690755\",\"518718\",\"518710\",\"439227\",\"620520\",\"622598\",\"622609\",\"621286\",\"402658\",\"370286\",\"370285\",\"370289\",\"370287\",\"521302\",\"621299\",\"621485\",\"621483\",\"628262\",\"628362\",\"621486\",\"518212\",\"556617\",\"558916\",\"433666\",\"520108\",\"403393\",\"514906\",\"433669\",\"433668\",\"433667\",\"404157\",\"404174\",\"404173\",\"404172\",\"400360\",\"403391\",\"403392\",\"404158\",\"404159\",\"404171\",\"621773\",\"621767\",\"621768\",\"621770\",\"621772\",\"622689\",\"622688\",\"622680\",\"622679\",\"622678\",\"356392\",\"356391\",\"356390\",\"376968\",\"376969\",\"376966\",\"433670\",\"433671\",\"433680\",\"968807\",\"968808\",\"968809\",\"628208\",\"628209\",\"628206\",\"442729\",\"442730\",\"622690\",\"622691\",\"622998\",\"622999\",\"621771\",\"620082\",\"622692\",\"622698\",\"622696\",\"622663\",\"622664\",\"622665\",\"622666\",\"622667\",\"622669\",\"622670\",\"622671\",\"622672\",\"622668\",\"622661\",\"622674\",\"90030\",\"622673\",\"622660\",\"622662\",\"356837\",\"356838\",\"356839\",\"356840\",\"486497\",\"425862\",\"625978\",\"625980\",\"625979\",\"625981\",\"625977\",\"625976\",\"625975\",\"628201\",\"628202\",\"406254\",\"406252\",\"622655\",\"620518\",\"621489\",\"621492\",\"303\",\"481699\",\"524090\",\"543159\",\"620085\",\"622161\",\"622570\",\"622650\",\"622658\",\"622685\",\"622659\",\"622687\",\"622657\",\"407405\",\"421869\",\"421870\",\"421871\",\"512466\",\"528948\",\"552288\",\"517636\",\"556610\",\"545392\",\"545393\",\"545431\",\"545447\",\"622617\",\"622622\",\"622615\",\"622619\",\"472067\",\"421393\",\"472068\",\"622600\",\"622601\",\"628258\",\"464580\",\"356858\",\"356857\",\"356856\",\"356859\",\"464581\",\"427571\",\"427570\",\"421865\",\"415599\",\"553161\",\"545217\",\"523952\",\"622602\",\"622621\",\"622616\",\"622603\",\"377155\",\"377153\",\"377152\",\"377158\",\"622620\",\"622623\",\"625913\",\"625912\",\"625911\",\"622618\",\"436768\",\"436769\",\"436770\",\"491032\",\"491033\",\"491034\",\"491035\",\"491036\",\"491037\",\"491038\",\"436771\",\"518364\",\"541709\",\"541710\",\"548844\",\"493427\",\"520152\",\"520382\",\"552794\",\"528931\",\"685800\",\"558894\",\"406365\",\"487013\",\"406366\",\"6858000\",\"6858001\",\"6858009\",\"625072\",\"625809\",\"625071\",\"625808\",\"9111\",\"622568\",\"622558\",\"622555\",\"622556\",\"622557\",\"622559\",\"622560\",\"621462\",\"625810\",\"628260\",\"628259\",\"625807\",\"625806\",\"625805\",\"458123\",\"458124\",\"520169\",\"552853\",\"521899\",\"955593\",\"955592\",\"955591\",\"955590\",\"622258\",\"622259\",\"622261\",\"622260\",\"622250\",\"622251\",\"622253\",\"622252\",\"6653783\",\"49104\",\"53783\",\"6649104\",\"622254\",\"622255\",\"622256\",\"622257\",\"625029\",\"625028\",\"434910\",\"522964\",\"601428\",\"********\",\"622656\",\"620013\",\"620021\",\"621069\",\"628218\",\"628216\",\"622262\",\"620521\",\"621436\",\"622284\",\"********\",\"405512\",\"621335\",\"621002\",\"526855\",\"622156\",\"622155\",\"528020\",\"622157\",\"531659\",\"412962\",\"412963\",\"998802\",\"998801\",\"415753\",\"415752\",\"622989\",\"622986\",\"435745\",\"435744\",\"356869\",\"356868\",\"483536\",\"998800\",\"622538\",\"620010\",\"622536\",\"622539\",\"622535\",\"622983\",\"623058\",\"625361\",\"625360\",\"622525\",\"622526\",\"622298\",\"602907\",\"621626\",\"627068\",\"627067\",\"627066\",\"627069\",\"549633\",\"552398\",\"548738\",\"625087\",\"625086\",\"625085\",\"625082\",\"625083\",\"625084\",\"451290\",\"451289\",\"524070\",\"523036\",\"622901\",\"461982\",\"486494\",\"486493\",\"486861\",\"528057\",\"527414\",\"90592\",\"622909\",\"966666\",\"625962\",\"625961\",\"625960\",\"625963\",\"438589\",\"438588\",\"622908\",\"622922\",\"628212\",\"95595\",\"95596\",\"95597\",\"95598\",\"95599\",\"622840\",\"622844\",\"622847\",\"622848\",\"622845\",\"622826\",\"622827\",\"622841\",\"103\",\"622824\",\"622825\",\"622823\",\"622846\",\"622843\",\"622849\",\"622821\",\"622822\",\"621671\",\"620501\",\"622828\",\"621619\",\"620059\",\"623018\",\"623206\",\"621282\",\"621336\",\"621222\",\"623022\",\"622638\",\"625969\",\"625968\",\"625967\",\"622630\",\"999999\",\"539867\",\"539868\",\"528709\",\"528708\",\"628318\",\"623021\",\"622632\",\"623020\",\"523959\",\"622633\",\"622637\",\"622636\",\"622631\",\"623023\",\"622778\",\"622282\",\"625903\",\"628207\",\"512431\",\"520194\",\"622281\",\"621418\",\"622316\",\"622318\",\"940022\",\"621279\",\"418152\",\"456418\",\"622521\",\"404738\",\"404739\",\"498451\",\"622518\",\"515672\",\"517650\",\"525998\",\"365850\",\"365851\",\"365852\" }\n    return checkBankNumber(str, prefixes, 16, 19)\nend\n", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "TYSHXYDM", "builtInName": "其他[统一社会信用代码]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w|\\d\\.)(?:[1|5|9][1|2|3|9]|[2-46-8A-GY]1)(?:10|11|12|13|14|15|21|22|23|31|32|33|34|35|36|37|41|42|43|44|45|46|50|51|52|53|54|61|62|63|64|65)[0-9A-HJ-NP-RTUWXY]{14}(?!\\w|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    str = string.upper(str)\n    -- check length\n    local length = string.len(str)\n    if 18 ~= length then\n        return 0\n    end\n\n    local checksum = 0;\n    local weights = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 }\n    local checksums = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'T', 'U', 'W', 'X', 'Y' }\n\n    local i = 1\n    while i < length do\n        local ch = string.sub(str, i, i)\n        for k, v in ipairs(checksums) do\n            if ch == v then\n                checksum = (k - 1) * weights[i] + checksum\n            end\n        end\n        i = i + 1\n    end\n\n    checksum = 31 - checksum % 31\n    if checksum == 31 then\n        checksum = 0\n    end\n\n    if checksums[checksum + 1] ~= string.sub(str, length) then\n        return 0\n    end\n\n    -- success\n    return 1\nend", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "id_card_full", "builtInName": "中华人民共和国网络安全法-公民个人信息类[中国大陆居民身份证号]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\d|\\d\\.)(?:(?:[16][1-5]|21|22|23|3[1-7]|4[1-6]|5[0-4])\\d{4})(?:(?:[1|2]\\d{3}(?:0[1-9]|1[012])(?:0[1-9]|[12]\\d|3[01])\\d{3}[xX\\d])|(?:\\d{2}(?:0[1-9]|1[012])(?:0[1-9]|[12]\\d|3[01])\\d{3}))(?!\\d|\\.\\d)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    str = string.upper(str)\n    -- check length\n    local length = string.len(str)\n    if 18 ~= length then\n        return 0\n    end\n\n    -- check province\n    local province = string.sub(str, 1, 6)\n    --print( province )\n\n    -- check birthday\n    local birthday = string.sub(str, 7, 14)\n    local y = tonumber(string.sub(birthday, 1, 4))\n    local m = tonumber(string.sub(birthday, 5, 6))\n    local d = tonumber(string.sub(birthday, 7, 8))\n    if (y < 1900 or y > 2100 or m < 1 or m > 12 or d < 1 or d > 32) then\n        return 0\n    end\n\n    checksum = 0;\n    local weights = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 }\n\n    local i = 1\n    while i < length do\n        local digit = tonumber(string.sub(str, i, i)) * weights[i]\n        checksum = checksum + digit\n        i = i + 1\n    end\n\n    checksum = checksum % 11\n\n    local checksums = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' }\n    if checksums[checksum + 1] ~= string.sub(str, length) then\n        return 0\n    end\n\n    -- success\n    return 1\nend", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}, {"id": null, "createdAt": null, "updatedAt": null, "bundle": {}, "builtInCode": "car_license_plate_num", "builtInName": "其他[普通车牌号]", "conditionType": "Content", "contentCondition": "Regex", "attributeCondition": null, "attributeEQ": null, "attributeIN": [], "attributeLT": null, "attributeGT": null, "charCase": false, "fuzzyMatch": true, "matchMin": null, "matchMax": null, "matchRepeat": true, "keywordType": null, "keywordInPosGT": null, "keywordInPosLT": null, "keywordMaxSpace": null, "keywords": [], "regexs": ["(?<!\\w)[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}\\s*[A-Z]{1}\\s*[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}(?!\\w)"], "regexCheckLuaScript": "function logDebug(message)\n    -- Get the DEBUG_MODE environment variable\n    local debugMode = os.getenv(\"LUA_DEBUG\")\n    -- Check if DEBUG_MODE is set to \"true\"\n    if debugMode == \"true\" then\n        print(message)\n    end\nend\n-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行\n-- base_start\nfunction split(istr, sep)\n    if sep == nil then\n        sep = \",%s*\"\n    end\n    local t = {}\n    local pattern = \"([^\" .. sep .. \"]+)\"\n    for str in string.gmatch(istr, pattern) do\n        -- 检查 str 是否为空字符串，确保非空才插入结果表\n        if str ~= \"\" then\n            table.insert(t, str)\n        end\n    end\n    return t\nend\n\nfunction startsWith(cardNumber, prefixes)\n    for _, prefix in ipairs(prefixes) do\n        -- Check if the cardNumber starts with the current prefix\n        if cardNumber:sub(1, #prefix) == prefix then\n            logDebug(\"card number prefix found \" .. prefix)\n            return true\n        end\n    end\n    return false\nend\n\nfunction luhnCheck(cardNumber)\n    local sum = 0\n    local shouldDouble = false\n    -- 从右到左遍历字符串\n    for i = #cardNumber, 1, -1 do\n        -- 获取当前位字符，并转换为数字\n        local digit = tonumber(cardNumber:sub(i, i))\n        if not digit then\n            logDebug(\"card number has not digit \" .. digit)\n            return false\n        end\n        if shouldDouble then\n            digit = digit * 2\n            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加\n            if digit > 9 then\n                digit = digit - 9\n            end\n        end\n\n        sum = sum + digit\n        shouldDouble = not shouldDouble\n    end\n    -- Luhn算法的规则是总和必须能被10整除\n    logDebug(\"card number luhn sum \" .. sum)\n    return sum % 10 == 0\nend\n\nfunction checkBankNumber(str, prefixes, minLen, maxLen)\n    -- trim space\n    str = string.gsub(str, \"[^%d]\", \"\")\n    -- check length\n    local length = string.len(str)\n    if length < minLen or length > maxLen then\n        logDebug(\"card number length is \" .. length)\n        return -1\n    end\n    -- check prefix\n    if startsWith(str, prefixes) == false then\n        logDebug(\"card number prefix miss\")\n        return -2\n    end\n    -- check luhn checksum\n    if luhnCheck(str) == false then\n        logDebug(\"card number luhn check fail\")\n        return -3\n    end\n    -- success\n    return 1\nend\n\nversion = 1\nfunction check(str)\n    local length = string.len(str)\n    local i = 1\n    local count = 0\n\n    while i <= length do\n        local digit = tonumber(string.sub(str, i, i))\n        i = i + 1\n        if digit then\n            count = count + 1\n        end\n\n        if count > 2 then\n            return 1\n        end\n    end\n    -- failed\n    return 0\nend", "dicts": [], "clientInsideFunction": null, "fuzzyHashs": [], "machineLearnClassify": null}]}
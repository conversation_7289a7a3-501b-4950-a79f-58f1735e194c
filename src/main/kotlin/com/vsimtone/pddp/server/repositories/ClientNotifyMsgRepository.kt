package com.vsimtone.pddp.server.repositories

import com.vsimtone.pddp.server.entities.ClientDevice
import com.vsimtone.pddp.server.entities.ClientNotifyMsg
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ClientNotifyMsgRepository : BaseRepository<ClientNotifyMsg> {

    @Query("update ClientNotifyMsg set notified = true where device = ?1 and msgIdx <= ?2 and notified = false")
    @Modifying()
    fun setNotified(device: ClientDevice, msgId: Long): Int
}

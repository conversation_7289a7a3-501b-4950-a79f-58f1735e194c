package com.vsimtone.pddp.server.consts

class LicensePubkey {

    // 使用命令生成 cat public_key.data | gzip | xxd -i -C
    companion object {
        val KEY = arrayOf(
            0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x0d, 0xc9,
            0x49, 0x96, 0x43, 0x40, 0x00, 0x00, 0xd0, 0x03, 0x59, 0x84, 0x98, 0xca,
            0xa2, 0x17, 0x65, 0x88, 0x29, 0xa8, 0x04, 0x31, 0xec, 0x22, 0x4a, 0x8c,
            0x15, 0x34, 0x25, 0x9c, 0xbe, 0x7b, 0xf7, 0xdf, 0xfb, 0x9e, 0x6d, 0x6b,
            0x76, 0x0b, 0x7d, 0xf5, 0xdd, 0x4d, 0x75, 0xd7, 0x98, 0xca, 0xc6, 0xaa,
            0xf0, 0x66, 0x5c, 0x20, 0x0c, 0x34, 0xf8, 0x06, 0xd0, 0xfb, 0x7f, 0xed,
            0xed, 0xfe, 0xdb, 0x80, 0x62, 0x72, 0xfc, 0xfa, 0xdf, 0xb7, 0x3f, 0x80,
            0xfd, 0xeb, 0xac, 0xf5, 0x77, 0x9c, 0xc1, 0x7c, 0x02, 0xeb, 0x6d, 0xc9,
            0xb0, 0xa6, 0xea, 0x81, 0x29, 0x85, 0x9c, 0x91, 0x41, 0x0b, 0xfb, 0x67,
            0x4f, 0x2c, 0xc4, 0x5d, 0xad, 0x04, 0xfc, 0x94, 0x3f, 0x26, 0xa1, 0x1c,
            0xf7, 0x95, 0xdd, 0x3d, 0x0b, 0xb7, 0xa5, 0x47, 0x5c, 0x8c, 0xe6, 0x23,
            0xff, 0x72, 0x8e, 0x23, 0xeb, 0x65, 0xb1, 0x8d, 0x1f, 0xcf, 0x11, 0xf7,
            0x69, 0x53, 0xa4, 0x1c, 0x39, 0x01, 0x71, 0x05, 0x5e, 0xdc, 0xb2, 0x7d,
            0xa6, 0xee, 0xfa, 0xe0, 0x53, 0x53, 0xd4, 0x13, 0x02, 0xbb, 0xfa, 0x78,
            0x00, 0xab, 0x46, 0x20, 0xc7, 0xca, 0x51, 0x52, 0xce, 0x54, 0xef, 0x0f,
            0x6c, 0x2f, 0x4f, 0x36, 0x27, 0x7d, 0xf2, 0x29, 0x3e, 0x26, 0x66, 0x0a,
            0x6c, 0x9d, 0x5a, 0xa5, 0x42, 0x85, 0xc7, 0xa8, 0xc6, 0x97, 0x4e, 0x20,
            0xab, 0x06, 0x64, 0x5c, 0x84, 0x49, 0xed, 0x1a, 0x85, 0x22, 0xcf, 0x09,
            0x96, 0x8a, 0x47, 0x39, 0x78, 0x4d, 0x42, 0xab, 0xfb, 0x5a, 0x42, 0x5b,
            0x85, 0x5f, 0x50, 0x44, 0x98, 0x4e, 0x97, 0xca, 0xa4, 0xec, 0x3e, 0x75,
            0x50, 0xcf, 0x15, 0x67, 0x9d, 0xce, 0x64, 0xd1, 0x1d, 0x7a, 0xcf, 0x7e,
            0x9f, 0xdc, 0xaa, 0x1a, 0xbf, 0x21, 0x8f, 0xa6, 0xc8, 0xbc, 0x8e, 0x2f,
            0x21, 0xf2, 0xc7, 0x78, 0xb3, 0xfd, 0x7e, 0xb6, 0xf7, 0x3c, 0x7e, 0x3d,
            0x3c, 0x81, 0x07, 0xba, 0x95, 0xf2, 0x0b, 0x28, 0xc4, 0xef, 0x9d, 0x30,
            0x46, 0x74, 0xd3, 0x0a, 0xf7, 0xe5, 0xa9, 0xee, 0x4b, 0xe3, 0x5b, 0x61,
            0x74, 0x53, 0xac, 0x0e, 0xa5, 0xde, 0xea, 0xb6, 0x6b, 0x29, 0x62, 0x1b,
            0xc1, 0x04, 0xa3, 0x28, 0x05, 0x6c, 0xb2, 0x4c, 0x2c, 0x0b, 0x2b, 0x67,
            0x9a, 0x5b, 0x3f, 0xec, 0x79, 0xb1, 0xa7, 0xb6, 0x19, 0x92, 0x36, 0xe7,
            0x46, 0x21, 0x53, 0x8a, 0xe0, 0x0d, 0xfc, 0x6d, 0x46, 0xb0, 0x19, 0xca,
            0xb3, 0x38, 0x61, 0xd3, 0xd6, 0xf2, 0x2b, 0xe1, 0xd9, 0x27, 0x54, 0x25,
            0x2f, 0x36, 0x9d, 0xd0, 0x4a, 0x9e, 0x9e, 0x7e, 0x25, 0x0e, 0x53, 0xce,
            0x4a, 0xb3, 0x1e, 0x16, 0x77, 0xc1, 0x79, 0x75, 0x34, 0x26, 0x99, 0x61,
            0x12, 0x07, 0x03, 0x03, 0x67, 0x82, 0xb5, 0x23, 0x1c, 0xb0, 0x7e, 0x0e,
            0x1b, 0x5f, 0x6c, 0xcd, 0x4c, 0x43, 0x3e, 0xbe, 0x6b, 0x31, 0xa4, 0x5e,
            0xcf, 0x0d, 0xf4, 0x2e, 0x86, 0xee, 0x7c, 0x34, 0x08, 0xd8, 0xe5, 0x98,
            0xe9, 0x4c, 0x3a, 0x3b, 0x72, 0x5a, 0xad, 0x49, 0xf7, 0x58, 0x19, 0x91,
            0xa9, 0xb0, 0x8a, 0x46, 0xaf, 0x62, 0x4e, 0x67, 0x04, 0x53, 0xc2, 0x62,
            0xbf, 0xaa, 0x38, 0xbb, 0x65, 0xa2, 0x3c, 0x58, 0xa5, 0xf8, 0xa9, 0xcb,
            0x6a, 0xe1, 0x76, 0x8b, 0x21, 0x9d, 0xa7, 0x84, 0xab, 0x4d, 0x66, 0xa2,
            0xa3, 0x54, 0x7b, 0xfc, 0xce, 0x61, 0xa6, 0x03, 0x02, 0x5d, 0x49, 0x86,
            0xdb, 0x72, 0xb4, 0x30, 0x9a, 0x02, 0x0a, 0x22, 0x7d, 0xeb, 0xef, 0x46,
            0x1b, 0x34, 0x5d, 0xa1, 0xfa, 0xec, 0x5e, 0xae, 0x54, 0x1b, 0x0b, 0xb9,
            0x0f, 0xa3, 0xf9, 0xfb, 0xf9, 0xc0, 0x5a, 0xa4, 0xcc, 0x81, 0x73, 0xc3,
            0xc7, 0xe5, 0xbd, 0x71, 0xc7, 0xc1, 0x25, 0x06, 0x01, 0xe9, 0x61, 0x5f,
            0x1f, 0x34, 0x72, 0xdb, 0xde, 0x5b, 0xe8, 0x7c, 0x66, 0x79, 0x54, 0x14,
            0xd0, 0x01, 0xaf, 0x24, 0x1c, 0xab, 0x48, 0x9e, 0x2e, 0x24, 0xbe, 0x3b,
            0x3a, 0xff, 0x7a, 0x8c, 0xa2, 0x51, 0xb7, 0x32, 0x63, 0x5f, 0xad, 0x57,
            0x27, 0x6d, 0xcf, 0x1b, 0x6d, 0xcc, 0x40, 0x1f, 0x67, 0x7f, 0x68, 0xca,
            0x20, 0x19, 0xeb, 0xdb, 0xc4, 0x9d, 0x9c, 0x8b, 0xd3, 0x69, 0x70, 0x33,
            0x20, 0xbc, 0xfd, 0xfc, 0xfc, 0x01, 0xba, 0x32, 0xf7, 0x68, 0xe0, 0x02,
            0x00, 0x00
        )
    }
}
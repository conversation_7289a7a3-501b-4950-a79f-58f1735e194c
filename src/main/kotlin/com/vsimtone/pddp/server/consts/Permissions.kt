package com.vsimtone.pddp.server.consts

object Permissions {

    // 用户权限
    const val USER_VIEW = "ROLE_USER_VIEW";
    const val USER_EDIT = "ROLE_USER_EDIT";
    const val USER_SET_MANAGER = "ROLE_USER_SET_MANAGER";

    // 机构权限
    const val ORG_VIEW = "ROLE_ORG_VIEW";
    const val ORG_EDIT = "ROLE_ORG_EDIT";

    // 文件权限
    const val FILE_VIEW = "ROLE_FILE_VIEW";
    const val FILE_EDIT = "ROLE_FILE_EDIT";

    // 分类权限
    const val DATA_CLASSIFY_VIEW = "ROLE_DATA_CLASSIFY_VIEW";
    const val DATA_CLASSIFY_EDIT = "ROLE_DATA_CLASSIFY_EDIT";
    const val MACHINE_LEARN_VIEW = "ROLE_MACHINE_LEARN_VIEW";
    const val MACHINE_LEARN_EDIT = "ROLE_MACHINE_LEARN_EDIT";

    // 分级权限
    const val DATA_LEVEL_VIEW = "ROLE_DATA_LEVEL_VIEW";
    const val DATA_LEVEL_EDIT = "ROLE_DATA_LEVEL_EDIT";

    // 终端权限
    const val CLIENT_VIEW = "ROLE_CLIENT_VIEW";
    const val CLIENT_EDIT = "ROLE_CLIENT_EDIT";
    const val CLIENT_RELEASE_DOWN_CONF = "ROLE_CLIENT_RELEASE_DOWN_CONF";
    const val CLIENT_UPGRADE_VIEW = "ROLE_CLIENT_UPGRADE_VIEW";
    const val CLIENT_UPGRADE_EDIT = "ROLE_CLIENT_UPGRADE_EDIT";

    // 策略权限
    const val POLICY_VIEW = "ROLE_POLICY_VIEW";
    const val POLICY_EDIT = "ROLE_POLICY_EDIT";

    // LDAP权限
    const val LDAP_VIEW = "ROLE_LDAP_VIEW";
    const val LDAP_EDIT = "ROLE_LDAP_EDIT";

    // 代理节点权限
    const val PROXY_NODE_VIEW = "ROLE_PROXY_NODE_VIEW";
    const val PROXY_NODE_EDIT = "ROLE_PROXY_NODE_EDIT";

    // 目录管理权限
    const val USER_FOLDER_VIEW="ROLE_USER_FOLDER_VIEW"
    const val USER_FOLDER_EDIT="ROLE_USER_FOLDER_EDIT"

    // 文件管理权限
    const val USER_FILE_VIEW="ROLE_USER_FILE_VIEW"
    const val USER_FILE_EDIT="ROLE_USER_FILE_EDIT"

    // 系统配置编辑
    const val APP_CONFIG_EDIT = "ROLE_APP_CONFIG_EDIT";

    // 通用数据集
    const val COMMON_DATASET_VIEW = "ROLE_COMMON_DATASET_VIEW";
    const val COMMON_DATASET_EDIT = "ROLE_COMMON_DATASET_EDIT";

    // 操作日志查看
    const val OPT_LOG_VIEW = "ROLE_OPT_LOG_VIEW";


    // 事件查看
    const val EVENT_VIEW = "ROLE_EVENT_VIEW";
    const val EVENT_EDIT = "ROLE_EVENT_EDIT";
    const val EVENT_APPROVE = "ROLE_EVENT_APPROVE";
    const val EVENT_FILE_DOWNLOAD = "ROLE_EVENT_FILE_DOWNLOAD";
    const val EVENT_DELETE = "ROLE_EVENT_DELETE";

    // 终端扫描
    const val CLIENT_DATA_SCAN_VIEW = "ROLE_CLIENT_DATA_SCAN_VIEW";
    const val CLIENT_DATA_SCAN_EDIT = "ROLE_CLIENT_DATA_SCAN_EDIT";

    // 系统监控
    const val SYS_STATS = "ROLE_SYS_STATS";

    // 风险事件概况
    const val RISK_EVENT_SUMMARY = "ROLE_RISK_EVENT_SUMMARY";

    // 用户行为分析
    const val RISK_USER_ACTION_ANALYZE = "ROLE_RISK_USER_ACTION_ANALYZE";

    // 数据资产概况
    const val DATA_ASSET_SUMMARY = "ROLE_DATA_ASSET_SUMMARY";


    // 授权
    const val APP_LICENSE_VIEW = "ROLE_APP_LICENSE_VIEW";
    const val APP_LICENSE_EDIT = "ROLE_APP_LICENSE_EDIT";

    fun getAll(): List<String> {
        val data = mutableListOf<String>()
        Permissions.javaClass.fields.forEach {
            val v = it.get(Permissions).toString()
            if (v.startsWith("ROLE_")) {
                data.add(v)
            }
        }
        return data;
    }
}
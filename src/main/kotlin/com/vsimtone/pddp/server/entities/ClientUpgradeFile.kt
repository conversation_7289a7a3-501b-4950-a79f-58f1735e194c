package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonListConverter
import jakarta.persistence.*
import org.hibernate.annotations.DynamicUpdate


@Entity
@Table(
    indexes = []
)
@Config(defaultVariableName = "a")
@DynamicUpdate
class ClientUpgradeFile : BaseEntity() {

    var enabled = false;

    lateinit var sysType: ClientDevice.SysType

    lateinit var sysArch: ClientDevice.SysArch

    @Convert(converter = JsonListConverter::class)
    @Column(length = 1024)
    var matchSysVersions = mutableListOf<String>();

    @Convert(converter = JsonListConverter::class)
    @Column(length = 1024)
    var matchClientVersions = mutableListOf<String>();

    // 是否允许匿名下载
    var anonymousDownloadable = false;

    // 允许降级
    var allowedDowngrade = false;

    lateinit var upgradeFileToken: String

    lateinit var clientVersion: String

    var clientVersionNumber: Long = 0

    @Column(length = 1024)
    var upgradeInfo: String? = null

    @Column(length = 1024)
    var packageInfo: String? = null

    var upgradeRateInMinute = 0

    //    最大推送次数
    var maxPushCount = 0;

    @Convert(converter = JsonListConverter::class)
    @Column(length = SIZE_16MB)
    var orgs = mutableListOf<String>()

    var totalDeviceCount = 0L

    var pushedDeviceCount = 0L

    var successDeviceCount = 0L

    var upgradeFileReady = false;
    override fun toString(): String {
        return "ClientUpgradeFile(id=${id}, sysType=$sysType, sysArch=$sysArch, clientVersion='$clientVersion', allowedDowngrade=${allowedDowngrade})"
    }

}

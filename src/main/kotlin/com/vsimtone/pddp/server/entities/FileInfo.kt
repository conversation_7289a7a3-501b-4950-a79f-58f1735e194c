package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.entities.FileKeyIndex
import com.vsimtone.pddp.server.json.JsonViews
import jakarta.persistence.*

@Entity
@FileKeyIndex
@Config(defaultVariableName = "a")
class FileInfo : FileKey() {

    // 文件名称
    @Column(length = 512)
    var fileName: String = ""

    // 文件类型
    var mimeType: String = ""

    // 文件大小
    var fileSize: Long = 0

    // 文件路径
    @Column(length = 512)
    @JsonIgnore
    var storePath: String = ""

    var partsCount: Int = 0

    override fun toString(): String {
        return "FileInfo(id='$id', name='$fileName', type='$mimeType', size=$fileSize, b3sum='$fileB3Sum', partsCount=$partsCount)"
    }

}

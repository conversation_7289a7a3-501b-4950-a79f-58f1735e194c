package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.ManyToOne

@Entity
@Config(defaultVariableName = "a")
class UserShare : BaseEntity() {

    companion object {
        enum class ShareToType { User, Org }
        enum class ShareTargetType { Folder, File }

    }


    @Enumerated(EnumType.STRING)
    lateinit var shareToType: ShareToType

    @Enumerated(EnumType.STRING)
    lateinit var shareTargetType: ShareTargetType

    @ManyToOne(fetch = FetchType.LAZY)
    lateinit var fromUser: User

    @ManyToOne(fetch = FetchType.LAZY)
    var toUser: User? = null

    @ManyToOne(fetch = FetchType.LAZY)
    var toOrg: Organization? = null

    @ManyToOne(fetch = FetchType.LAZY)
    var userFile: UserFile? = null

    @ManyToOne(fetch = FetchType.LAZY)
    var userFolder: UserFolder? = null


}

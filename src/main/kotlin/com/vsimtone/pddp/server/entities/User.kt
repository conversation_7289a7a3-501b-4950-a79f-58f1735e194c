package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.json.JsonViews
import org.hibernate.annotations.DynamicUpdate
import jakarta.persistence.*

@Entity
@Table(
    indexes = [
        Index(name = "i_username", columnList = "username", unique = true),
        Index(name = "i_manager", columnList = "manager", unique = false),
    ]
)
@Config(defaultVariableName = "a")
@DynamicUpdate
class User : BaseEntity() {

    enum class Type { Local, Ldap }

    // 用户类型
    @Enumerated(EnumType.STRING)
    @JsonView(JsonViews.List::class)
    var type = Type.Local

    // 用户是否启用
    @JsonView(JsonViews.List::class)
    var enabled: Boolean = false;

    // 昵称
    var nickname = "";

    // 用户账户
    var username = "";

    // 邮箱
    var email: String? = null;

    // 所属机构
    @ManyToOne
    @JsonView(JsonViews.SpecList::class)
    var org = Organization();

    // 密码
    @JsonView(JsonViews.Exclude::class)
    var password: String? = null;

    // 登录失败次数
    @JsonView(JsonViews.SpecList::class)
    var loginFailCount: Int = 0;

    // 用户已锁定
    @JsonView(JsonViews.List::class)
    var locked: Boolean = false;

    // 是否是管理员
    @JsonView(JsonViews.List::class)
    var manager: Boolean = false;

    // 管理员角色
    @JsonView(JsonViews.SpecList::class)
    @ManyToMany(cascade = arrayOf(CascadeType.REFRESH), targetEntity = SysRole::class)
    @JoinTable(
        name = "user_manage_role",
        joinColumns = arrayOf(JoinColumn(name = "user_id", referencedColumnName = "id")),
        inverseJoinColumns = arrayOf(JoinColumn(name = "role_id", referencedColumnName = "id"))
    )
    var manageRoles = mutableListOf<SysRole>();

    // 管理机构
    @JsonView(JsonViews.SpecList::class)
    @ManyToMany(cascade = arrayOf(CascadeType.REFRESH), targetEntity = Organization::class)
    @JoinTable(
        name = "user_manage_org",
        joinColumns = arrayOf(JoinColumn(name = "user_id", referencedColumnName = "id")),
        inverseJoinColumns = arrayOf(JoinColumn(name = "org_id", referencedColumnName = "id"))
    )
    var manageOrgs = mutableListOf<Organization>();

    override fun toString(): String {
        return "User(id='$id', type=$type, nickname='$nickname', username='$username', org=$org, manager=$manager)"
    }


}
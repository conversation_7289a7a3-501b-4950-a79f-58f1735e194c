package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.querydsl.core.annotations.Config
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table

@Entity
@Table()
@Config(defaultVariableName = "a")
class LdapSyncRecord : BaseEntity() {
    @JsonIgnore
    @ManyToOne
    lateinit var config: LdapConfig

    @Column(columnDefinition = "mediumtext")
    var errmsg: String? = null

    var success = false

    var skipUserCount = 0

    var addOrgCount = 0

    var updateOrgCount = 0

    var removeOrgCount = 0

    var addUserCount = 0

    var updateUserCount = 0

    var removeUserCount = 0

    override fun toString(): String {
        return "LdapSyncRecord(success=$success, skipUserCount=$skipUserCount, addOrgCount=$addOrgCount, updateOrgCount=$updateOrgCount, removeOrgCount=$removeOrgCount, addUserCount=$addUserCount, updateUserCount=$updateUserCount, removeUserCount=$removeUserCount, errmsg=$errmsg)"
    }


}
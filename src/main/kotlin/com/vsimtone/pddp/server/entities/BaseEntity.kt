package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.vsimtone.pddp.server.services.IDGeneratorService
import jakarta.persistence.*
import org.apache.solr.client.solrj.beans.Field
import org.hibernate.annotations.GenericGenerator
import org.springframework.format.annotation.DateTimeFormat
import java.io.Serializable
import java.text.SimpleDateFormat
import java.util.*
import kotlin.reflect.KProperty1

/**
 * Created by zhangkun on 15-6-5.
 */
@MappedSuperclass
@JsonIgnoreProperties(value = ["hibernateLazyInitializer", "\$\$_hibernate_interceptor"])
open class BaseEntity : Serializable {

    companion object {
        const val DEFAULT_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss"
        val DEFAULT_DATE_FORMAT get() = SimpleDateFormat(DEFAULT_DATE_PATTERN)

        const val SIZE_64KB = 65535 - 1; // Text
        const val SIZE_16MB = 1024 * 1024 * 16 - 1; // MediumText
        const val SIZE_1GB = 1024 * 1024 * 1024; // LongText


        class IDArraySerializer : JsonSerializer<List<Long>>() {
            override fun serialize(value: List<Long>?, gen: JsonGenerator, serializers: SerializerProvider) {
                if (value == null)
                    gen.writeNull()
                else {
                    gen.writeStartArray()
                    value.forEach {
                        gen.writeString(it.toString())
                    }
                    gen.writeEndArray()
                }
            }
        }


        class OnlySpecKeySerializer<T>(private vararg val fields: KProperty1<T, *>) : JsonSerializer<T>() {
            override fun serialize(value: T?, gen: JsonGenerator, serializers: SerializerProvider) {
                if (value == null)
                    gen.writeNull()
                else {
                    gen.writeStartObject()
                    fields.forEach {
                        gen.writeFieldName(it.name)
                        gen.writeObject(it.getter.call(value))
                    }
                    gen.writeEndObject()
                }
            }
        }

        class OnlyIDSerializer : JsonSerializer<BaseEntity>() {
            override fun serialize(value: BaseEntity?, gen: JsonGenerator, serializers: SerializerProvider) {
                if (value == null)
                    gen.writeNull()
                else {
                    gen.writeObject(mapOf("id" to value.id))
                }
            }
        }

        class IDSerializer : JsonSerializer<Long>() {
            override fun serialize(value: Long?, gen: JsonGenerator, serializers: SerializerProvider) {
                if (value == null)
                    gen.writeNull()
                else
                    gen.writeString(value.toString())
            }
        }

        class IDDeserializer : JsonDeserializer<Long>() {
            override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Long {
                return p.valueAsString.toLong()
            }
        }
    }


    @Id
    @Column(name = "id")
    @GeneratedValue(generator = "id_gen")
    @GenericGenerator(name = "id_gen", strategy = IDGeneratorService.IDGeneratorImplName)
    @JsonSerialize(using = IDSerializer::class)
    @JsonDeserialize(using = IDDeserializer::class)
    @Field
    var id: Long? = null

    @DateTimeFormat(pattern = DEFAULT_DATE_PATTERN)
    @Column
    @Field
    var createdAt: Date? = null

    @DateTimeFormat(pattern = DEFAULT_DATE_PATTERN)
    @Column
    @Field
    var updatedAt: Date? = null

    @Transient
    @JsonSerialize
    @JsonInclude
    private var bundle: MutableMap<String, Any?>? = HashMap()

    @Transient
    @JsonIgnore
    var loadFromDB = false

    constructor(id: Long?) {
        this.id = id
    }

    constructor() {}

    @PrePersist
    fun onCreate() {
        if (createdAt == null)
            createdAt = Date()
        if (updatedAt == null)
            updatedAt = createdAt
    }

    @PreUpdate
    fun onUpdate() {
        updatedAt = Date()
    }

    @PostLoad
    private fun afterLoad() {
        loadFromDB = true
        this.onLoad()
    }

    fun onLoad() {

    }

    fun setBundle(bundle: MutableMap<String, Any?>) {
        this.bundle = bundle
    }

    fun putBundle(name: String, value: Any?) {
        if (bundle == null) bundle = HashMap()
        bundle!![name] = value
    }

    fun getBundle(name: String): Any? {
        return if (bundle != null) bundle!![name] else null
    }

    @JsonSerialize
    @JsonInclude
    fun getBundle(): Map<String, Any?>? {
        return bundle
    }


    override fun toString(): String {
        return javaClass.simpleName + "{" +
            "id=" + id +
            ", createdAt=" + (if (createdAt != null) DEFAULT_DATE_FORMAT.format(createdAt) else null) +
            ", updatedAt=" + (if (updatedAt != null) DEFAULT_DATE_FORMAT.format(updatedAt) else null) +
            '}'.toString()
    }

    override fun equals(o: Any?): Boolean {
        if (this === o) return true
        if (o == null || javaClass != o.javaClass) return false

        val that = o as BaseEntity?

        return !if (id != null) id != that!!.id else that!!.id != null

    }

}

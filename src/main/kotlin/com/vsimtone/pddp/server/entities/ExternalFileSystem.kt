package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.querydsl.core.annotations.Config
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.ManyToOne

@Entity
@Config(defaultVariableName = "a")
class ExternalFileSystem : BaseEntity() {

    enum class Type { S3, SFTP, FTP, FTPS, SAMBA }

    // 所属机构
    @ManyToOne()
    var belongOrg: Organization? = null;

    @Enumerated(EnumType.STRING)
    var type: Type? = null;

    var name: String? = null;

    var username: String? = null;

    @JsonIgnore
    var password: String? = null;

    var host: String? = null;

    var port = 0;

    var rootPath: String? = null;
    
    override fun toString(): String {
        return "ExternalFileSystem(type=$type, name=$name, username=$username, host=$host, port=$port, rootPath=$rootPath)"
    }


}
package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonConverter
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.utils.AppUtils
import org.hibernate.annotations.DynamicUpdate
import java.util.*
import jakarta.persistence.*

@Entity
@Table(
    indexes = [
        Index(name = "i_node_id", columnList = "nodeId", unique = true)
    ]
)
@Config(defaultVariableName = "a")
@DynamicUpdate
class EdgeNode : BaseEntity() {

    enum class Status { Online, Syncing, Offline }
    enum class PerfMonUploadType { None, Stats, Details }

    @ManyToOne()
    @JsonView(JsonViews.SpecList::class, JsonViews.SpecView::class)
    var cluster: EdgeNodeCluster? = null

    // 节点状态
    @Enumerated(EnumType.STRING)
    lateinit var status: Status

    // 节点ID
    @JsonView(JsonViews.SpecList::class)
    lateinit var nodeId: String

    // 节点IP
    @JsonView(JsonViews.SpecList::class)
    lateinit var ip: String;

    // 监控的网卡IP
    @JsonView(JsonViews.SpecList::class)
    var perfMonInterIP: String? = null;

    // 节点主机名
    @JsonView(JsonViews.SpecList::class)
    lateinit var hostname: String;

    // 节点版本
    @JsonView(JsonViews.SpecList::class)
    lateinit var nodeVersion: String;

    @JsonView(JsonViews.SpecView::class)
    lateinit var nodeHost: String;

    // 访问地址(https)
    @JsonView(JsonViews.SpecList::class)
    lateinit var httpsUrl: String;

    // 访问地址(http)
    @JsonView(JsonViews.SpecList::class)
    lateinit var httpUrl: String;

    // 加密证书
    @OneToOne(fetch = FetchType.LAZY)
    @JsonView(JsonViews.SpecList::class)
    lateinit var certKeyPair: CertKeyPair

    // 集群证书
    @JsonView(JsonViews.SpecList::class)
    @Column(length = 4096)
    lateinit var clusterPubkey: String

    // 心跳时间
    @JsonView(JsonViews.SpecList::class)
    var pingTime: Date? = null;

    // 连接时间
    @JsonView(JsonViews.SpecList::class)
    var connectTime: Date? = null;

    // 连接服务器名称
    @JsonView(JsonViews.SpecList::class)
    var connectServerName: String? = null;

    // 内存占用
    @JsonView(JsonViews.SpecList::class)
    var memAlloc: Long = 0L;

    // 总物理内存
    @JsonView(JsonViews.SpecList::class)
    var sysMemTotal: Long = 0

    // 物理CPU信息
    @JsonView(JsonViews.SpecList::class)
    var sysCpuInfo: String? = null

    // 进程启动时间
    @JsonView(JsonViews.SpecList::class)
    var nodeUptime: Long = 0

    @JsonView(JsonViews.SpecList::class)
    var dataDiskUsed: Long = 0

    @JsonView(JsonViews.SpecList::class)
    var dataDiskTotal: Long = 0
    
    @Version
    @JsonIgnore
    var dbVersion: Long = 0L;

    override fun toString(): String {
        return "EdgeNode(${cluster?.name}, $id, $ip, ${certKeyPair?.publicKeyHash?.substring(0, 4)}...${certKeyPair?.publicKeyHash?.substring((certKeyPair?.publicKeyHash?.length ?: 0) - 4)})"
    }

}

package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonConverter
import com.vsimtone.pddp.server.convert.JsonMapConverter
import jakarta.persistence.*
import org.hibernate.annotations.DynamicUpdate
import java.util.*

@Entity
@Table(
    indexes = [
        Index(name = "i_device_id", columnList = "deviceId", unique = true),
        Index(name = "i_from_ip", columnList = "fromIP"),
        Index(name = "i_lastOnlineTime", columnList = "lastOnlineTime")
    ]
)
@Config(defaultVariableName = "a")
@DynamicUpdate
class ClientDevice : BaseEntity() {

    enum class SysType { Windows, Kylin, UOS, Unknown }
    enum class SysArch { X86, X86_64, ARM64, ARM32, Unknown }

    enum class Status { Online, NoUserSession, Offline, Expired }

    //    上传日志、卸载设备、重置ID、重置路由
    enum class Action { UploadLog, Uninstall, ResetID, ResetRoute, ResetLogin, ReloadConfig }

    enum class LoginType { Auto, Manual }

    companion object {
        @Converter
        class ActionArrayConverter : JsonConverter<Array<String>>(Array<String>::class.java) {
        }
    }

    // 设备ID
    lateinit var deviceId: String;

    // 主机名称
    lateinit var hostname: String;

    // 用户账户
    var username: String? = null

    @Column(length = 1024)
    var userSessions: String? = null

    // 请求来自IP
    var fromIP: String? = null

    // 本地IP
    var localIPAddr: String? = null

    // 本地IP MAC地址
    var localIPMAC: String? = null

    lateinit var version: String

    var versionNum: Long = 0

    var systemVersion: String? = null

    var systemKernel: String? = null

    @Enumerated(EnumType.STRING)
    var systemType: SysType? = null

    @Enumerated(EnumType.STRING)
    var systemArch: SysArch? = null

    // 开机时间
    var systemStartTime: Date? = null

    // 系统负载(1分钟)
    var systemCpuUsage1M: Float? = null

    // 系统负载(5分钟)
    var systemCpuUsage5M: Float? = null

    // 系统负载(15分钟)
    var systemCpuUsage15M: Float? = null

    // 系统CPU数量
    var systemCpuCount: Int? = null

    // 系统总内存
    var systemMemTotal: Long? = null

    // 系统已使用内存
    var systemMemUsed: Long? = null

    // 系统总交换
    var systemSwapTotal: Long? = null

    // 系统已使用交换
    var systemSwapUsed: Long? = null

    // 系统已登录用户数量
    var systemLoginUsers: Int? = null

    // 在线开始时间
    var firstOnlineTime: Date? = null

    // 心跳时间
    var lastOnlineTime: Date? = null

    // 登录时间
    var loginProgramTime: Date? = null

    @Enumerated(EnumType.STRING)
    var loginType: LoginType? = null

    // 状态
    @Enumerated(EnumType.STRING)
    var status: Status = Status.Offline

    @ManyToOne(fetch = FetchType.LAZY)
    var user: User? = null

    @ManyToOne(fetch = FetchType.LAZY)
    var org: Organization? = null

    // 边缘节点集群ID
    var edgeNodeClusterId: Long? = null

    // 备注
    @Column(length = 512)
    var remark: String? = null;

    // 最大消息索引
    var maxNotifyMsgIdx: Long = 0L;

    // 已通知消息索引
    var notifiedMsgIdx: Long = 0L;

//    待处理动作
    @Convert(converter = ActionArrayConverter::class)
    var pendingActions = arrayOf<String>()

//    健康状态
    @Convert(converter = JsonMapConverter::class)
    @Column(length = SIZE_64KB)
    var healthState: Map<String, String>? = null

//    文件列表
    @Column(length = SIZE_64KB)
    var fsList: String? = null

    var healthScore = 0;

    var totalMemUse = 0L;

//    数据库版本
    @Version
    @JsonIgnore
    var dbVersion: Long = 0L;

    fun toFullString(): String {
        return "ClientDevice(deviceId='$deviceId', hostname='$hostname', username=$username, fromIP=$fromIP, localIPAddr=$localIPAddr, localIPMAC=$localIPMAC, version='$version', versionNum=$versionNum, systemVersion=$systemVersion, lastOnlineTime=$lastOnlineTime, startProgramTime=$loginProgramTime, status=$status, userId=$user?.id, orgId=$org?.id)"
    }


    override fun toString(): String {
        return "ClientDevice(id='$id', hostname='$hostname', ip='$fromIP', systemVersion=$systemVersion, org='${org?.name}', user='${user?.username}')"
    }
}
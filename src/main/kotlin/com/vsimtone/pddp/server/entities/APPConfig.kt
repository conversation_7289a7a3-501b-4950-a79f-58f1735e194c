package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import jakarta.persistence.*

@Table(
    indexes = [Index(name = "i_key", columnList = "c_key", unique = true)]
)
@Entity
@Config(defaultVariableName = "a")
class APPConfig : BaseEntity() {

    @Column(name = "c_key")
    lateinit var key: String

    @Column(name = "c_val", length = SIZE_1GB)
    var `val`: String? = null

    @Enumerated(EnumType.STRING)
    var type: Type? = null

    enum class Type {
        String, Integer, Float, Boolean, JsonMap, JsonArray, Long
    }

    override fun toString(): String {
        return "APPConfig(id='$id', key='$key', `val`=$`val`, type=$type)"
    }
}

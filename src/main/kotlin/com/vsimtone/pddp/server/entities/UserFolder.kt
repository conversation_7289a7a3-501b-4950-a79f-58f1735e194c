package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.querydsl.core.annotations.Config
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.ManyToOne
import org.hibernate.annotations.DynamicUpdate

@Entity
@Config(defaultVariableName = "a")
@DynamicUpdate
class UserFolder : BaseEntity() {

    @ManyToOne(fetch = FetchType.LAZY)
    lateinit var owner: User

    @Column(length = 512)
    var name = ""

    @Column(length = 512)
    var path = ""

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    var parent: UserFolder? = null

}

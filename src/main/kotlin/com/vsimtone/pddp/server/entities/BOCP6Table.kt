package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import jakarta.persistence.*

@Entity
@Table(
    name = "boc_p6_table",
    indexes = [
        Index(name = "i_type_key", columnList = "f_type,f_key", unique = true)
    ]
)
@Config(defaultVariableName = "a")
class BOCP6Table : BaseEntity() {

    enum class Type { SYNC_FILE, ORG_DATA, USER_DATA }

    @Column(name = "f_type")
    @Enumerated(EnumType.STRING)
    lateinit var type: Type

    @Column(name = "f_key")
    lateinit var key: String

    @Column(name = "f_name")
    var name: String? = null

    @Column(name = "f_email")
    var email: String? = null

    @Column(name = "f_parent")
    var parent: String? = null

    var isValidData = true;

    var userLastSeen: String? = null

    var orgLastSeen: String? = null

    var orgHierLastSeen: String? = null

    var updatedCount: Long = 0;

    @Column(name = "f_raw", length = SIZE_64KB)
    var raw: String? = null

    override fun toString(): String {
        val lastSeen = mutableListOf(this.userLastSeen, this.orgLastSeen, this.orgHierLastSeen).filterNotNull().joinToString(",")
        return "BOCP6Table(id=$id, type=$type, createdAt=${createdAt?.time}, updatedAt=${updatedAt?.time}, key='$key', name=$name, email=$email, parent=$parent, isValidData=$isValidData, lastSeen=$lastSeen, updatedCount=$updatedCount)"
    }

}

package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.json.JsonViews
import jakarta.persistence.*

@Table(
    indexes = [
        Index(name = "i_file_id", columnList = "fileID"),
        Index(name = "i_file_token", columnList = "fileToken"),
        Index(name = "i_file_b3sum", columnList = "fileB3Sum")
    ]
)
annotation class FileKeyIndex

@MappedSuperclass
class FileKey : BaseEntity() {

    var fileID: Long = 0

    @Column(length = 256)
    @JsonView(JsonViews.Exclude::class)
    @JsonIgnore
    var fileToken: String = ""

    @Column(length = 256)
    @JsonView(JsonViews.Exclude::class)
    @JsonIgnore
    var fileB3Sum: String = ""

}


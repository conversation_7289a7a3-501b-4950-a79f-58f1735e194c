package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.querydsl.core.annotations.Config
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table

@Entity
@Table
@Config(defaultVariableName = "a")
class ClientDeviceHistory : BaseEntity() {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    lateinit var clientDevice: ClientDevice

    var fieldKey = "";

    var fieldName = "";

    var oldValue = "";

    var newValue = "";

    var dataIdx = 0L;

}

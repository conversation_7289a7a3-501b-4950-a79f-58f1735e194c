package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.json.JsonViews
import jakarta.persistence.*

@Entity
@FileKeyIndex
@Table(
    indexes = [
        Index(name = "i_part_b3sum", columnList = "partB3sum", unique = true)
    ]
)
@Config(defaultVariableName = "a")
class FilePart : FileKey() {

    var partIndex: Int = 0

    var partSize: Int = 0

    var partB3sum: String = ""

}


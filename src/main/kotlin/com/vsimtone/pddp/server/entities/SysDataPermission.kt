package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonListConverter
import jakarta.persistence.Column
import jakarta.persistence.Convert
import jakarta.persistence.Entity
import jakarta.persistence.Table

/**
 * 系统数据权限，通用对象，设定数据权限
 */
@Entity
@Table
@Config(defaultVariableName = "a")
class SysDataPermission : BaseEntity() {

    enum class Type { OrgSelf, OrgAll, User }

    class SysDataPermItem {

        // 类型
        lateinit var type: Type

        // 机构
        @JsonSerialize(using = Companion.IDArraySerializer::class)
        var orgs: List<Long>? = null

        // 用户
        @JsonSerialize(using = Companion.IDArraySerializer::class)
        var users: List<Long>? = null

        // 可修改
        var modifiable = false

    }

    // 名称
    lateinit var name: String;

    @Column(length = 4096)
    @Convert(converter = JsonListConverter::class)
    var perms = mutableListOf<SysDataPermItem>()

}
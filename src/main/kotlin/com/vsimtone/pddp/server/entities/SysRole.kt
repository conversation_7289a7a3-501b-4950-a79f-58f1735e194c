package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.querydsl.core.annotations.PropertyType
import com.querydsl.core.annotations.QueryType
import com.vsimtone.pddp.server.convert.JsonListConverter
import com.vsimtone.pddp.server.json.JsonViews
import jakarta.persistence.*

@Entity
@Table
@Config(defaultVariableName = "a")
class SysRole : BaseEntity() {

    enum class Code {
        // 系统管理员
        SysManager,

        // 数据管理员
        DataManager,

        // 内管理员
        ContentManager,

        // 安全审计员
        AuditManager,

        // 日志管理员
        OptLogView,

        // 运维管理员
        OpsManager,

        // 超级管理员
        SuperManager
    }

    var name = ""

    @Enumerated(EnumType.STRING)
    lateinit var code: Code

    @Column(length = 4096)
    @Convert(converter = JsonListConverter::class)
    @JsonView(JsonViews.Exclude::class)
    @QueryType(PropertyType.STRING)
    var permissions = mutableListOf<String>()

}
package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonListConverter
import jakarta.persistence.*

@Entity
@Table
@Config(defaultVariableName = "a")
class EdgeNodeRoute : BaseEntity() {

    enum class Status { Enabled, Disabled }

    // 所属机构
    @ManyToOne()
    var belongOrg: Organization? = null;

    @Enumerated(EnumType.STRING)
    var status: Status? = null

    lateinit var network: String

    lateinit var lowAddress: String

    lateinit var highAddress: String

    var addressCount: Long = 0

    var sortIndex: Int = 0

    @Convert(converter = JsonListConverter::class)
    var edgeNodeClusters = mutableListOf<Long>()

}

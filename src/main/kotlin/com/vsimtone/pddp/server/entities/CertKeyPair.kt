package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.querydsl.core.annotations.Config
import java.security.KeyPair
import jakarta.persistence.*
import kotlin.jvm.Transient

@Entity
@Table
@Config(defaultVariableName = "a")
class CertKeyPair : BaseEntity() {
    enum class UsedBy { Server, EdgeNode, EdgeNodeCluster }

    @Enumerated(EnumType.STRING)
    lateinit var usedBy: UsedBy

    // 公钥
    @Column(length = 4096)
    lateinit var publicKey: String

    @Transient
    var keyLen = 0

    // 公钥散列
    @Column(length = 256)
    lateinit var publicKeyHash: String

    // 私钥
    @Column(length = 4096)
    @JsonIgnore
    var privateKey: String? = null

    // 备注
    @Column(length = 4096)
    var remark: String? = null

    @Transient
    @JsonIgnore
    var _keyPair: KeyPair? = null

    override fun toString(): String {
        return "CertKeyPair(publicKeyHash='$publicKeyHash', keyLen='${keyLen}', remark=$remark)"
    }

}
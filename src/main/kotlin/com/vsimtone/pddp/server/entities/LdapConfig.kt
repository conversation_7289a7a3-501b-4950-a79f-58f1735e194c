package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.json.JsonViews
import jakarta.persistence.*
import java.util.*

@Entity
@Table()
@Config(defaultVariableName = "a")
class LdapConfig : BaseEntity() {
    enum class Type { ActiveDirectory, Ldap, BOC_P6 }

    @Enumerated(EnumType.STRING)
    var type: Type? = null
    var host: String? = null;
    var port: Int = 389;
    var username: String? = null;

    var domain: String = "";

    @JsonView(JsonViews.Exclude::class)
    @JsonIgnore
    var password: String? = null;

    var rootDN: String? = null;


    var bocP6FSType: ExternalFileSystem.Type? = null
    var bocP6RootPath: String? = null;

    @OneToOne
    var syncTo: Organization? = null;
    var syncDN: String? = null;
    var syncScheduleCron: String? = null;
    var syncScheduleLatestExecTime: Date? = null;
    var syncScheduleNextExecTime: Date? = null;
    var syncMaxDeleteSize: Int? = 0;

    var authWithAdHost: String? = null;
    var authWithAdPort: Int? = null;

    var authWithKrbPrincipal: String? = null;

    @JsonView(JsonViews.Exclude::class)
    @JsonIgnore
    @Column(length = SIZE_16MB)
    var authWithKrbKeytab: String? = null;

    var authWithKrbKeytabHash: String? = null;


    override fun toString(): String {
        return "LdapConfig(type=$type, host=$host, port=$port, username=$username, domain='$domain', syncTo=$syncTo, syncDN=$syncDN, authWithAdHost=$authWithAdHost, authWithAdPort=$authWithAdPort, authWithKrbPrincipal=$authWithKrbPrincipal, authWithKrbKeytabHash=$authWithKrbKeytabHash)"
    }

}
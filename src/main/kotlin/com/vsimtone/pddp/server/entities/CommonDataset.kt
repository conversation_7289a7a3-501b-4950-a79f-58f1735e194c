package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonListConverter
import jakarta.persistence.*
import org.hibernate.annotations.DynamicUpdate

@Table()
@Entity()
@Config(defaultVariableName = "a")
@DynamicUpdate
class CommonDataset : BaseEntity() {
    enum class Type { Regex, Word, FileExt, MimeType, ProcessName, UsbDeviceID, MailAddress, IPV4Cidr, IPV6Cidr, FSPath, Other, Domain, Url, GVFSType }

    @Enumerated(EnumType.STRING)
    @Column(name = "f_type")
    lateinit var type: Type

    var name: String = ""

    @Column(name = "f_desc")
    var desc: String = ""

    @Column(name = "f_code")
    var code: String? = null

    @Column(name = "f_values", columnDefinition = "mediumtext")
    @Convert(converter = JsonListConverter::class)
    var values = mutableListOf<String>()

    var disabled = false;

    override fun toString(): String {
        return "CommonDataset(id=$id, type=$type, name='$name')"
    }

}
package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonConverter
import com.vsimtone.pddp.server.convert.JsonListConverter
import com.vsimtone.pddp.server.entities.EdgeNode.PerfMonUploadType
import com.vsimtone.pddp.server.json.JsonViews
import org.hibernate.annotations.DynamicUpdate
import jakarta.persistence.*
import org.checkerframework.checker.units.qual.A
import kotlin.jvm.java

@Entity
@Table(
)
@Config(defaultVariableName = "a")
@DynamicUpdate
class EdgeNodeCluster : BaseEntity() {

    companion object {
        class EdgeNodeClusterConfig : java.io.Serializable {

            // 服务器上传速率限制
            var serverUpSpeedLimit = 100 * 1024 * 1024L

            // 服务器上传速率限制
            var serverDownSpeedLimit = 100 * 1024 * 1024L

            // 终端并发限制
            var clientConcurrentLimit = 1000L

            // 终端并发等待时间
            var clientConcurrentWaitTime = 20 * 1000

            // 终端并发等待数量
            var clientConcurrentWaitLimit = 2000

            // 终端下载速度限制
            var clientDownSpeedLimit = 1024 * 1024L

            // 终端请求批量转发数量
            var clientBatchForwardCount = 100

            // 终端请求批量转发大小(1MB)
            var clientBatchForwardSize = 1024 * 1024L

            // 终端请求批量转发周期(100ms)
            var clientBatchForwardInterval = 100

            // 性能统计上传类型
            var perfMonUploadType = PerfMonUploadType.Stats

            // 最低数据磁盘可用空间(500MB)
            var minDataDiskFree = 1024 * 1024 * 500L

            // 配置版本
            var version = 1L

            var nodes = mutableListOf<String>()

        }

        class EdgeNodePendingAction : java.io.Serializable {
        }

        class EdgeNodeClusterConfigConverter : JsonConverter<EdgeNodeClusterConfig>(EdgeNodeClusterConfig::class.java) {}
    }

    // 所属机构
    @ManyToOne()
    var belongOrg: Organization? = null;

    // 已启用
    var enabled = false

    lateinit var name: String

    var useCustomConfig = false;

    var configVersion = 1L

    // 配置文件
    @Lob()
    @Convert(converter = EdgeNodeClusterConfigConverter::class)
    @JsonView(JsonViews.SpecView::class, JsonViews.SpecList::class)
    var config: EdgeNodeClusterConfig? = null

    var onlyAllowedNetworks = false;

    // 加密证书
    @OneToOne(fetch = FetchType.LAZY)
    @JsonView(JsonViews.SpecList::class)
    var certKeyPair: CertKeyPair? = null

    @Version
    @JsonIgnore
    var dbVersion: Long = 0L;

    override fun toString(): String {
        return "EdgeNodeCluster(id='$id', name='$name', publicKeyHash='${certKeyPair?.publicKeyHash}')"
    }
}

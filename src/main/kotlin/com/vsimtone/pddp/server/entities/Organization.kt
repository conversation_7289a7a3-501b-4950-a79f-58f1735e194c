package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.querydsl.core.annotations.Config
import jakarta.persistence.*

@Entity
@Table(
    indexes = [
        Index(name = "i_key", columnList = "f_key", unique = true),
        Index(name = "i_path", columnList = "path", unique = true)
    ]
)
@Config(defaultVariableName = "a")
class Organization : BaseEntity() {

    enum class Type { Sys, Local, Ldap }

    var name = ""

    @Enumerated(EnumType.STRING)
    var type = Type.Local

    @Column(name = "f_key")
    var key: String? = null

    @Column(length = 512)
    var path = ""

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    var parent: Organization? = null

    override fun toString(): String {
        return "Organization(id='$id', name='$name', type=$type, path='$path')"
    }

}
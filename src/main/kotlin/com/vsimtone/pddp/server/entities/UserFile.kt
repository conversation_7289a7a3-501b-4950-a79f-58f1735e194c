package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.FetchType
import jakarta.persistence.ManyToOne
import org.hibernate.annotations.DynamicUpdate

@Entity
@FileKeyIndex
@Config(defaultVariableName = "a")
class UserFile : FileKey() {

    companion object {
        enum class Type { USER_SHARE, USER_CREATE }
    }

    @ManyToOne(fetch = FetchType.LAZY)
    lateinit var owner: User

    @ManyToOne(fetch = FetchType.LAZY)
    lateinit var folder: UserFolder

    @Column(length = 512)
    lateinit var fileName: String

    @Column(length = 512)
    var fileSize = 0

    var fileType: String = ""

    @ManyToOne
    var fromShareUser: User? = null
    
}

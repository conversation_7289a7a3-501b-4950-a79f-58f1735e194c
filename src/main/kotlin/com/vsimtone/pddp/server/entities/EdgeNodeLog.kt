package com.vsimtone.pddp.server.entities

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonListConverter
import com.vsimtone.pddp.server.convert.JsonMapConverter
import com.vsimtone.pddp.server.json.JsonViews
import jakarta.persistence.*

@Entity
@Config(defaultVariableName = "a")
class EdgeNodeLog : BaseEntity() {

    companion object {
        enum class Type {
            JoinCluster, Online, Offline, Enable, Disable
        }
    }

    lateinit var nodeId: String

    @ManyToOne(fetch = FetchType.LAZY)
    lateinit var edgeNode: EdgeNode

    @Enumerated(EnumType.STRING)
    var type: Type = Type.Online

    var title: String = ""

    @Column(length = 512)
    var message: String = ""

    @Column(name = "f_data", columnDefinition = "mediumtext")
    @Convert(converter = JsonMapConverter::class)
    var data = mutableMapOf<String, Any>()
}


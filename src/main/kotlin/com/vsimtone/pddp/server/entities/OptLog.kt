package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import com.vsimtone.pddp.server.convert.JsonMapConverter
import jakarta.persistence.*

@Entity
@Config(defaultVariableName = "a")
@Table(
    indexes = [
        Index(name = "i_username", columnList = "username"),
        Index(name = "i_objName", columnList = "objName")
    ]
)
class OptLog : BaseEntity() {

    companion object {
        const val VIEW = "查看"
        const val ADD = "新增"
        const val EDIT = "修改"
        const val DELETE = "删除"
        const val BATCH_DELETE = "批量删除"
        const val IMPORT = "导入"
        const val EXPORT = "导出"
        const val ENABLE = "启用"
        const val DISABLED = "禁用"
    }

    lateinit var username: String


    lateinit var type: String

    @Column()
    lateinit var moduleName: String

    @Column()
    lateinit var objName: String

    @Convert(converter = JsonMapConverter::class)
    @Column(length = SIZE_16MB)
    var details = mapOf<String, Any?>()

}
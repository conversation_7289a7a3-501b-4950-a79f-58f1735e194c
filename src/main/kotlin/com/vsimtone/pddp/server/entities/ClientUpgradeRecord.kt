package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import jakarta.persistence.Entity
import jakarta.persistence.Index
import jakarta.persistence.ManyToOne
import jakarta.persistence.Table

@Entity
@Table(
    indexes = [
        Index(name = "i_upgrade_file_device", columnList = "client_upgrade_file_id,client_device_id", unique = true)
    ]
)
@Config(defaultVariableName = "a")
class ClientUpgradeRecord : BaseEntity() {

    @ManyToOne
    lateinit var clientUpgradeFile: ClientUpgradeFile

    @ManyToOne
    lateinit var clientDevice: ClientDevice

    var success = false;

    var pushCount = 0;

    var manualPush = false;

}

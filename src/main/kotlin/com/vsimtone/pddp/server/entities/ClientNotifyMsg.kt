package com.vsimtone.pddp.server.entities

import com.querydsl.core.annotations.Config
import jakarta.persistence.*

@Entity
@Table(
    indexes = [
        Index(name = "i_msgIdx", columnList = "device_id,msgIdx", unique = true),
    ]
)
@Config(defaultVariableName = "a")
class ClientNotifyMsg : BaseEntity() {

    enum class Type { EventApproveResult, EventApproveRequest }

    @ManyToOne
    lateinit var device: ClientDevice;

    @Enumerated(EnumType.STRING)
    lateinit var type: Type

    lateinit var title: String

    @Column(length = 4096)
    lateinit var msg: String

    lateinit var target: String

    var msgIdx = 1L;

    var notified = false;

}
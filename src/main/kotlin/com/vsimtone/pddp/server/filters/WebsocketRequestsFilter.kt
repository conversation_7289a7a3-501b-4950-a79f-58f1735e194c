package com.vsimtone.pddp.server.filters

import com.vsimtone.pddp.server.utils.AppUtils
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter

@Component
class WebsocketRequestsFilter : OncePerRequestFilter() {

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        if (request.getHeader("upgrade") != null) {
            request.getSession(true).setAttribute("ClientIP", AppUtils.getClientIpAddress(request));
        }
        filterChain.doFilter(request, response);
    }
}

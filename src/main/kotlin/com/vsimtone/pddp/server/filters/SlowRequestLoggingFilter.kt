package com.vsimtone.pddp.server.filters

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import java.util.logging.Logger

@Component
class SlowRequestLoggingFilter : OncePerRequestFilter() {


    var log = Logger.getLogger(SlowRequestLoggingFilter::class.simpleName)

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        val startTime = System.currentTimeMillis()
        try {
            filterChain.doFilter(request, response)
        } catch (e: Exception) {
            throw e;
        } finally {
            val useTime = System.currentTimeMillis() - startTime
            if (useTime >= 1000) {
                val status = response.status
                val rlog = StringBuffer()
                rlog.append("#")
                rlog.append(String.format("[%1$4s,", request.method))
                rlog.append(status)
                if (useTime <= 1000)
                    rlog.append(String.format(",%1$3dms]", useTime))
                else if (useTime < 1000 * 9999)
                    rlog.append(String.format(",%1$4ds]", useTime / 1000))
                else
                    rlog.append(String.format(",%1ms]", useTime))

                if (SecurityContextHolder.getContext().authentication != null)
                    rlog.append("[" + SecurityContextHolder.getContext().authentication.name + "]")

                rlog.append(" [" + request.remoteAddr + "," + getClientIpAddress(request) + "]")
                rlog.append(String.format(" [%1$-50s]", getURI(request)))
                rlog.append(" [" + (if (request.getAttribute("req_log") != null) request.getAttribute("req_log") else "-") + "]")
                if (status == 302)
                    rlog.append("[Redirect:" + response.getHeader("Location") + "]")
                else
                    rlog.append("[-]")
                rlog.append("[" + request.getHeader("user-agent") + "]")
                rlog.append("#")
                if (status == 200 || status == 304)
                    log.info(rlog.toString())
                else
                    log.warning(rlog.toString())
            }
        }
    }

    companion object {

        private val HEADERS_TO_TRY = arrayOf(
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "REMOTE_ADDR"
        )

        fun getClientIpAddress(request: HttpServletRequest): String {
            for (header in HEADERS_TO_TRY) {
                val ip = request.getHeader(header)
                if (ip != null && ip.length != 0 && !"unknown".equals(ip, ignoreCase = true)) {
                    return ip
                }
            }
            return request.remoteAddr
        }

        fun getURI(request: HttpServletRequest): String {
            val url = StringBuffer()
            url.append(request.scheme + "://")
            url.append(request.getHeader("Host"))
            url.append(request.requestURI)
            if (request.queryString != null)
                url.append("?" + request.queryString)
            return url.toString()
        }
    }
}

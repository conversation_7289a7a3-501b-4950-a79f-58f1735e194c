package com.vsimtone.pddp.server.filters

import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.services.LicenseService
import com.vsimtone.pddp.server.utils.JSONUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse

@Component
class LicenseFilter : OncePerRequestFilter() {

    companion object {
        val ALLOW_PATHS = listOf(
            "/edge_node_endpoint",
            "/users/login",
            "/users/logout",
            "/users/curr",
            "/users/domains",
            "/captcha",
            "/license",
            "/license/upload",
            "/license/components",
            "/license/generate_request",
            "/sys/alerts",
            "/home/<USER>",
            "/client_devices/latest_client_packages"
        )
    }

    @Autowired
    lateinit var licenseService: LicenseService

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        if (licenseService.getLicenseStatus() == LicenseService.Companion.LicenseStatus.Active)
            return filterChain.doFilter(request, response)
        val allow = ALLOW_PATHS.find { request.servletPath.endsWith(it) } != null
        if (allow)
            filterChain.doFilter(request, response);
        else {
            response.writer.write(JSONUtils.toString(RestResp(403, "license_invalidate")))
        }
    }
}

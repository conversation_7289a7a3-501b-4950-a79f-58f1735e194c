package com.vsimtone.pddp.server.redis

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

import java.io.Serializable

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-3-7.
 */

@Configuration
@ConfigurationProperties(prefix = "spring.data.redis")
class SpringRedisConfig : Serializable {
    var host: String? = null
    var port: Int = 6379
    var database: Int = 0
    var minIdleSize = 10
    var poolSize = 100
    var timeout = 10000
    var password: String? = null
}

package com.vsimtone.pddp.server.scheduled_items

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.EntityPathBase
import com.vsimtone.pddp.server.entities.LdapConfig
import com.vsimtone.pddp.server.entities.QLdapConfig
import com.vsimtone.pddp.server.schedules.EntityScheduled
import com.vsimtone.pddp.server.services.LdapConfigService
import com.vsimtone.pddp.server.services.UserService
import com.vsimtone.pddp.server.utils.DateUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*


@Component
class LdapUserSyncScheduled : EntityScheduled<LdapConfig>() {

    companion object {
        val DO_SYNC = "do_sync"
    }

    @Autowired
    lateinit var ldapConfigService: LdapConfigService

    @Autowired
    lateinit var userService: UserService

    override fun init(): EntityPathBase<LdapConfig> {
        entityClass = LdapConfig::class.java
        defineItem(DO_SYNC, DateUtil.minute(60))
        return QLdapConfig.a
    }

    override fun listQuery(key: String, currDate: Date): Predicate? {
        val a = QLdapConfig.a
        if (DO_SYNC == key) {
            return BooleanBuilder(a.syncTo.isNotNull).and(a.syncScheduleCron.isNotNull).and(a.syncScheduleNextExecTime.loe(currDate))
        }
        return null;
    }

    override fun process(o: LdapConfig, key: String, currDate: Date) {
        if (DO_SYNC == key) {
            o.syncScheduleLatestExecTime = Date()
            o.syncScheduleNextExecTime = userService.getNextExecTime(o.syncScheduleLatestExecTime, o.syncScheduleCron)
            ldapConfigService.save(o)
            userService.runInAfterTranCommitted {
                userService.sync(o.id!!)
            }
        }
    }
}
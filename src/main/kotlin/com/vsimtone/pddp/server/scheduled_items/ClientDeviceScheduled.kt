package com.vsimtone.pddp.server.scheduled_items

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.EntityPathBase
import com.vsimtone.pddp.server.entities.ClientDevice
import com.vsimtone.pddp.server.entities.QClientDevice
import com.vsimtone.pddp.server.schedules.EntityScheduled
import com.vsimtone.pddp.server.services.APPConfigService
import com.vsimtone.pddp.server.services.ClientDeviceService
import com.vsimtone.pddp.server.services.OrgService
import com.vsimtone.pddp.server.services.UserService
import com.vsimtone.pddp.server.utils.DateUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*

@Component
class ClientDeviceScheduled : EntityScheduled<ClientDevice>() {

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var orgService: OrgService


    companion object {
        val UPDATE_EXPIRED = "update_expired"
        val ORG_MISS = "org_miss"
    }

    override fun init(): EntityPathBase<ClientDevice> {
        entityClass = ClientDevice::class.java
        defineItem(UPDATE_EXPIRED, DateUtil.hour(1)).config {
            it.fullPageQuery = true
            it.runInReadOnlyAndNoTran = true
        }
        //  全表扫描，一周一次
        defineItem(ORG_MISS, DateUtil.day(7)).config {
            it.fullPageQuery = true
            it.runInReadOnlyAndNoTran = true
        }
        return QClientDevice.a
    }

    override fun listQuery(key: String, currDate: Date): Predicate? {
        if (userService.isSyncing()) return null;
        val a = QClientDevice.a
        if (UPDATE_EXPIRED == key) {
            return BooleanBuilder(a.lastOnlineTime.loe(DateUtil.nowDate(0 - appConfigService.getLong(ClientDeviceService.CNF_EXPIRE_TIME)!!))).and(a.status.eq(ClientDevice.Status.Offline))
        }
        if (ORG_MISS == key) {
            return BooleanBuilder(a.user.org.ne(a.org))
        }
        return null;
    }

    override fun process(_o: ClientDevice, key: String, currDate: Date) {
        val o = clientDeviceService.findByIdInCache(_o.id!!)!!;
        if (UPDATE_EXPIRED == key) {
            logger.info("Device expired: ${o.toFullString()}")
            o.status = ClientDevice.Status.Expired
            o.edgeNodeClusterId = null
            o.user = null
            o.org = orgService.getSysOrg(OrgService.SYS_ORG_KEY_UNKNOWN_CLIENT)
            clientDeviceService.saveDelay(o, true)
        }
        if (ORG_MISS == key) {
            logger.info("Device org miss: ${o.toFullString()}")
            if (o.user == null)
                o.org = null
            else {
                o.org = userService.findById(o.user!!.id!!).org
            }
            clientDeviceService.saveDelay(o, true)
        }
    }

}
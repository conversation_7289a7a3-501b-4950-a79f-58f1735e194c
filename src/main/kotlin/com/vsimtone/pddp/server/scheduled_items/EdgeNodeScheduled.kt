package com.vsimtone.pddp.server.scheduled_items

import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.EntityPathBase
import com.vsimtone.pddp.server.entities.EdgeNode
import com.vsimtone.pddp.server.entities.QEdgeNode
import com.vsimtone.pddp.server.schedules.EntityScheduled
import com.vsimtone.pddp.server.services.EdgeNodeService
import com.vsimtone.pddp.server.utils.DateUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*

@Component
class EdgeNodeScheduled : EntityScheduled<EdgeNode>() {

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    companion object {
        val UPDATE_OFFLINE = "update_offline"
    }

    override fun init(): EntityPathBase<EdgeNode> {
        entityClass = EdgeNode::class.java
        defineItem(UPDATE_OFFLINE, DateUtil.minute(3)).config {
            it.fullPageQuery = true
            it.pageSize = 1000
            it.onlyIdColumn = QEdgeNode.a.id
            it.runInReadOnlyAndNoTran = true
        }
        return QEdgeNode.a
    }

    override fun listQuery(key: String, currDate: Date): Predicate? {
        if (UPDATE_OFFLINE == key) {
            return QEdgeNode.a.pingTime.lt(DateUtil.nowDate(DateUtil.minute(-5))).and(QEdgeNode.a.status.ne(EdgeNode.Status.Offline))
        }
        return null;
    }

    override fun process(o: EdgeNode, key: String, currDate: Date) {
        if (UPDATE_OFFLINE == key) {
            edgeNodeService.runInTran {
                val pn = edgeNodeService.findById(o.id!!)
                pn.status = EdgeNode.Status.Offline
                edgeNodeService.save(pn)
            }
        }
    }

}
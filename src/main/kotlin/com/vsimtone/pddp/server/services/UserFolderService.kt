package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.entities.QUserFolder
import com.vsimtone.pddp.server.entities.User
import com.vsimtone.pddp.server.entities.UserFolder
import com.vsimtone.pddp.server.repositories.UserFolderRepository
import jakarta.annotation.Resource
import org.springframework.stereotype.Service


@Service
class UserFolderService : BaseCrudService<UserFolder, UserFolderRepository>(QUserFolder.a) {


    @Resource
    private lateinit var userFolderRepository: UserFolderRepository


    @Resource
    private lateinit var userService: UserService

    fun createFolder(user: User, parent: UserFolder?, name: String): UserFolder {
        val userFolder = UserFolder()
        userFolder.name = name
        userFolder.owner = user
        if (parent == null)
            userFolder.path = "/"
        else{
            userFolder.parent = parent
            userFolder.path = parent.path + name + "/"
        }

        userFolderRepository.save(userFolder)
        return userFolder
    }

    override fun onReady() {
        val userList = userService.findAll();
        val queries = BooleanBuilder();
        val a = QUserFolder.a;
        queries.and(a.path.eq("/"))
        val userFolderList = userFolderRepository.findAll(queries)

        // 将已存在的根目录按用户ID映射
        val existingRoots = userFolderList.associateBy({ it.owner!!.id }, { it })

        val foldersToCreate = userList.filter { !existingRoots.containsKey(it.id) }
            .map {
                UserFolder().apply {
                    name = "我的文件"
                    owner = it
                    path = "/"
                }
            }

        if (foldersToCreate.isNotEmpty()) {
            userFolderRepository.saveAll(foldersToCreate)
        }
    }
}
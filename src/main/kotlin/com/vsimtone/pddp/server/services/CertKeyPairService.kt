package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.entities.CertKeyPair
import com.vsimtone.pddp.server.entities.QCertKeyPair
import com.vsimtone.pddp.server.repositories.CertKeyPairRepository
import com.vsimtone.pddp.server.utils.CryptUtils
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import java.security.*
import java.security.interfaces.RSAPublicKey
import java.security.spec.PKCS8EncodedKeySpec
import java.security.spec.X509EncodedKeySpec
import javax.crypto.Cipher

@Service
class CertKeyPairService : BaseCrudService<CertKeyPair, CertKeyPairRepository>(QCertKeyPair.a) {

    lateinit var serverCertKeyPair: CertKeyPair

    override fun onReady() {
        Security.addProvider(BouncyCastleProvider())
        val serverCertKeyPair = repo.findOne(BooleanBuilder().and(QCertKeyPair.a.usedBy.eq(CertKeyPair.UsedBy.Server)))
        if (serverCertKeyPair.isPresent)
            this.serverCertKeyPair = serverCertKeyPair.get()
        else {
            this.serverCertKeyPair = newCertKey(null, CertKeyPair.UsedBy.Server);
            this.serverCertKeyPair.remark = "自动生成的服务器证书";
            this.repo.save(this.serverCertKeyPair);
        }
        this.serverCertKeyPair._keyPair = toKeyPair(this.serverCertKeyPair)
        this.logger.info("Server cert key pair ${this.serverCertKeyPair}")
//        findAll().forEach {
//            it.publicKeyHash = pubKeyHash(this.toKeyPair(it))
//            save(it)
//        }
    }

    fun pubKeyHash(keyPair: KeyPair): String {
        return CryptUtils.sha256hex(keyPair.public.encoded);
    }

    fun pubKey(keyPair: KeyPair): String {
        return CryptUtils.base64encode(keyPair.public.encoded)
    }

    private fun decryptData(data: ByteArray, certKeyPair: CertKeyPair): ByteArray {
        val cipher: Cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding", "BC")
        cipher.init(Cipher.DECRYPT_MODE, certKeyPair._keyPair!!.private)
        val decodeRaw = cipher.doFinal(data)
        return decodeRaw;
    }

    fun decodeData(data: ByteArray, certKeyPair: CertKeyPair): ByteArray {
        val keySize = certKeyPair.keyLen / 8
        val cAesKey = data.sliceArray(IntRange(0, keySize - 1))
        val aesKey = decryptData(cAesKey, certKeyPair)
        val data = CryptUtils.aesGCMDecrypt(data.sliceArray(IntRange(keySize, data.size - 1)), aesKey)
        return data
    }

    private fun cryptData(data: ByteArray, certKeyPair: CertKeyPair): ByteArray {
        val cipher: Cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding", "BC")
        cipher.init(Cipher.ENCRYPT_MODE, certKeyPair._keyPair!!.public)
        val encodeRaw = cipher.doFinal(data)
        return encodeRaw;
    }

    fun encodeData(data: ByteArray, certKeyPair: CertKeyPair): ByteArray {
        val aesKey = CryptUtils.randomBytes(CryptUtils.AES_GCM_KEY_SIZE)
        val cAesKey = this.cryptData(aesKey, certKeyPair)
        val buf = ByteArrayOutputStream()
        buf.write(cAesKey)
        val cData = CryptUtils.aesGCMEncrypt(data, aesKey)
        buf.write(cData)
        return buf.toByteArray()
    }

    fun signData(data: ByteArray, certKeyPair: CertKeyPair): ByteArray {
        if (certKeyPair.privateKey == null)
            throw RuntimeException("The cert key no private key")
        val keyPair = toKeyPair(certKeyPair)
        val signatureChecker = Signature.getInstance("SHA256withRSA")
        signatureChecker.initSign(keyPair.private)
        signatureChecker.update(data)
        return signatureChecker.sign()
    }

    fun verifySign(data: ByteArray, sign: ByteArray, certKeyPair: CertKeyPair): Boolean {
        val keyPair = toKeyPair(certKeyPair)
        val signatureChecker = Signature.getInstance("SHA256withRSA")
        signatureChecker.initVerify(keyPair.public)
        signatureChecker.update(data)
        return signatureChecker.verify(sign)
    }

    fun toKeyPair(keyPair: CertKeyPair): KeyPair {
        val pubkeyRaw = CryptUtils.base64decode(keyPair.publicKey)
        val keyFactory = KeyFactory.getInstance(ClientDeviceService.RSA_ALGORITHM)
        val pubkey = keyFactory.generatePublic(X509EncodedKeySpec(pubkeyRaw))
        var priKey: PrivateKey? = null
        if (keyPair.privateKey != null) {
            val prikeyRaw = CryptUtils.base64decode(keyPair.privateKey!!)
            priKey = keyFactory.generatePrivate(PKCS8EncodedKeySpec(prikeyRaw))
        }
        keyPair.keyLen = (pubkey as RSAPublicKey).modulus.bitLength()
        return KeyPair(pubkey, priKey)
    }

    fun newCertKey(__pubkey: String? = null, usedBy: CertKeyPair.UsedBy): CertKeyPair {

        if (__pubkey != null) {
            val _pubkey = __pubkey.split("\n").filter { it.trim().isNotEmpty() }.filter { !it.startsWith("-") }.joinToString("")
            val pair = CertKeyPair()
            var rawPubKey = CryptUtils.base64decode(_pubkey)
            pair.publicKey = _pubkey
            pair.usedBy = usedBy
            pair.publicKeyHash = CryptUtils.sha256hex(rawPubKey)
            pair._keyPair = toKeyPair(pair);
            return pair
        }
        val kpGen = KeyPairGenerator.getInstance(ClientDeviceService.RSA_ALGORITHM)
        kpGen.initialize(2048);
        var keyPair = kpGen.genKeyPair()
        var pubkey = CryptUtils.base64encode(keyPair.public.encoded)
        var prikey = CryptUtils.base64encode(keyPair.private.encoded)
        var pair = CertKeyPair()
        pair.privateKey = prikey
        pair.publicKey = pubkey
        pair.publicKeyHash = pubKeyHash(keyPair)
        pair.usedBy = usedBy
        pair._keyPair = keyPair
        pair.keyLen = 2048
        return pair
    }

}

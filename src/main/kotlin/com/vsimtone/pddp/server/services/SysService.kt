package com.vsimtone.pddp.server.services

import com.fasterxml.jackson.annotation.JsonIgnore
import com.vsimtone.pddp.server.configs.ServerConfig
import com.vsimtone.pddp.server.utils.*
import jakarta.annotation.PostConstruct
import org.apache.commons.lang3.StringUtils
import org.redisson.Redisson
import org.redisson.api.RMap
import org.redisson.api.RTopic
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.ApplicationEvent
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.ContextClosedEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import oshi.PlatformEnum
import oshi.SystemInfo
import oshi.hardware.NetworkIF
import java.io.Serializable
import java.util.*
import kotlin.concurrent.thread


@Service
class SysService : BaseService() {

    @Autowired
    lateinit var dbSchemaExportService: DBSchemaExportService

    companion object {

        const val REDIS_USED_BY = "pddp-server";

        var springAppReady = false;

        class AllServerMsgEvent(var msg: String) : ApplicationEvent(msg) {
        }

        class ServerNodeRuntimeConfig : Serializable {
            @JsonIgnore
            var applyTime = 0L;
            var cnfId: String? = null;
            var forHostName: String? = null;
            var forIP: String? = null;
            var enableScheduled = false;
            var enableDeviceDelaySave = false;
            var enableSysPerfMon = false;
            var enableProxyLoadBalance = false;
            override fun toString(): String {
                return "ServerNodeRuntimeConfig(cnfId=$cnfId, forHostName=$forHostName, forIP=$forIP, enableScheduled=$enableScheduled, enableDeviceDelaySave=$enableDeviceDelaySave, enableSysPerfMon=$enableSysPerfMon, enableProxyLoadBalance=$enableProxyLoadBalance)"
            }
        }

        val defaultServerNodeRuntimeConfig = ServerNodeRuntimeConfig();

        init {
            defaultServerNodeRuntimeConfig.cnfId = "code_default";
            defaultServerNodeRuntimeConfig.enableScheduled = true;
            defaultServerNodeRuntimeConfig.enableDeviceDelaySave = true;
            defaultServerNodeRuntimeConfig.enableSysPerfMon = true;
            defaultServerNodeRuntimeConfig.enableProxyLoadBalance = true;
        }

        class ServerNodeInfo : Serializable {
            var nodeId: Long = 0
            var id: String = ""
            var osName: PlatformEnum? = null
            var osArch: String? = null
            var osInfo: String? = null
            var machineId: String? = null

            var ipAddr: String? = null
            var hostName: String? = null

            var onlineTime: Long = 0

            var cpuUsage = 0.0

            var memTotal: Long = 0
            var memUsed: Long = 0

            var rootDiskTotal: Long = 0
            var rootDiskUsed: Long = 0

            var dataDiskTotal: Long = 0
            var dataDiskUsed: Long = 0

            var startedAt: Date? = null

            var version: String? = null

            var edgeNodeCount: Int = 0

            var licenseStatus = LicenseService.Companion.LicenseStatus.NoLicense

            var runtimeConfig = ServerNodeRuntimeConfig();

            constructor()

            constructor(id: String, nodeId: Long, onlineTime: Long) {
                this.id = id
                this.nodeId = nodeId
                this.onlineTime = onlineTime
            }

            override fun toString(): String {
                return "ServerNodeInfo(nodeId=$nodeId, id='$id', version=$version, osName=$osName, osArch=$osArch, osInfo=$osInfo, ipAddr=$ipAddr, hostName=$hostName, memTotal=$memTotal, rootDiskTotal=$rootDiskTotal, dataDiskTotal=$dataDiskTotal)"
            }
        }

        var INFO_ID = UUID.randomUUID().toString()
    }

    init {
        readyInvokeOrder = READY_INVOKE_HIGH;
    }

    @Autowired
    lateinit var redisson: Redisson

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var perfMonService: PerfMonService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var serverConfig: ServerConfig

    lateinit var serverNodes: RMap<String, ServerNodeInfo>

    lateinit var thisNodeInfo: ServerNodeInfo

    var thisNodeInfoReportTime: Long = -1

    @Autowired
    lateinit var idGeneratorService: IDGeneratorService

    @Autowired
    lateinit var applicationEventPublisher: ApplicationEventPublisher

    private lateinit var allServerMsgTopic: RTopic

    var nodeStartedAt = Date()
    var nodeId: Long = -1

    var useNetInter: NetworkIF? = null
    var useNetInterAddr: String? = null
    var prevNetSent: Long = 0
    var prevNetRecv: Long = 0

    fun setNetInterInfo() {
        val si = SystemInfo();
        val hal = si.hardware
        hal.networkIFs.forEach {
            val inter = it.queryNetworkInterface()
            val hitIpv4Addr = it.iPv4addr.find { !it.startsWith("169.254") }
            logger.info("Network interface ${it.name} loop:${inter.isLoopback} up:${inter.isUp} ${hitIpv4Addr}")
            if (!inter.isLoopback && inter.isUp && hitIpv4Addr != null) {
                useNetInterAddr = hitIpv4Addr
                useNetInter = it
                logger.info("Use network interface: ${useNetInter!!.name}, $useNetInterAddr")
                return@forEach
            }
        }
    }

    override fun onInit() {
        dbSchemaExportService.doGenerate();
    }


    @Scheduled(fixedDelay = 60 * 1000L)
    fun queueMonitor() {
        if (!ready) return
        var m = fun(name: String, size: Int) {
            if (size < 1000) return;
            else if (size < 3000) {
                logger.warn("$name : $size")
            } else {
                logger.error("$name : $size")
            }
        }
    }

    fun pushMsgToAllServers(msg: String) {
        this.logger.info("Push to all servers: ${msg}")
        this.allServerMsgTopic.publish(msg);
    }

    @EventListener(ApplicationReadyEvent::class)
    fun onSpringAppReady() {
        springAppReady = true;
        this.serverNodes.filter {
            it.value.hostName == this.thisNodeInfo.hostName && it.value.machineId == this.thisNodeInfo.machineId && it.key != this.thisNodeInfo.id
        }.map {
            this.serverNodes.remove(it.key)
            this.logger.info("Remove same old ${it.value}")
        }
    }

    @PostConstruct
    fun init() {
        Runtime.getRuntime().addShutdownHook(thread(start = false) {
            shutdowning = true
        })
        var usedByBucket = redisService.redisson.getBucket<String>("used_by", RedisUtils.autoCodec(String::class.java))
        var usedBy = usedByBucket.get()
        if (StringUtils.isEmpty(usedBy)) {
            usedByBucket.set(REDIS_USED_BY);
            usedBy = REDIS_USED_BY;
        }
        if (usedBy != REDIS_USED_BY) {
            throw RuntimeException("The redis database is used by ${usedBy}, please change redis db.")
        }
        serverNodes = redisService.redisson.getMap("main_server_nodes", RedisUtils.autoCodec(ServerNodeInfo::class.java, String::class.java))
        var locked = redisService.runInLock("server_node_started", 60L) {
            var ids = mutableListOf<Long>()
            serverNodes.forEach { id, node ->
                ids.add(node.nodeId)
            }
            do {
                nodeId = (Math.random() * 1024).toLong()
                logger.info("Server node id ${nodeId}")
            } while (ids.contains(nodeId))
        }
        if (!locked)
            throw RuntimeException("Sys service init fail. lock fail.")
        idGeneratorService.doInit(nodeId)
        autoRegisterThisNode()

        allServerMsgTopic = redisService.redisson.getTopic("server_msg", RedisUtils.autoCodec(String::class.java))
        allServerMsgTopic.addListener(String::class.java) { channel, msg ->
            var wait = 0;
            while (!springAppReady) {
                Thread.sleep(1000);
                wait++;
                if (shutdowning) return@addListener
                if (wait >= 10)
                    this.logger.info("Receive server msg: ${msg}, wait ready.")
            }
            this.logger.info("Receive server msg: ${msg}")
            try {
                applicationEventPublisher.publishEvent(AllServerMsgEvent(msg));
            } catch (e: Exception) {
                this.logger.error("Receive server msg: ${msg}, process error", e)
            }
        }
        schedule("clear_server_node", 30 * 1000L) {
            autoCleanServerNodes()
        }
    }

    @EventListener(ContextClosedEvent::class)
    fun onServerClosed(evt: ContextClosedEvent) {
        shutdowning = true
        this.logger.info("Server closed.");
    }

    @Scheduled(fixedDelay = 1000 * 10L)
    fun autoRegisterThisNode() {
        val interval = 10L;
        val info = ServerNodeInfo(INFO_ID, nodeId, System.currentTimeMillis())
        val workDir = System.getProperty("user.dir")
        info.startedAt = nodeStartedAt
        try {
            if (!ready) {
                setNetInterInfo()
            } else {
                info.licenseStatus = this.thisNodeInfo.licenseStatus;
                info.machineId = this.thisNodeInfo.machineId;
                info.runtimeConfig = this.thisNodeInfo.runtimeConfig;
            }
            val si = SystemInfo();
            val os = si.operatingSystem
            info.osName = SystemInfo.getCurrentPlatform()
            info.osArch = System.getProperty("os.arch")
            info.osInfo = os.toString()
            val hal = si.hardware
            val cpuCount = hal.processor.logicalProcessorCount
            info.memTotal = hal.memory.total
            info.memUsed = hal.memory.total - hal.memory.available
            info.version = serverConfig.version
            if (info.machineId == null) {
                val hardwareUuid = hal.computerSystem.hardwareUUID
                val sysSN = hal.computerSystem.serialNumber
                if (hardwareUuid == "unknown" || sysSN == "unknown") {
                    this.logger.info("MachineId1: ${hardwareUuid} ${sysSN} ${info.osName!!.name} ${info.osArch} ${useNetInter!!.macaddr}")
                    info.machineId = CryptUtils.sha256hex(info.osName!!.name + ":" + info.osArch + ":" + useNetInter!!.macaddr)
                    this.logger.info("MachineId1: ${info.machineId}")
                } else {
                    this.logger.info("MachineId2: ${hardwareUuid} ${sysSN} ${info.osName!!.name} ${info.osArch} ${useNetInter!!.macaddr}")
                    info.machineId = CryptUtils.sha256hex(hal.computerSystem.hardwareUUID + ":" + hal.computerSystem.serialNumber)
                    this.logger.info("MachineId2: ${info.machineId}")
                }
            }
            val fileStores = os.fileSystem.fileStores.toMutableList()
            fileStores.sortByDescending { it.mount.length }
            fileStores.forEach {
                if (it.mount == "/" && info.rootDiskUsed == 0L) {
                    info.rootDiskUsed = it.totalSpace - it.usableSpace
                    info.rootDiskTotal = it.totalSpace
                }
                if (workDir.startsWith(it.mount) && info.dataDiskUsed == 0L) {
                    info.dataDiskUsed = it.totalSpace - it.usableSpace
                    info.dataDiskTotal = it.totalSpace
                }
            }

            info.hostName = os.networkParams.hostName
            info.ipAddr = useNetInterAddr
            info.edgeNodeCount = edgeNodeService.wsSessions.size;
            var cpuLoads = hal.processor.getSystemLoadAverage(1)
            if (cpuLoads[0] > 0)
                info.cpuUsage = cpuLoads[0] / cpuCount * 100
            else
                info.cpuUsage = 0.0;

            if (ready && this.thisNodeInfo.runtimeConfig.applyTime <= System.currentTimeMillis() - 60 * 1000L) {
                val runtimeConfigs = JSONUtils.toObject(
                    this.appConfigService.getRawString("core.main_server.runtime_config") ?: "[]",
                    Array<ServerNodeRuntimeConfig>::class.java
                );
                var useCnf = runtimeConfigs.find { it.forHostName == info.hostName || it.forIP == info.ipAddr };
                if (useCnf == null)
                    useCnf = runtimeConfigs.find { it.forHostName == null && it.forIP == null }
                if (useCnf == null)
                    useCnf = defaultServerNodeRuntimeConfig;

                info.runtimeConfig.applyTime = Date().time

                if (info.runtimeConfig.cnfId != useCnf.cnfId) {
                    info.runtimeConfig = useCnf;
                    this.logger.info("Apply ${info.runtimeConfig}")
                }
            }

            thisNodeInfo = info

            if (!ready) return;
            if (this.thisNodeInfo.runtimeConfig.enableSysPerfMon) {
                perfMonService.addPerfMon(
                    "main_server:${info.hostName}",
                    "edge_node.count",
                    mapOf(),
                    info.edgeNodeCount.toDouble()
                )
                perfMonService.addPerfMon("main_server:${info.hostName}", "sys.cpu.usage", mapOf(), info.cpuUsage)
                perfMonService.addPerfMon(
                    "main_server:${info.hostName}",
                    "sys.mem.usage",
                    mapOf(),
                    info.memUsed.toDouble() / info.memTotal * 100.0
                )
                if (info.rootDiskTotal > 0)
                    perfMonService.addPerfMon(
                        "main_server:${info.hostName}",
                        "sys.root_disk.usage",
                        mapOf(),
                        info.rootDiskUsed.toDouble() / info.rootDiskTotal * 100.0
                    )
                if (info.dataDiskTotal > 0)
                    perfMonService.addPerfMon(
                        "main_server:${info.hostName}",
                        "sys.data_disk.usage",
                        mapOf(),
                        info.dataDiskUsed.toDouble() / info.dataDiskTotal * 100.0
                    )
                if (useNetInter != null && useNetInter!!.updateAttributes()) {
                    val bytesSent = useNetInter!!.bytesSent
                    val bytesRecv = useNetInter!!.bytesRecv
                    if (prevNetSent != 0L) {
                        perfMonService.addPerfMon(
                            "main_server:${info.hostName}",
                            "sys.net.sent",
                            mapOf(),
                            (bytesSent - prevNetSent) / interval.toDouble()
                        )
                    }
                    if (prevNetRecv != 0L) {
                        perfMonService.addPerfMon(
                            "main_server:${info.hostName}",
                            "sys.net.recv",
                            mapOf(),
                            (bytesRecv - prevNetRecv) / interval.toDouble()
                        )
                    }
                    prevNetRecv = bytesRecv
                    prevNetSent = bytesSent
                } else {
                    setNetInterInfo()
                    prevNetRecv = 0
                    prevNetSent = 0
                }
            }
        } catch (e: Exception) {
            logger.error("get info error", e)
        }
        if (System.currentTimeMillis() - thisNodeInfoReportTime >= 10 * 1000L) {
            thisNodeInfoReportTime = System.currentTimeMillis()
            serverNodes[INFO_ID] = info
        }
    }

    fun autoCleanServerNodes() {
        val removeNodes = mutableListOf<String>()
        serverNodes.forEach { id, info ->
            if (info.onlineTime <= System.currentTimeMillis() - 30 * 1000)
                removeNodes.add(id)
        }
        removeNodes.forEach {
            serverNodes.fastRemove(it)
        }
    }

}
package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.PageResult
import com.vsimtone.pddp.server.configs.SolrClientConfig
import com.vsimtone.pddp.server.entities.BaseEntity
import com.vsimtone.pddp.server.utils.JSONUtils
import org.apache.commons.lang3.StringUtils
import org.apache.solr.client.solrj.SolrClient
import org.apache.solr.client.solrj.SolrQuery
import org.apache.solr.client.solrj.SolrQuery.ORDER
import org.apache.solr.client.solrj.SolrRequest
import org.apache.solr.common.SolrInputDocument
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service


@Service
class SolrService : BaseService() {

    @Autowired
    lateinit var solrClient: SolrClient

    @Autowired
    lateinit var solrConfig: SolrClientConfig.Companion.SolrConfig

    @Autowired
    lateinit var redisService: RedisService

    companion object {
        class SolrQueryResult {
            var total = 0L
            var data = mutableListOf<MutableMap<String, Any>>();
            var offset = 0
            var limit = 0
        }
    }

    fun enabled(): Boolean {
        return solrConfig.url?.isNotEmpty() ?: false
    }

    override fun onInit() {
        super.onInit()
        if (!enabled()) return;
        val pingResp = solrClient.ping();
        this.logger.info("Solr connected ${solrConfig.url}, user=${solrConfig.user}, core=${solrConfig.core}, ping=${pingResp}");
    }

    fun solrQueryParams(params: Map<String, String>): String {
        val p = mutableListOf<String>()
        params.forEach {
            if (!StringUtils.isAllBlank(it.value)) p.add(it.key + ":" + it.value);
        }
        return p.joinToString(" and ");
    }

    fun solrQuery(params: String, pageAttr: PageAttr): PageResult<MutableMap<String, Any>> {
        val startedAt = System.currentTimeMillis();
        val query = SolrQuery();
        val page = pageAttr.get();
        query.setQuery(params)
        query.setStart(page.offset.toInt())
        query.setRows(page.pageSize)
        page.sort.forEach {
            if (it.isAscending)
                query.addSort(it.property, ORDER.asc)
            else
                query.addSort(it.property, ORDER.desc)
        }
        val rawResults = solrClient.query(query, SolrRequest.METHOD.POST).results;
        val results = mutableListOf<MutableMap<String, Any>>()
        rawResults.forEach {
            val item = mutableMapOf<String, Any>()
            it.fieldNames.forEach { name ->
                item[name] = it.getFieldValue(name)
            }
            results.add(item)
        }
        val used = System.currentTimeMillis() - startedAt;
        if (used > 1000) {
            this.logger.info("solr query slow, params=${query.toQueryString()}, used=${used / 1000}s")
        }

        return PageResult(results, page, rawResults.numFound);
    }

    fun flattenMap(prefix: String, map: Map<String, Any?>): Map<String, Any?> {
        val flatMap = mutableMapOf<String, Any?>()
        map.forEach { (key, value) ->
            val fullKey = if (prefix.isEmpty()) key else "$prefix.$key"
            when (value) {
                is Map<*, *> -> {
                    val flattened = flattenMap(fullKey, value as Map<String, Any?>)
                    flatMap.putAll(flattened)
                }

                is List<*> -> {
                    flattenList(fullKey, value as List<Any?>, flatMap)
                }

                else -> {
                    flatMap[fullKey] = value
                }
            }
        }
        return flatMap
    }

    fun flattenList(prefix: String, list: List<Any?>, flatMap: MutableMap<String, Any?>) {
        list.forEachIndexed { index, item ->
            val fullKey = "${prefix}[${index}]"
            when (item) {
                is Map<*, *> -> {
                    val flattened = flattenMap(fullKey, item as Map<String, Any?>)
                    flatMap.putAll(flattened)
                }

                is List<*> -> {
                    flattenList(fullKey, item as List<Any?>, flatMap)
                }

                else -> {
                    flatMap[fullKey] = item
                }
            }
        }
    }

    fun <T : BaseEntity> save(data: T) {
        val entityType = entityManager.metamodel.entity<Any>(data.javaClass);
        val tableName = entityType.name.lowercase();
        val doc = SolrInputDocument();
        val dataRAW = JSONUtils.toString(data).replace("\"id\"", "\"_db_id\"");
        val dataMap = JSONUtils.toObject(dataRAW, Map::class.java).toMutableMap() as MutableMap<String, Any?>;
        doc.setField("_db_type", tableName);
        flattenMap("", dataMap).forEach { (key, value) ->
            doc.setField(key, value);
        }
        solrClient.add(doc);
    }

    fun commit() {
        solrClient.commit();
    }
}

package com.vsimtone.pddp.server.services

import com.sun.tools.javac.tree.TreeInfo.args
import com.vsimtone.pddp.server.entities.CertKeyPair
import com.vsimtone.pddp.server.entities.EdgeNode
import com.vsimtone.pddp.server.entities.EdgeNodeCluster
import com.vsimtone.pddp.server.entities.FileInfo
import com.vsimtone.pddp.server.entities.QEdgeNodeCluster
import com.vsimtone.pddp.server.repositories.EdgeNodeClusterRepository
import org.redisson.Redisson
import org.redisson.api.RMap
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.net.URL
import java.net.URLEncoder

@Service
class EdgeNodeClusterService : BaseCrudService<EdgeNodeCluster, EdgeNodeClusterRepository>(QEdgeNodeCluster.a) {
    @Autowired
    lateinit var redisson: Redisson

    @Autowired
    lateinit var certKeyPairService: CertKeyPairService

    @Autowired
    @Lazy
    lateinit var edgeNodeService: EdgeNodeService

    fun newDownloadAuthKey(file: FileInfo): String {
        return ""
    }

    @Transactional(propagation = Propagation.MANDATORY)
    fun nodeJoin(cluster: EdgeNodeCluster, node: EdgeNode, remark: String) {
        if (node.cluster != null) {
            throw RuntimeException("node already exists")
        }
        node.cluster = cluster
        if (cluster.certKeyPair == null) {
            cluster.certKeyPair = certKeyPairService.newCertKey(node.clusterPubkey, CertKeyPair.UsedBy.EdgeNode)
            certKeyPairService.save(cluster.certKeyPair!!)
        }
        edgeNodeService.save(node)
        cluster.configVersion++
        save(cluster)
        this.logger.info("node join. remark=${remark}, cluster=${cluster}, node=${node}")
    }

    @Transactional(propagation = Propagation.MANDATORY)
    fun nodeUpdate(cluster: EdgeNodeCluster, node: EdgeNode, remark: String) {
        cluster.configVersion++
        save(cluster)
        this.logger.info("node update. remark=${remark}, cluster=${cluster}, node=${node}")
    }
    
    fun getNodeUrls(cluster: EdgeNodeCluster): MutableList<String> {
        return edgeNodeService.findByCluster(cluster).map {
            "vped://" + it.nodeHost + "?pubkey=" + URLEncoder.encode(it.certKeyPair.publicKey, Charsets.UTF_8.name())
        }.toMutableList()
    }
}

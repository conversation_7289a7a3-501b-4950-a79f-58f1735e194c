package com.vsimtone.pddp.server.services

import com.fasterxml.jackson.annotation.JsonProperty
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.repositories.BOCP6TableRepository
import com.vsimtone.pddp.server.utils.JSONUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.text.SimpleDateFormat
import java.util.zip.GZIPInputStream

@Service
class BOCP6TableService : BaseCrudService<BOCP6Table, BOCP6TableRepository>(QBOCP6Table.a) {

    @Autowired
    lateinit var externalFileSystemService: ExternalFileSystemService

    @Autowired
    lateinit var appConfigService: APPConfigService


    companion object {

        const val CNF_KEY_DELETE_USER_STATES = "core.boc_p6.delete_user_states";
        const val CNF_KEY_DELETE_ORG_STATES = "core.boc_p6.delete_org_states";
//        const val CNF_KEY_ORG_HIER_TYPES_STATES = "core.boc_p6.org_hier_types";

        const val BOC_TABLE_PREFIX_BOC_ORG = "C05011.26000000.ORg.";
        const val BOC_TABLE_PREFIX_BOC_ORG_HIER = "C05011.26000000.LEv.";
        const val BOC_TABLE_PREFIX_EMP = "C09911.26000000.EMP.";

        const val BOC_TABLE_FULL_FLAG = "0";
        const val BOC_TABLE_PART_FLAG = "1";

        class BocP6Result {
            val orgs = mutableListOf<UserService.OutOrgItem>()
            val users = mutableListOf<UserService.OutUserItem>()
        }

        class BocP6DIIPControl : java.io.Serializable {
            @get:JsonProperty("TabName")
            var TabName = ""

            @get:JsonProperty("AssemblyID")
            var AssemblyID = ""

            @get:JsonProperty("PlatformID")
            var PlatformID = ""

            @get:JsonProperty("IsSplitFlag")
            var IsSplitFlag = ""

            @get:JsonProperty("MechanismID")
            var MechanismID = ""

            @get:JsonProperty("DataStartDate")
            var DataStartDate = ""

            @get:JsonProperty("DataEndDate")
            var DataEndDate = ""

            @get:JsonProperty("IncID")
            var IncID = ""

            @get:JsonProperty("RecNum")
            var RecNum = ""

            @get:JsonProperty("Sep")
            var Sep = ""

            @get:JsonProperty("CycFlag")
            var CycFlag = ""

            @get:JsonProperty("TableGenType")
            var TableGenType = ""

            @get:JsonProperty("GenTime")
            var GenTime = ""
            override fun toString(): String {
                return "BocP6DIIPControl(TabName='$TabName', AssemblyID='$AssemblyID', IsSplitFlag='$IsSplitFlag', DataStartDate='$DataStartDate', DataEndDate='$DataEndDate', RecNum='$RecNum', TableGenType='$TableGenType', GenTime='$GenTime')"
            }
        }

        class BocP6TableData {
            var fileName = ""
            var fileTime = 0L
            var fileIsFull = false
            var data = mutableListOf<List<String>>()
            var diipControl = BocP6DIIPControl()
        }
    }

    override fun onReady() {
        this.appConfigService.init(CNF_KEY_DELETE_ORG_STATES, JSONUtils.toString(arrayOf("6")), APPConfig.Type.JsonArray);
        this.appConfigService.init(CNF_KEY_DELETE_USER_STATES, JSONUtils.toString(arrayOf("3", "4")), APPConfig.Type.JsonArray);
//        this.appConfigService.init(CNF_KEY_ORG_HIER_TYPES_STATES, JSONUtils.toString(arrayOf("0")), APPConfig.Type.JsonArray);
    }

    private fun loadBocP6Table(fileName: String, buf: ByteArray): BocP6TableData {
        this.logger.info("Start load p6 table ${fileName} ${buf.size} bytes")
        var rowDemiter = "\n";
        var diipControlPrefix = "|||diip-control|||";
        var data = BocP6TableData();
        data.fileName = fileName;
        var rawContent = buf.toString(Charsets.UTF_8)
        var rawLines = rawContent.split(rowDemiter).filter { it.trim().isNotBlank() }
        var diipControlRaw = (rawLines.find { it.startsWith(diipControlPrefix) } ?: throw RuntimeException("'${diipControlPrefix}' not found in file ${fileName} ")).substring(diipControlPrefix.length)
        try {
            var diipControl = JSONUtils.toObject(diipControlRaw, BocP6DIIPControl::class.java)
            data.diipControl = diipControl
        } catch (e: Exception) {
            throw RuntimeException("${fileName} Parse json '${diipControlRaw}' error", e)
        }
        if (data.diipControl.Sep.isBlank() || data.diipControl.TabName.isBlank())
            throw RuntimeException("'${diipControlRaw}' format error in file ${fileName} ")
        rawLines.forEach {
            if (it.startsWith(diipControlPrefix)) return@forEach
            val colsRaw = it.split(data.diipControl.Sep).map { it2 -> it2.trim() }
            data.data.add(colsRaw)
        }
        var fileNamePart = data.fileName.split(".")[3]
        try {
            data.fileTime = SimpleDateFormat("YYYYMMDD").parse(fileNamePart.substring(0, 8)).time
        } catch (e: Exception) {
            throw RuntimeException("Parse file name '${fileName}' error: file time '${fileNamePart.substring(0, 8)}' parse error", e)
        }
        data.fileIsFull = fileNamePart.substring(6) == BOC_TABLE_FULL_FLAG
        this.logger.info("Success load p6 table ${fileName} : ${data.diipControl}, ${data.data.size} lines.")
        return data;
    }

    private fun syncTable(cnf: LdapConfig, result: BocP6Result) {
        val externalFileSystem = ExternalFileSystem()
        externalFileSystem.type = cnf.bocP6FSType
        externalFileSystem.host = cnf.host
        externalFileSystem.port = cnf.port
        externalFileSystem.rootPath = "/"
        externalFileSystem.username = cnf.username
        externalFileSystem.password = cnf.password

        val localData = findAll().groupBy { it.type }.mapValues {
            val m = mutableMapOf<String, BOCP6Table>()
            it.value.forEach { it2 -> m[it2.key] = it2 }
            m
        }.toMutableMap()
        BOCP6Table.Type.values().forEach {
            if (localData[it] == null) localData[it] = mutableMapOf()
            this.logger.info("Load from db: ${localData[it]!!.size} $it")
        }
        val bocP6Tables = mutableListOf<BocP6TableData>()
        val bocP6TablePrefixList = listOf(BOC_TABLE_PREFIX_EMP, BOC_TABLE_PREFIX_BOC_ORG, BOC_TABLE_PREFIX_BOC_ORG_HIER)
        externalFileSystemService.findFiles(externalFileSystem, cnf.bocP6RootPath!!, 1000) { f, files ->
            if (f == null) return@findFiles false
            val filePath = f.name.path
            val fileName = f.name.baseName
            val fileExt = f.name.extension
            if (fileExt != "gz") {
                logger.info("Ignore ${filePath},reason: extension not gz.")
                return@findFiles false
            }

            if (bocP6TablePrefixList.find { fileName.startsWith(it) } == null) {
                logger.info("Ignore ${filePath}, reason: prefix unknown.")
                return@findFiles false
            }

            val fileNameParts = fileName.split(".")
            if (fileNameParts.size != 5) {
                logger.info("Ignore ${filePath}, reason: the file name format is incorrect.")
                return@findFiles false
            }

            if (fileNameParts[3].matches(Regex("\\d{6}[01]"))) {
                logger.info("Ignore ${filePath}, reason: the format of the date and increment part of the file name is wrong.")
                return@findFiles false
            }

            if (localData[BOCP6Table.Type.SYNC_FILE]!![fileName] != null) {
                logger.info("Ignore ${filePath}, reason: sync record exists.")
                return@findFiles false
            }

            bocP6Tables.add(loadBocP6Table(fileName, GZIPInputStream(ByteArrayInputStream(f.content.byteArray)).readAllBytes()))
            false
        }
        bocP6Tables.sortWith() { a: BocP6TableData, b: BocP6TableData ->
            var aScore = 0
            var bScore = 0
            if (a.fileIsFull) aScore++
            if (b.fileIsFull) bScore++
            if (aScore != bScore) return@sortWith aScore - bScore
            return@sortWith (a.fileTime - b.fileTime).toInt()
        }
        val localDataIsValidate = mutableMapOf<Long, Boolean>()
        localData.values.forEach {
            it.values.forEach {
                if (!it.isValidData) localDataIsValidate[it.id!!] = false;
            }
        }
        val localUserMap = localData[BOCP6Table.Type.USER_DATA]!!
        val localOrgMap = localData[BOCP6Table.Type.ORG_DATA]!!
        if (bocP6Tables.size == 0) {
            this.logger.info("Not found any new p6 table.")
        } else {
            bocP6Tables.forEach {
                val localTab = BOCP6Table()
                localTab.type = BOCP6Table.Type.SYNC_FILE
                localTab.key = it.fileName
                localTab.name = it.fileName
                localTab.raw = JSONUtils.toString(it.diipControl)
                this.save(localTab)
                this.repo.flush()
                localData[BOCP6Table.Type.SYNC_FILE]!![localTab.key] = localTab
            }
            val bocOrgHiers = bocP6Tables.filter { it.diipControl.TabName == "boc_org_hier" }
            val bocEmployees = bocP6Tables.filter { it.diipControl.TabName == "tbl_employee_info" }
            val bocOrgs = bocP6Tables.filter { it.diipControl.TabName == "boc_org" }

            val deletedBocOrgSts = this.appConfigService.getList(CNF_KEY_DELETE_ORG_STATES) as List<String>
            val deletedBocUserSts = this.appConfigService.getList(CNF_KEY_DELETE_USER_STATES) as List<String>
            val orgHierTypes = listOf("0"); // this.appConfigService.getList(CNF_KEY_ORG_HIER_TYPES_STATES) as List<String>

            this.logger.info("delete org states : ${deletedBocOrgSts.joinToString(",")}")
            this.logger.info("delete user states : ${deletedBocUserSts.joinToString(",")}")
            this.logger.info("org hier types : ${orgHierTypes.joinToString(",")}")

            bocEmployees.forEach { bocEmployee ->
                bocEmployee.data.forEachIndexed { idx, it ->
                    if (it.size < 35) {
                        this.logger.warn("employee line column wrong, ignore, file=${bocEmployee.fileName}, idx=${idx}, data=${it.joinToString(",")}")
                        return@forEachIndexed
                    }
                    val uid = it[0]
                    var nickname = it[2] + it[3]
                    if (nickname.length <= 1) nickname = it[1]
                    val email = it[47]
                    val org = it[28]
                    val deleted = deletedBocUserSts.contains(it[34]) // 默认2正常
                    val localUser = localUserMap[uid] ?: BOCP6Table()
                    localUser.type = BOCP6Table.Type.USER_DATA
                    localUser.key = uid
                    localUser.name = nickname
                    localUser.email = email
                    localUser.parent = org
                    localUser.userLastSeen = bocEmployee.fileName + ":" + idx
                    if (localUser.parent!!.isEmpty()) localUser.parent = null
                    localUser.raw = JSONUtils.toString(it)
                    localUser.updatedCount++
                    if (deleted) {
                        if (localUser.id != null) this.delete(localUser)
                        localUserMap.remove(uid)
                        this.logger.info("deleted $localUser")
                    } else {
                        this.save(localUser)
                        localUserMap[uid] = localUser;
                    }
                }
                this.repo.flush();
                this.logger.info("${bocEmployee.fileName} saved ${bocEmployee.data.size} user");
            }
            bocOrgs.forEach { bocOrg ->
                bocOrg.data.forEachIndexed { idx, it1 ->
                    if (it1.size < 21) {
                        this.logger.warn("org line column wrong, ignore, file=${bocOrg.fileName}, idx=${idx}, data=${it1.joinToString(",")}")
                        return@forEachIndexed
                    }
                    val id = it1[0]
                    val name = it1[1]
                    val deleted = deletedBocOrgSts.contains(it1[20])
                    val localOrg = localOrgMap[id] ?: BOCP6Table()
                    localOrg.key = id
                    localOrg.type = BOCP6Table.Type.ORG_DATA
                    localOrg.name = name
                    localOrg.orgLastSeen = bocOrg.fileName + ":" + idx
                    localOrg.raw = JSONUtils.toString(it1)
                    localOrg.updatedCount++
                    if (deleted) {
                        if (localOrg.id != null) this.delete(localOrg)
                        localOrgMap.remove(id)
                        this.logger.info("deleted $localOrg")
                    } else {
                        this.save(localOrg)
                        localOrgMap[id] = localOrg
                    }
                }
                this.repo.flush();
                this.logger.info("${bocOrg.fileName} saved ${bocOrg.data.size} org");
            }
            bocOrgHiers.forEach { bocOrgHier ->
                var skipOrgHierRecords = 0;
                bocOrgHier.data.forEachIndexed { idx, it ->
                    if (it.size < 4) {
                        this.logger.warn("org hier line column wrong, ignore, file=${bocOrgHier.fileName}, idx=${idx}, data=${it.joinToString(",")}")
                        skipOrgHierRecords++;
                        return@forEachIndexed
                    }
                    val id = it[0]
                    val hierType = it[2]
                    val parentId = it[3]
                    if (!orgHierTypes.contains(hierType)) {
                        skipOrgHierRecords++;
                        return@forEachIndexed
                    }
                    if (id.isEmpty()) {
                        this.logger.warn("org hier id is empty, ignore, file=${bocOrgHier.fileName}, idx=${idx}, data=${it.joinToString(",")}")
                        return@forEachIndexed
                    }
                    if (id == parentId) {
                        this.logger.warn("org hier parent is it self, ignore, file=${bocOrgHier.fileName}, idx=${idx}, data=${it.joinToString(",")}")
                        return@forEachIndexed
                    }
                    val localOrg = localOrgMap[id] ?: BOCP6Table()
                    localOrg.key = id
                    localOrg.type = BOCP6Table.Type.ORG_DATA
                    localOrg.parent = parentId
                    localOrg.orgHierLastSeen = bocOrgHier.fileName + ":" + idx
                    if (localOrg.parent!!.isEmpty()) localOrg.parent = null
                    this.save(localOrg)
                    localOrgMap[id] = localOrg
                }
                this.repo.flush();
                this.logger.info("${bocOrgHier.fileName} saved ${bocOrgHier.data.size} org_hiers, skip ${skipOrgHierRecords} org_hiers");
            }
        }
        val checkedOrgValidate = mutableMapOf<Long, Boolean>()
        fun checkAndSaveOrg(org: BOCP6Table, repeats: MutableList<String> = mutableListOf<String>()) {
            if (checkedOrgValidate.containsKey(org.id!!)) return;
            var isValidData = org.name != null && org.name!!.isNotEmpty() && org.key.isNotEmpty();
            if (repeats.contains(org.key)) {
                this.logger.warn("Repeated org $org, ${repeats.joinToString("|")}")
                isValidData = false;
            } else {
                repeats.add(org.key)
            }
            if (isValidData && org.parent != null) {
                val parent = localOrgMap[org.parent];
                if (parent == null) {
                    isValidData = false;
                } else {
                    checkAndSaveOrg(parent, repeats);
                    isValidData = parent.isValidData;
                }
            }
            org.isValidData = isValidData;
            checkedOrgValidate[org.id!!] = isValidData;
            if (org.isValidData != localDataIsValidate[org.id!!]) {
                this.save(org)
            }
        }
        localOrgMap.values.forEach {
            checkAndSaveOrg(it);
        }
        this.repo.flush();
        this.logger.info("check and save ${localOrgMap.size} org done");
        localUserMap.values.forEach {
            var isValidData = it.name != null && it.name!!.isNotEmpty() && it.key.isNotEmpty();
            if (isValidData && it.parent != null) {
                val parent = localOrgMap[it.parent]
                isValidData = parent?.isValidData ?: false
            } else {
                isValidData = false;
            }
            it.isValidData = isValidData;
            if (it.isValidData != localDataIsValidate[it.id!!]) {
                this.save(it)
            }
        }
        this.repo.flush();
        this.logger.info("check and save ${localUserMap.size} user done");
        result.users.addAll(localUserMap.values.filter { it.isValidData }.map {
            val outUser = UserService.OutUserItem()
            outUser.uid = it.key + "@" + cnf.domain
            outUser.email = it.email ?: ""
            outUser.nickname = it.name ?: ""
            outUser.org = it.parent
            outUser
        })
        result.orgs.addAll(localOrgMap.values.filter { it.isValidData }.map {
            val outOrg = UserService.OutOrgItem()
            outOrg.id = it.key
            outOrg.name = it.name ?: ""
            outOrg.parent = it.parent
            outOrg
        })
        this.logger.info("Load all p6 data: org ${result.orgs.size}/${localOrgMap.size}, user ${result.users.size}/${localUserMap.size}")
    }


    fun loadBocP6OrgAndUsers(cnf: LdapConfig): BocP6Result {
        val result = BocP6Result()
        runInTran {
            try {
                this.syncTable(cnf, result);
            } catch (e: Exception) {
                throw RuntimeException("Sync BOC P6 Error", e)
            }
        }
        return result
    }
}

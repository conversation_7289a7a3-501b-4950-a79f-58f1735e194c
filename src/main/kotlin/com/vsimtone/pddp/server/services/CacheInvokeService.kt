package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.utils.DateUtil
import com.vsimtone.pddp.server.utils.JSONUtils
import org.redisson.api.LocalCachedMapOptions
import org.redisson.api.RLocalCachedMap
import org.redisson.codec.TypedJsonJacksonCodec
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.util.concurrent.ConcurrentHashMap

@Service
class CacheInvokeService : BaseService() {
    companion object {
        class CacheItem : java.io.Serializable {
            var getCount = 0L
            var updateCount = 0L
            var lastGetAt = 0L
            var updatedAt = 0L
            var needRefresh = false
            lateinit var dataCls: String
            lateinit var dataRaw: String
            lateinit var serverId: String
        }

        // 清理时间
        val CLEAN_TIME = DateUtil.day(15)

    }

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var sysService: SysService

    lateinit var cacheItems: RLocalCachedMap<String, CacheItem>

    var cacheInvokes = ConcurrentHashMap<String, () -> Any?>()

    override fun onReady() {
        cacheItems = redisService.redisson.getLocalCachedMap("cache:invoke:items", TypedJsonJacksonCodec(String::class.java, CacheItem::class.java), LocalCachedMapOptions.defaults())
        schedule("cache:invoke:clean", DateUtil.minute(1)) {
            this.cleanCache()
        }
    }

    @Scheduled(fixedDelay = 10 * 1000L, initialDelay = 10 * 1000L)
    fun refreshCache() {
        if (!ready) return;
        val cacheNames = cacheItems.filter { it.value.serverId == sysService.thisNodeInfo.id }.keys
        cacheNames.forEach { name ->
            if (cacheInvokes[name] == null) {
                logger.error("Cache invoke func lost.delete cache")
                cacheItems.fastRemove(name);
                return@forEach
            }
            val cacheItemNoLock = cacheItems[name] ?: return@forEach
            if (!cacheItemNoLock.needRefresh) {
                if (cacheItemNoLock.lastGetAt / (24 * 3600 * 1000) == System.currentTimeMillis() / (24 * 3600 * 1000)) {
                    return@forEach
                }
            }
            val now = System.currentTimeMillis()
            val dataRaw = JSONUtils.toString(cacheInvokes[name]!!()!!)
            val used = DateUtil.useTimeToHum(System.currentTimeMillis() - now)
            redisService.runInLock("cache:invoke:${name}", 60) {
                var cacheItem = cacheItems[name] ?: return@runInLock
                if (cacheItem.updatedAt != cacheItemNoLock.updatedAt) return@runInLock
                cacheItem.dataRaw = dataRaw
                cacheItem.updatedAt = System.currentTimeMillis()
                cacheItem.needRefresh = false
                cacheItems.put(name, cacheItem)
                logger.info("Refresh cache ${name}, use ${used}");
            }
        }
        cacheInvokes.keys.forEach {
            if (!cacheNames.contains(it)) {
                cacheInvokes.remove(it)
                logger.info("Remove invoke func cache ${it}")
            }
        }
    }

    fun cleanCache() {
        cacheItems.forEach { name, item ->
            if (System.currentTimeMillis() - item.lastGetAt > CLEAN_TIME) {
                cacheItems.remove(name);
                cacheInvokes.remove(name);
                logger.info("Clean cache ${name}");
            }
        }
    }

    fun <T : Any> cacheInvoke(name: String, refresh: Boolean, cb: () -> T): T? {
        if (System.getenv("PDDP_IGNORE_CACHE") != null) {
            logger.warn("Ignore cache invoke: ${name}")
            return cb();
        }
        var getCache: CacheItem? = null
        redisService.runInLock("cache:invoke:${name}", 60) {
            var cacheItem = cacheItems[name]
            if (cacheItem == null) {
                var value = cb();
                cacheItem = CacheItem();
                cacheItem.dataCls = value.javaClass.name;
                cacheItem.dataRaw = JSONUtils.toString(value);
                cacheItem.updatedAt = System.currentTimeMillis()
                cacheItem.updateCount++
                cacheItem.serverId = sysService.thisNodeInfo.id;
                logger.info("New cache invoke ${name}")
            } else {
                cacheItem.getCount++
                if (!sysService.serverNodes.containsKey(cacheItem.serverId))
                    cacheItem.serverId = sysService.thisNodeInfo.id;
            }
            cacheItem.lastGetAt = System.currentTimeMillis()
            cacheItem.needRefresh = refresh;
            cacheInvokes[name] = cb;
            cacheItems[name] = cacheItem
            getCache = cacheItem
        }
        if (getCache != null)
            return JSONUtils.toObject(getCache!!.dataRaw, Class.forName(getCache!!.dataCls)) as T?
        return null;
    }
}
package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.utils.JSONUtils
import jakarta.servlet.http.HttpSession
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.*

@Service
class CaptchaService {

    companion object {
        enum class Type { Image }

        val SMS_SEND_CACHE_TIMEOUT = 1000 * 90L
        val CAPTCHA_STRING_CHARS = "qwertyupasdfghjkzxcvbnmQWERTYUPASDFGHJKLZXCVBNM123456789"
        val CAPTCHA_NUMBER_CHARS = "0123456789"
        val SESSION_ATTR_NAME = "captcha:"

    }

    @Autowired
    private lateinit var redisService: RedisService

    private val logger = LoggerFactory.getLogger("captchaService")

    open class CaptchaData() {
        var code: String = ""
        var time: Long = System.currentTimeMillis()
        var type: Type = Type.Image
        var to: String = ""
        var reason: String = ""
        val timeout = 10 * 1000 * 60L
        override fun equals(other: Any?): Boolean {
            if (other !is CaptchaData) return false;
            if (!code.equals(other.code, true)) return false;
            if (other.time - time >= timeout) return false;
            if (reason != other.reason) return false;
            if (to != other.to) return false;
            if (type != other.type) return false;
            return true;
        }

        private fun sessionKey(): String {
            return SESSION_ATTR_NAME + type.toString().lowercase(Locale.getDefault());
        }

        fun writeToSession(session: HttpSession) {
            session.setAttribute(sessionKey(), JSONUtils.toString(this));
        }

        fun validate(session: HttpSession): Boolean {
            var data = session.getAttribute(sessionKey()) as String?
            if (data == null) return false;
            var ok = JSONUtils.toObject(data, CaptchaData::class.java) == this;
            if (ok) {
                session.removeAttribute(sessionKey())
            }
            return ok;
        }
    }

    fun getCode(isNum: Boolean, length: Int): String {
        var code = ""
        var rnd = Random(System.currentTimeMillis())
        for (i in 1..length) {
            if (isNum)
                code += CAPTCHA_NUMBER_CHARS[(rnd.nextInt(CAPTCHA_NUMBER_CHARS.length)).toInt()]
            else
                code += CAPTCHA_STRING_CHARS[(rnd.nextInt(CAPTCHA_STRING_CHARS.length)).toInt()]
        }
        return code
    }

    fun generateImage(session: HttpSession): String {
        var code = getCode(false, 4)
        var data = CaptchaData()
        data.code = code
        data.writeToSession(session)
        return code
    }

    fun validateImage(session: HttpSession, code: String): Boolean {
        var data = CaptchaData()
        data.code = code
        return data.validate(session);
    }

}
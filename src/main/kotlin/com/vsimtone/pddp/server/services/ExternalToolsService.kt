package com.vsimtone.pddp.server.services

import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.util.ResourceUtils
import oshi.PlatformEnum
import java.io.File
import java.io.InputStream
import java.io.InputStreamReader
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

@Service
class ExternalToolsService : BaseService() {

    @Autowired
    lateinit var sysService: SysService

    lateinit var toolsDir: File;

    lateinit var osArchDir: File;

    companion object {
        class RunOpts() {
            var workDir: String? = null
            var envs: Map<String, String>? = null
            var stdin: String? = null
            var waitExitSeconds = 60L
            var encoding = Charsets.UTF_8
            var onStdout: ((data: String) -> Unit)? = null
            var onStderr: ((data: String) -> Unit)? = null
        }

        val defaultRunOpts = RunOpts()
    }

    init {
        readyInvokeOrder = READY_INVOKE_HIGH;
    }

    override fun onReady() {
        val osName = mapOf(
            PlatformEnum.LINUX to "linux",
            PlatformEnum.MACOS to "darwin",
            PlatformEnum.WINDOWS to "win",
        ).get(sysService.thisNodeInfo.osName)
        var osArch = System.getProperty("os.arch")
        if (osArch == "amd64")
            osArch = "x86_64"
        toolsDir = ResourceUtils.getFile("classpath:tools");
        osArchDir = File(toolsDir.path + File.separator + "${osName}_${osArch}");
        this.logger.info("Tools path ${printPath(toolsDir.path)} ${printPath(osArchDir.path)}")
    }

    fun toolPathPrefix(): String {
        return File("").absolutePath;
    }

    fun printPath(path: String): String {
        return path.replace(toolPathPrefix() + File.separator, "")
    }


    fun runTool(cmd: String, args: Array<String>, opts: RunOpts = defaultRunOpts): String {
        val pb = ProcessBuilder().command(cmd, *args)
        if (opts.workDir != null)
            pb.directory(File(opts.workDir!!))
        val p = pb.start()
        if (opts.stdin != null) {
            p.outputStream.write(opts.stdin!!.toByteArray(Charsets.UTF_8));
            p.outputStream.close();
        }
        val stdouts = StringBuffer()
        val stderrs = StringBuffer()
        val readThreads = mutableListOf<Thread>()
        val readFunc = fun(s: InputStream, out: StringBuffer, outFunc: ((data: String) -> Unit)?) {
            readThreads.add(thread {
                val reader = IOUtils.toBufferedReader(InputStreamReader(s, opts.encoding))
                while (true) {
                    val line = reader.readLine() ?: break
                    if (outFunc != null)
                        outFunc(line)
                    else
                        out.append(line + "\n");
                }
            })
        }
        readFunc(p.inputStream, stdouts, opts.onStdout)
        readFunc(p.errorStream, stderrs, opts.onStderr)
        val exited = p.waitFor(opts.waitExitSeconds, TimeUnit.SECONDS)
        readThreads.forEach { it.join() }
        if (!exited) p.destroy()
        if (p.exitValue() == 0) {
            return stdouts.toString()
        }
        throw RuntimeException("Run tool ${printPath(cmd)} error: exit code is ${p.exitValue()}, ${printPath(stderrs.toString())}")
    }

    fun getTool(vararg binNames: String): String {
        var toolFilePath = "";
        binNames.forEach { binName ->
            if (toolFilePath.isNotEmpty()) return@forEach
            toolFilePath = mutableListOf(toolsDir.canonicalPath + File.separator + binName, osArchDir.canonicalPath + File.separator + binName).find {
                File(it).exists()
            } ?: ""
        }
        if (toolFilePath.isEmpty())
            throw RuntimeException("Tools ${binNames.joinToString(",")} not found.")
        return toolFilePath
    }
}
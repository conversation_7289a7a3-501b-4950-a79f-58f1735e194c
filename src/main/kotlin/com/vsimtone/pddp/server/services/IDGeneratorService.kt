package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.entities.BaseEntity
import com.vsimtone.pddp.server.utils.SnowflakeID
import org.hibernate.HibernateException
import org.hibernate.engine.spi.SharedSessionContractImplementor
import org.hibernate.id.IdentifierGenerator
import org.hibernate.service.ServiceRegistry
import org.hibernate.type.Type
import org.springframework.stereotype.Service
import java.io.Serializable
import java.util.*

@Service
class IDGeneratorService {

    lateinit var snowflakeID: SnowflakeID

    companion object {
        const val IDGeneratorImplName = "com.vsimtone.pddp.server.services.IDGeneratorService\$Impl"
    }

    fun doInit(nodeId: Long) {
        snowflakeID = SnowflakeID(nodeId)
    }

    class Impl : IdentifierGenerator {

        private lateinit var name: String

        var service: IDGeneratorService? = null

        @Throws(HibernateException::class)
        override fun generate(session: SharedSessionContractImplementor, data: Any): Serializable {
            if (service == null)
                service = BaseService.appCtx.getBean(IDGeneratorService::class.java)
            if (data is BaseEntity) {
                if (data.id != null)
                    return data.id!!
                if (data.getBundle("generate_id") != null)
                    return data.getBundle("generate_id") as Long
            }
            return service!!.snowflakeID.nextId()
        }

        @Throws(HibernateException::class)
        override fun configure(type: Type, params: Properties, serviceRegistry: ServiceRegistry) {
            name = params.getProperty("target_table")
            if (params.contains("name"))
                name = params.getProperty("name")
        }
    }

}
package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.EntityPath
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.repositories.LdapConfigRepository
import com.vsimtone.pddp.server.repositories.OrganizationRepository
import com.vsimtone.pddp.server.utils.AppUtils
import org.hibernate.Session
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class OrgService : BaseCrudService<Organization, OrganizationRepository>(QOrganization.a) {

    companion object {
        val SYS_ORG_KEY_ROOT = "sys_root";
        val SYS_ORG_KEY_UNKNOWN_CLIENT = "sys_unknown_client";
    }

    val SYS_ORG_CACHE = mutableMapOf<String, Organization>()

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var idGeneratorService: IDGeneratorService

    @Autowired
    lateinit var ldapConfigRepository: LdapConfigRepository;

    @Autowired
    lateinit var appConfigService: APPConfigService;

    init {
        readyInvokeOrder = READY_INVOKE_HIGH;
    }

    fun initOrg() {
        val rootOrg = initSysOrg(SYS_ORG_KEY_ROOT, "总部", null);
        initSysOrg(SYS_ORG_KEY_UNKNOWN_CLIENT, "未知终端", rootOrg);
    }

    override fun onReady() {
        super.onReady()
        if (appConfigService.getBoolean("core.org.upgrade_dn.done") != true) {
            val syncConfList = ldapConfigRepository.findAll(QLdapConfig.a.syncTo.isNotNull).toList()
            val orgList = findAll(QOrganization.a.key.like("dn:%")).toList()
            this.logger.info("Upgrade ldap sync org f_key start: ${orgList.size} org, ${syncConfList.size} ldap conf.")
            val progressReport = AppUtils.ProgressReport("Upgrade ldap sync org f_key progress:", this.logger, orgList.size);
            orgList.forEachIndexed { idx, org ->
                val cnf = syncConfList.find { org.path.indexOf(it.syncTo!!.path) == 0 } ?: throw Error("Org ${org} sync from unknown.")
                org.key = "${cnf.id}:${org.key}"
                save(org);
                if (idx % 1000 == 0)
                    repo.flush()
                progressReport.report(idx);
            }
            this.logger.info("Upgrade ldap sync org f_key stop.")
            appConfigService.setConfig("core.org.upgrade_dn.done", "true", APPConfig.Type.Boolean)
        }
        val pathNeedFix = repo.findAll(QOrganization.a.path.isNull).toList();
        if (pathNeedFix.isNotEmpty()) {
            val progressReport = AppUtils.ProgressReport("Org path miss fix", this.logger, pathNeedFix.size);
            pathNeedFix.forEachIndexed { idx, org ->
                save(org);
                if (idx % 1000 == 0)
                    repo.flush()
                progressReport.report(idx);
            }
            this.logger.info("Org path miss fix done.")
        }
    }

    @Transactional
    fun delOrg(org: Organization): Boolean {
        var ok = true;
        val refs = listOf<Array<Any>>(
            arrayOf(QClientDevice.a, QClientDevice.a.org),
            arrayOf(QExternalFileSystem.a, QExternalFileSystem.a.belongOrg),
            arrayOf(QUser.a, QUser.a.org),
            arrayOf(QOrganization.a, QOrganization.a.parent),
            arrayOf(QUser.a, QUser.a.manageOrgs.any()),
            arrayOf(QEdgeNodeCluster.a, QEdgeNodeCluster.a.belongOrg),
            arrayOf(QEdgeNodeRoute.a, QEdgeNodeRoute.a.belongOrg),
            arrayOf(QLdapConfig.a, QLdapConfig.a.syncTo)
        )
        refs.forEach {
            if (ok) {
                val refData = queryFactory.from(it[0] as EntityPath<*>).where((it[1] as QOrganization).id.eq(org.id)).limit(1).fetchFirst()
                if (refData != null) {
                    this.logger.info("Ignore delete ${org}: reason ref by ${it[0]} ${it[1]} ${refData}")
                    ok = false;
                }
            }
        }
        if (ok) {
            repo.delete(org)
            this.logger.info("Deleted ${org}")
        }
        return ok;
    }

    fun isSyncTo(id: Long): Boolean {
        return ldapConfigRepository.count(QLdapConfig.a.syncTo.id.eq(id)) > 0
    }

    fun getSysOrg(key: String): Organization {
        return SYS_ORG_CACHE[key]!!;
    }

    fun findByParentAndName(parent: Organization?, name: String): Organization? {
        val queries = BooleanBuilder()
        if (parent != null)
            queries.and(QOrganization.a.parent.eq(parent))
        else
            queries.and(QOrganization.a.parent.isNull)
        queries.and(QOrganization.a.name.eq(name))
        return findOne(queries)
    }

    fun initSysOrg(key: String, name: String, parent: Organization?): Organization {
        val a = QOrganization.a
        val orgOptl = repo.findOne(a.type.eq(Organization.Type.Sys).and(a.key.eq(key)));
        if (!orgOptl.isPresent) {
            val org = Organization();
            org.parent = parent;
            org.name = name;
            org.key = key;
            org.type = Organization.Type.Sys;
            save(org);
            SYS_ORG_CACHE[key] = org;
            logger.info("Add sys org $key $name");
            return org;
        } else {
            SYS_ORG_CACHE[key] = orgOptl.get();
            return orgOptl.get();
        }
    }

    fun getNames(org: Organization): List<String> {
        val names = mutableListOf<String>()
        var t = org
        while (true) {
            names.add(t.name)
            t = t.parent ?: break
        }
        names.reverse()
        return names;
    }

    fun updatePath(data: Organization) {
        if (data.parent == null)
            data.path = data.id.toString() + "|";
        else
            data.path = data.parent!!.path + data.id.toString() + "|";
    }

    override fun save(data: Organization) {
        val isNew = data.id == null
        if (isNew)
            data.id = idGeneratorService.snowflakeID.nextId();
        updatePath(data);
        if (isNew)
            entityManager.unwrap(Session::class.java).save(data);
        else
            entityManager.unwrap(Session::class.java).update(data);
    }

    fun isParent(parent: Organization, org: Organization): Boolean {
        return (org.path.startsWith(parent.path))
    }
}
package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.Expression
import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.BooleanExpression
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.utils.DateUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.*

@Service
class HomeStatsService : BaseService() {

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService


    override fun onReady() {
    }
    

    fun lastOrgName(name: String): String {
        return name.split("|").last()
    }

}
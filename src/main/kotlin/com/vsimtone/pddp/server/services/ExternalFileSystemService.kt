package com.vsimtone.pddp.server.services

import com.jcraft.jsch.JSch
import com.jcraft.jsch.Logger
import com.mysema.commons.lang.URLEncoder
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.entities.ExternalFileSystem
import com.vsimtone.pddp.server.entities.QExternalFileSystem
import com.vsimtone.pddp.server.repositories.ExternalFileSystemRepository
import jcifs.smb.SmbAuthException
import okhttp3.internal.closeQuietly
import org.apache.commons.lang3.StringUtils
import org.apache.commons.vfs2.FileObject
import org.apache.commons.vfs2.FileSystemOptions
import org.apache.commons.vfs2.impl.StandardFileSystemManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ExternalFileSystemService : BaseCrudService<ExternalFileSystem, ExternalFileSystemRepository>(QExternalFileSystem.a) {

    @Autowired
    lateinit var fileService: FileService

    init {
        readyInvokeOrder = READY_INVOKE_HIGH;
    }

    override fun onInit() {
        JSch.setLogger(object : Logger {
            override fun isEnabled(level: Int): Boolean {
                return false;
            }

            override fun log(level: Int, message: String) {
                logger.info("JSch: $level: $message");
            }

        });
    }

    fun getVFSBaseUrl(data: ExternalFileSystem, subPath: String? = null): String {
        val url = StringBuilder()
        when (data.type) {
            ExternalFileSystem.Type.S3 -> url.append("s3://")
            ExternalFileSystem.Type.FTP -> url.append("ftp://")
            ExternalFileSystem.Type.FTPS -> url.append("ftps://")
            ExternalFileSystem.Type.SFTP -> url.append("sftp://")
            ExternalFileSystem.Type.SAMBA -> url.append("smb://")
            else -> throw UnsupportedOperationException("External file system type ${data.type} not supported.")
        }
        url.append(URLEncoder.encodeURL(data.username))
        url.append(":")
        url.append(URLEncoder.encodeURL(data.password))
        url.append("@")
        url.append(data.host + ":" + data.port)
        if (StringUtils.isAllEmpty(data.rootPath))
            url.append("/")
        else {
            if (!data.rootPath!!.startsWith("/"))
                url.append("/")
            url.append(URLEncoder.encodeURL(data.rootPath))
            if (!data.rootPath!!.endsWith("/"))
                url.append("/")
        }
        if (subPath != null) {
            if (subPath.startsWith("/"))
                url.append(subPath.substring(1));
            else
                url.append(subPath);
        }
        return url.toString();
    }

    fun getVFS(): StandardFileSystemManager {
        val vfs = StandardFileSystemManager();
        vfs.init();
        return vfs;
    }

    fun getVFSOptions(data: ExternalFileSystem): FileSystemOptions {
        return fileService.getDefaultVFSOptions(getVFSBaseUrl(data));
    }

    fun checkConfig(data: ExternalFileSystem) {
        val options = this.getVFSOptions(data);
        getVFS().use {
            val baseUrl = getVFSBaseUrl(data);
            if (baseUrl.indexOf(":") != -1 && !it.hasProvider(baseUrl.substring(0, baseUrl.indexOf(":")))) {
                throw RestException(1, "VFS Not supported $data");
            }
            val baseFile = it.resolveFile(baseUrl, options);
            try {
                if (!baseFile.exists())
                    throw RestException(1, "根路径不存在")
                if (!baseFile.isFolder)
                    throw RestException(1, "根路径不是一个目录")
            } catch (_e: Exception) {
                var e: Throwable = _e;
                while (e.cause != null)
                    e = e.cause!!
                if (e is SmbAuthException) {
                    throw RestException(1, "帐号或者密码错误", e)
                }
                if (e.message?.indexOf("cannot be found", 0, true) != -1) {
                    throw RestException(1, "根路径不存在", e)
                }
                if (e.message?.indexOf("not found", 0, true) != -1) {
                    throw RestException(1, "根路径不存在", e)
                }
                if (e.message?.indexOf("not login", 0, true) != -1) {
                    throw RestException(1, "帐号或者密码错误", e)
                }
                if (e.message?.indexOf("bad password", 0, true) != -1) {
                    throw RestException(1, "帐号或者密码错误", e)
                }
                throw e;
            }
        }
    }

    fun findFiles(vfs: StandardFileSystemManager, rootFile: FileObject, maxFile: Long, cb: (f: FileObject?, files: List<FileObject>?) -> Boolean) {
        var exit = false;
        if (!rootFile.exists()) throw RestException(1, "根路径不存在")
        if (!rootFile.isFolder) throw RestException(1, "根路径不是一个目录")
        val folders = mutableListOf<FileObject>()
        val files = mutableListOf<FileObject>()
        folders.add(rootFile)
        while (folders.isNotEmpty()) {
            if (exit) break
            val folder = folders.removeAt(0)
            val children = folder.children
            children.forEach {
                if (exit) return@forEach
                if (!it.isReadable) return@forEach
                if (it.isHidden) return@forEach
                if (it.name.path.length <= folder.name.path.length) return@forEach
                if (it.isFile) {
                    if (cb(it, null)) {
                        files.add(it);
                    }
                    if (files.size >= maxFile) {
                        exit = true;
                    }
                } else if (it.isFolder)
                    folders.add(it)
            }
        }
        cb(null, files);
        folders.forEach {
            it.closeQuietly();
        }
        files.forEach {
            it.closeQuietly();
        }
    }

    fun findFiles(data: ExternalFileSystem, subPath: String, maxFile: Long, cb: (f: FileObject?, files: List<FileObject>?) -> Boolean) {
        val options = this.getVFSOptions(data);
        getVFS().use { vfs ->
            var rootPath = getVFSBaseUrl(data, subPath);
            if (!rootPath.endsWith("/")) rootPath += "/";
            val rootFile = vfs.resolveFile(rootPath, options);
            rootFile.use {
                findFiles(vfs, rootFile, maxFile, cb)
            }
            rootFile.closeQuietly();
        }
    }
}

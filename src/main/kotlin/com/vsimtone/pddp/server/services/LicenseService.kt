package com.vsimtone.pddp.server.services

import com.fasterxml.jackson.annotation.JsonView
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.configs.ServerConfig
import com.vsimtone.pddp.server.consts.LicensePubkey
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.utils.CryptUtils
import com.vsimtone.pddp.server.utils.JSONUtils
import org.apache.commons.codec.binary.Hex
import org.msgpack.core.MessagePack
import org.redisson.api.RAtomicLong
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.io.File
import java.util.*
import java.util.zip.GZIPInputStream
import kotlin.math.ceil

@Service
class LicenseService : BaseService() {
    companion object {

        enum class LicenseStatus { NoLicense, Expired, Active }

        enum class Components { LdapTypeBOC_P6 }

        const val CNF_KEY_LICENSE_CONTENT = "core.license.content";
        const val MSG_RELOAD_LICENSE = "license_RELOAD";

        const val LICENSE_V1 = 1;

        class LicenseRequest {
            @JsonView(JsonViews.Exclude::class)
            lateinit var serverPubkey: String

            @JsonView(JsonViews.Exclude::class)
            lateinit var serverPubkeyHash: String
            lateinit var serverMachineIdList: List<String>
            lateinit var productCode: String
            var requestTimestamp: Long = 0
            lateinit var serverUrl: String
            lateinit var serverVer: String
            var version: Int = LICENSE_V1
            var serverInstallTime = 0L
        }

        class LicenseContent {
            lateinit var request: LicenseRequest
            lateinit var productName: String
            lateinit var issueBy: String
            lateinit var issueTo: String
            var issueTime: Long = 0L
            var activeTime: Long = 0L
            var expireTime: Long = 0L
            var version: Int = LICENSE_V1
            var maxClientDevice: Long = 0L
            var components = listOf<String>()
            override fun toString(): String {
                return "LicenseContent(productName='$productName', issueBy='$issueBy', issueTo='$issueTo', issueTime=$issueTime, activeTime=$activeTime, expireTime=$expireTime, version=$version, maxClientDevice=$maxClientDevice, components=$components)"
            }
        }
    }

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var orgService: OrgService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var certKeyPairService: CertKeyPairService

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var sysAlertService: SysAlertService

    @Autowired
    lateinit var serverConfig: ServerConfig

    lateinit var freeClientDeviceLicense: RAtomicLong

    var _license_content: LicenseContent? = null;
    var _license_status = LicenseStatus.NoLicense;

    override fun onReady() {
        freeClientDeviceLicense = redisService.redisson.getAtomicLong("license:free_client_device")
        try {
            refreshLicense();
        } catch (e: Exception) {
            this.logger.error("Load license error", e);
        }
        schedule("license:update_free_client_device", 60 * 1000) {
            refreshFreeClientDevice()
        }
        sysAlertService.addAlertProvider("license_check") {
            val alerts = mutableListOf<SysAlertService.Companion.SysAlert>()
            sysService.serverNodes.forEach { id, info ->
                if (info.licenseStatus != LicenseStatus.Active) {
                    alerts.add(SysAlertService.Companion.SysAlert("主服务" + info.hostName, "未授权"));
                }
            }
            val freeClientDeviceNum = freeClientDeviceLicense.get();
            if (getLicenseStatus() == LicenseStatus.Active && freeClientDeviceNum > 0 && freeClientDeviceNum <= 10) {
                alerts.add(SysAlertService.Companion.SysAlert("主服务", "终端授权数量快用完了(剩余${freeClientDeviceNum}个)"));
            }
            if (getLicenseStatus() == LicenseStatus.Active && freeClientDeviceNum <= 0) {
                alerts.add(SysAlertService.Companion.SysAlert("主服务", "终端授权数量已用完"));
            }
            val content = getLicenseContent() ?: return@addAlertProvider alerts;
            val freeDay = ceil((content.expireTime - System.currentTimeMillis()) / (3600 * 24 * 1000.0));
            if (freeDay > 0 && freeDay <= 15) {
                alerts.add(SysAlertService.Companion.SysAlert("主服务" + sysService.thisNodeInfo.hostName, "授权即将到期(剩余${freeDay}天)"));
            }
            if (freeDay <= 0) {
                alerts.add(SysAlertService.Companion.SysAlert("主服务" + sysService.thisNodeInfo.hostName, "授权已过期"));
            }
            return@addAlertProvider alerts;
        }
    }

    @EventListener(SysService.Companion.AllServerMsgEvent::class)
    fun onReloadLicenseEvent(msg: SysService.Companion.AllServerMsgEvent) {
        if (msg.msg == MSG_RELOAD_LICENSE) {
            refreshLicense()
            sysAlertService.sysAlerts.clear()
        }
    }

    fun applyLicenseContent(content: String) {
        val licenseContent = decodeLicenseContent(content);
        if (verifyLicenseContent(licenseContent) != LicenseStatus.Active)
            throw RestException(1, "授权未激活")
        appConfigService.setConfig(CNF_KEY_LICENSE_CONTENT, content, APPConfig.Type.String)
        this.logger.info("Apply license ${licenseContent}")
        runInAfterTranCommitted {
            refreshFreeClientDevice();
            sysService.pushMsgToAllServers(MSG_RELOAD_LICENSE);
        }
    }

    fun getLicenseContent(): LicenseContent? {
        return _license_content
    }

    fun hasComponent(cmp: Components): Boolean {
        if (_license_content != null)
            return _license_content!!.components.contains(cmp.name)
        return false
    }

    fun getLicenseStatus(): LicenseStatus {
        return _license_status;
    }

    fun setLicenseStatus(status: LicenseStatus, content: LicenseContent?) {
        _license_content = content;
        _license_status = status;
        sysService.thisNodeInfo.licenseStatus = status;
    }

    fun getInstallTime(): Long {

        val installTimes = mutableListOf<Date?>();

        installTimes.add(userService.query().select(QUser.a.createdAt.min()).fetchOne());
        installTimes.add(userService.query().select(QUser.a.updatedAt.min()).fetchOne());

        installTimes.add(clientDeviceService.query().select(QClientDevice.a.createdAt.min()).fetchOne());
        installTimes.add(clientDeviceService.query().select(QClientDevice.a.updatedAt.min()).fetchOne());

        installTimes.add(certKeyPairService.query().select(QCertKeyPair.a.createdAt.min()).fetchOne());
        installTimes.add(certKeyPairService.query().select(QCertKeyPair.a.updatedAt.min()).fetchOne());
        installTimes.add(Date(File("").lastModified()))

        val minInstallTime = installTimes.filterNotNull().filter { it.time > 0 && it.time <= System.currentTimeMillis() }.minByOrNull { it.time }
        if (minInstallTime == null || minInstallTime.time == 0L) {
            setLicenseStatus(LicenseStatus.Expired, null)
            return 0;
        }
        return minInstallTime.time
    }

    fun requireClientDeviceLicense(): Boolean {
        val f = freeClientDeviceLicense.decrementAndGet()
        return f >= 0
    }

    fun refreshFreeClientDevice() {
        if (getLicenseStatus() == LicenseStatus.Active) {
            var free = runInReadOnlyAndNoTran {
                getLicenseContent()!!.maxClientDevice - clientDeviceService.count(QClientDevice.a.status.ne(ClientDevice.Status.Expired));
            }
            if (free < 0) {
                clientDeviceService.query()
                    .where(QClientDevice.a.status.ne(ClientDevice.Status.Expired))
                    .orderBy(QClientDevice.a.updatedAt.desc())
                    .limit(0 - free).fetch().forEach {
                        it.status = ClientDevice.Status.Expired;
                        it.user = null;
                        it.org = orgService.getSysOrg(OrgService.SYS_ORG_KEY_UNKNOWN_CLIENT)
                        clientDeviceService.saveDelay(it, false);
                        this.logger.info("${it} Out of license, set expired.");
                    }
                freeClientDeviceLicense.set(0);
            } else
                freeClientDeviceLicense.set(free);
        } else {
            freeClientDeviceLicense.set(0);
        }
    }

    @Scheduled(fixedDelay = 300 * 1000L)
    fun refreshLicense() {
        val licenseContentRaw = appConfigService.getString(CNF_KEY_LICENSE_CONTENT)
        if (licenseContentRaw == null) {
            setLicenseStatus(LicenseStatus.NoLicense, null)
            return;
        }
        val licenseContent = decodeLicenseContent(licenseContentRaw)
        try {
            setLicenseStatus(verifyLicenseContent(licenseContent), licenseContent)
        } catch (e: Exception) {
            this.logger.error("License verify error", e);
            setLicenseStatus(LicenseStatus.NoLicense, licenseContent);
        }
    }

    fun verifyLicenseContent(licenseContent: LicenseContent): LicenseStatus {
        val nowTime = System.currentTimeMillis()
        if (!licenseContent.request.serverMachineIdList.contains(sysService.thisNodeInfo.machineId!!)) {
            throw RuntimeException("当前机器ID未授权")
        }
        if (licenseContent.request.serverUrl != serverConfig.url) {
            throw RuntimeException("地址不匹配")
        }
        if (licenseContent.request.serverPubkeyHash != certKeyPairService.serverCertKeyPair.publicKeyHash) {
            throw RuntimeException("证书不匹配")
        }
        if (licenseContent.activeTime > nowTime) {
            throw RuntimeException("未到授权开始时间: ${BaseEntity.DEFAULT_DATE_FORMAT.format(licenseContent.activeTime)}")
        }
        if (licenseContent.expireTime < nowTime) {
            return LicenseStatus.Expired;
        }
        return LicenseStatus.Active;
    }

    fun getIssueKeyPair(): CertKeyPair {
        val rawPubKey = GZIPInputStream(ByteArrayInputStream(LicensePubkey.KEY.map { it.toByte() }.toByteArray())).readAllBytes().toString(Charsets.UTF_8);
        val keyPair = certKeyPairService.newCertKey(rawPubKey, CertKeyPair.UsedBy.Server)
        return keyPair;
    }

    fun decodeLicenseContent(content: String): LicenseContent {
        val keyPair = getIssueKeyPair();
        val contentReader = MessagePack.newDefaultUnpacker(CryptUtils.base64decode(content))
        val contentVersion = contentReader.unpackInt()
        var contentSign = ""
        var contentData: ByteArray
        if (contentVersion == 1) {// format 1
            contentData = contentReader.readPayload(contentReader.unpackBinaryHeader());
            contentSign = contentReader.unpackString();
        } else {
            throw RuntimeException("Unknown license content version: ${contentVersion}")
        }
        val signCheckOk = certKeyPairService.verifySign(contentData, Hex.decodeHex(contentSign), keyPair);
        if (!signCheckOk) {
            throw RuntimeException("License sign invalidate");
        }
        var contentDataStr = certKeyPairService.decodeData(contentData, certKeyPairService.serverCertKeyPair).toString(Charsets.UTF_8)
        return JSONUtils.toObject(contentDataStr, LicenseContent::class.java)
    }

    fun generateLicenseRequest(): String {
        val req = LicenseRequest()
        req.serverPubkeyHash = certKeyPairService.serverCertKeyPair.publicKeyHash
        req.serverPubkey = certKeyPairService.serverCertKeyPair.publicKey
        val serverMachineIdList = sysService.serverNodes.map { it.value.machineId }.filterNotNull().toMutableList()
        if (this.appConfigService.licenseReqIncludeOfflineMachineID) {
            val prevContent = this.getLicenseContent()
            if (prevContent != null) {
                prevContent.request.serverMachineIdList.forEach {
                    if (!serverMachineIdList.contains(it)) {
                        serverMachineIdList.add(it)
                    }
                }
            }
        }
        req.serverMachineIdList = serverMachineIdList
        req.productCode = "pddp"
        req.requestTimestamp = System.currentTimeMillis();
        req.serverUrl = serverConfig.url!!;
        req.serverVer = serverConfig.version;
        req.serverInstallTime = getInstallTime();
        val keyPair = getIssueKeyPair();
        val reqData = JSONUtils.toString(req);
        val reqCryptedData = certKeyPairService.encodeData(reqData.toByteArray(Charsets.UTF_8), keyPair);
        return CryptUtils.base64encode(reqCryptedData);
    }

}
package com.vsimtone.pddp.server.services

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.vsimtone.pddp.server.entities.LdapConfig
import org.apache.commons.lang3.StringUtils
import org.apache.commons.text.StringEscapeUtils
import org.apache.commons.text.translate.AggregateTranslator
import org.apache.commons.text.translate.EntityArrays
import org.apache.commons.text.translate.JavaUnicodeEscaper
import org.apache.commons.text.translate.LookupTranslator
import org.springframework.stereotype.Service
import java.util.*
import javax.naming.AuthenticationException
import javax.naming.Context
import javax.naming.directory.BasicAttribute
import javax.naming.directory.BasicAttributes
import javax.naming.directory.DirContext
import javax.naming.directory.SearchControls
import javax.naming.ldap.*


@Service
class LDAPService : BaseService() {
    companion object {
        const val S_UID = "uid"
        const val S_SAM_ACCOUNT_NAME = "sAMAccountName"
        const val S_MAIL = "mail"
        const val S_OU = "ou"
        const val S_DC = "dc"
        const val S_SN = "sn"
        const val S_CN = "cn"
        const val S_DISPLAY_NAME = "displayName"
        const val S_OBJECT_CLASS = "objectClass"

        private val RegexCache = mutableMapOf<String, Regex>()
        fun getRegex(p: String): Regex {
            if (!RegexCache.containsKey(p)) RegexCache[p] = Regex(p)
            return RegexCache[p]!!
        }

        data class LdapUserName(val cn: String, val sn: String)
        data class LdapOrgName(val ou: String)

        val escapeJavaMap: MutableMap<CharSequence, CharSequence> = HashMap()

        init {
            escapeJavaMap["\""] = "\\\""
            escapeJavaMap["\\"] = "\\\\"
        }

        val escapeText = AggregateTranslator(
            LookupTranslator(Collections.unmodifiableMap(escapeJavaMap)),
            LookupTranslator(EntityArrays.JAVA_CTRL_CHARS_ESCAPE),
            JavaUnicodeEscaper.between(0xE000, 0xF8FF),
            JavaUnicodeEscaper.between(0xF0000, 0xFFFFD),
            JavaUnicodeEscaper.above(0xFFFF)
        )

        fun utf8Escape(str: String): String {
            return escapeText.translate(str);
        }

        fun utf8Escape(attrs: BasicAttributes): BasicAttributes {
            var newAttrs = BasicAttributes();
            var all = attrs.all
            while (all.hasMore()) {
                var attr = all.next()
                if (attr !is BasicAttribute) continue;
                var newAttr = BasicAttribute(attr.id)
                for (i in 0 until attr.size()) {
                    var v = attr.get(i)
                    if (v != null && v is String) v = utf8Escape(v);
                    newAttr.add(v);
                }
                newAttrs.put(newAttr)
            }
            return newAttrs;
        }

        fun utf8Unescape(str: String): String {
            return StringEscapeUtils.unescapeJava(str);
        }

        fun utf8Unescape(attrs: BasicAttributes): BasicAttributes {
            var newAttrs = BasicAttributes();
            var all = attrs.all
            while (all.hasMore()) {
                var attr = all.next()
                if (attr !is BasicAttribute) continue;
                var newAttr = BasicAttribute(attr.id)
                for (i in 0 until attr.size()) {
                    var v = attr.get(i)
                    if (v != null && v is String) v = utf8Unescape(v);
                    newAttr.add(v);
                }
                newAttrs.put(newAttr)
            }
            return newAttrs;
        }

        class LdapDN {
            lateinit var dn: String
            lateinit var names: Map<String, String>
            var hierarchy: Int = 0

            @JsonIgnore
            var parent: LdapDN? = null

            constructor(dn: String, names: Map<String, String>, parent: LdapDN?) {
                this.dn = dn.replace(getRegex("UID="), "uid=").replace(getRegex("CN="), "cn=").replace(getRegex("SN="), "sn=").replace(getRegex("OU="), "ou=").replace(getRegex("DC="), "dc=")
                this.names = names
                this.parent = parent
                if (parent != null) this.hierarchy = parent.hierarchy + 1
            }

            override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false
                other as LdapDN
                if (dn != other.dn) return false
                return true
            }

            override fun hashCode(): Int {
                return dn.hashCode()
            }

            override fun toString(): String {
                return "LdapDN(dn='$dn')"
            }

        }

        open fun splitStr(src: String, delimiters: String, limit: Int = 0): List<String> {
            var strs = mutableListOf<String>()
            var i = 0;
            var si = 0;
            do {
                var pos = src.indexOf(delimiters, i)
                if (pos == 0) {
                    si += delimiters.length
                } else if (pos == -1) {
                    strs.add(src.substring(si))
                    break
                } else if (src[pos - 1] != '\\') {
                    if (si != pos) {
                        strs.add(src.substring(si, pos))
                        if (limit != 0 && strs.size + 1 == limit) {
                            strs.add(src.substring(pos + delimiters.length))
                            break
                        }
                    }
                    si = pos + delimiters.length
                } else if (pos == src.length - 1) {
                    strs.add(src.substring(si))
                    break
                }
                i = pos + delimiters.length
            } while (i < src.length)
            return strs
        }

        class AttributesSerializer : JsonSerializer<BasicAttributes>() {
            override fun serialize(attrs: BasicAttributes?, gen: JsonGenerator, serializers: SerializerProvider) {
                if (attrs == null) gen.writeNull()
                else {
                    gen.writeStartObject()
                    var names = mutableListOf<String>()
                    var all = attrs.all
                    while (all.hasMore()) {
                        var attr = all.next()
                        if (names.contains(attr.id)) continue
                        gen.writeFieldName(attr.id.lowercase())
                        gen.writeObject(attr.get())
                        names.add(attr.id)
                    }
                    gen.writeEndObject()
                }
            }

        }

        class LdapItem {
            enum class Type { Org, User, Other }

            lateinit var type: Type

            lateinit var dn: LdapDN

            @JsonSerialize(using = AttributesSerializer::class)
            lateinit var attrs: BasicAttributes

            lateinit var objectClasses: List<String>

            var hierarchy: Int = 0

            @JsonIgnore
            var parent: LdapItem? = null
            var children = mutableListOf<LdapItem>()

            @JsonIgnore
            @Transient
            var extras = mutableMapOf<Any?, Any?>()

            override fun toString(): String {
                return "LdapItem(type=$type, dn=$dn, attrs=$attrs)"
            }

            override fun equals(other: Any?): Boolean {
                if (this === other) return true
                if (javaClass != other?.javaClass) return false

                other as LdapItem

                if (dn != other.dn) return false

                return true
            }

            override fun hashCode(): Int {
                return dn.hashCode()
            }
        }

        class SearchOptions {
            lateinit var cnf: LdapConfig
            lateinit var ctx: LdapContext
            var dn: String? = null
            var includeChildren = false
            var treeResult = false
            var extraAttrs = listOf<String>()
            var filters = arrayOf<String>()
            var limit = 0L
        }


        open fun getFilter(vararg objectClasses: String): String {
            var f = StringBuffer()
            f.append("(&")
            objectClasses.forEach {
                f.append("(${S_OBJECT_CLASS}=${it})")
            }
            f.append(")")
            return f.toString()
        }

        data class LdapBasicTypeDefine(
            val dnName: String, val userObjectClasses: List<String>, val orgObjectClasses: List<String>, val userUniqueName: String
        ) {
            open fun getUserFilter(): String {
                return getFilter(userObjectClasses.first())
            }

            open fun getOrgFilter(): String {
                return getFilter(orgObjectClasses.first())
            }
        }

        val NameCleanRegex = getRegex("[/]+")

        val basicAttrs = listOf(S_OBJECT_CLASS, S_OU, S_UID, S_SAM_ACCOUNT_NAME, "cn", "sn", "dc", S_DISPLAY_NAME, "mail", "description")
        val basicTypes: Map<LdapConfig.Type, LdapBasicTypeDefine> = mapOf(
            LdapConfig.Type.ActiveDirectory to LdapBasicTypeDefine(
                "distinguishedName", listOf("organizationalPerson", "person", "top"), listOf("organizationalUnit", "top"), S_SAM_ACCOUNT_NAME
            ), LdapConfig.Type.Ldap to LdapBasicTypeDefine(
                "entryDn", listOf("inetOrgPerson", "organizationalPerson", "person", "top"), listOf("organizationalUnit", "top"), S_UID
            )
        )

        fun d(cnf: LdapConfig?): LdapBasicTypeDefine {
            return basicTypes[cnf!!.type]!!
        }

    }

    open fun getLdapCtx(host: String, port: Int, username: String, password: String): LdapContext {
        val env = Hashtable<String, String>()
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, "ldap://${host}:${port}");
        env.put(Context.SECURITY_PRINCIPAL, username);
        env.put(Context.SECURITY_CREDENTIALS, password);
        env.put(Context.SECURITY_AUTHENTICATION, "simple");
        env.put(Context.BATCHSIZE, "1000");
        return InitialLdapContext(env, null);
    }

    open fun getLdapCtx(cnf: LdapConfig): LdapContext {
        return getLdapCtx(cnf.host!!, cnf.port, cnf.username!!, cnf.password!!)
    }

    open fun checkUserPassword(cnf: LdapConfig, username: String, password: String): Boolean {
        try {
            if (StringUtils.isNotEmpty(cnf.authWithAdHost?.trim()) && cnf.authWithAdPort != null && cnf.authWithAdPort != 0)
                getLdapCtx(cnf.authWithAdHost!!, cnf.authWithAdPort!!, username, password).close()
            else
                getLdapCtx(cnf.host!!, cnf.port, username, password).close()
            return true
        } catch (e: AuthenticationException) {
            return false
        }
    }

    open fun getItemType(objectClasses: List<String>): LdapItem.Type {
        var end = objectClasses.sorted().first()
        if (end == "organizationalUnit") return LdapItem.Type.Org
        if (end == "organizationalPerson" || end == "user" || end == "inetOrgPerson") return LdapItem.Type.User
        return LdapItem.Type.Other
    }


    open fun parseNames(_rawNames: String): Map<String, String> {
        var names = mutableMapOf<String, String>()
        var rawNames = _rawNames
        splitStr(rawNames, "+").forEach {
            var tmp = splitStr(it, "=", 2)
            if (tmp.size != 2) throw RuntimeException("Wrong dn name ${it}")
            names.put(tmp[0].lowercase(), tmp[1])
        }
        return names
    }

    private fun _parseDN(dn: String): LdapDN {
        var dnl = splitStr(dn, ",", 2)
        var parent: LdapDN? = null
        if (dnl.size == 2) parent = _parseDN(dnl[1])
        return LdapDN(dn, parseNames(dnl[0]), parent)
    }

    open fun parseDN(dn: String): LdapDN {
        try {
            return _parseDN(dn)
        } catch (e: Exception) {
            throw RuntimeException("Parse dn failed: ${dn}, error: ${e.message}")
        }
    }

    open fun toDnName(names: Map<String, String>): String {
        var name = mutableListOf<String>()
        names.forEach {
            val key = it.key
            val value = it.value
            name.add("${key.lowercase()}=${value}")
        }
        return name.joinToString("+")
    }

    open fun toDN(names: Map<String, String>, parent: List<Map<String, String>>): LdapDN {
        var dn = mutableListOf<String>()
        dn.add(toDnName(names))
        parent.forEach {
            dn.add(toDnName(it))
        }
        return parseDN(dn.joinToString(","))
    }

    open fun toDN(names: Map<String, String>, parent: LdapDN): LdapDN {
        var dn = mutableListOf<String>()
        dn.add(toDnName(names))
        dn.add(parent.dn)
        return LdapDN(dn.joinToString(","), names, parent)
    }

    open fun toDN(vararg dn: String): LdapDN {
        return parseDN(dn.joinToString(","))
    }

    open fun getItem(_dn: String, _attributes: BasicAttributes): LdapItem {
        val attributes = utf8Unescape(_attributes)
        var dn = utf8Unescape(_dn)
        val item = LdapItem()
        if (dn.startsWith("\"") && dn.endsWith("\"")) {
            dn = dn.substring(1, dn.length - 1)
        }
        dn = dn.replace(getRegex("([^\\\\])?/"), "$1\\\\/").replace(NameCleanRegex, "")
        item.dn = parseDN(dn)
        item.attrs = attributes
        if (attributes.get(S_OBJECT_CLASS) == null && attributes.get(S_OBJECT_CLASS.lowercase()) != null) {
            val clsAttr = BasicAttribute(S_OBJECT_CLASS)
            attributes.get(S_OBJECT_CLASS.lowercase()).all.toList().forEach {
                clsAttr.add(it as String)
            }
            attributes.put(clsAttr)
        }
        item.objectClasses = attributes.get(S_OBJECT_CLASS).all.toList().map { it as String }
        item.type = getItemType(item.objectClasses)
        item.hierarchy = item.dn.hierarchy
        return item
    }

    open fun findByDN(ctx: LdapContext, dn: String): LdapItem {
        var obj = ctx.lookup(dn);
        return LdapItem();
    }

    open fun search(options: SearchOptions): List<LdapItem> {
        var filter = ""
        if (options.filters.size == 1) filter = options.filters[0]
        else if (options.filters.size > 1) filter = "(|${StringUtils.join(options.filters, "")})"
        else filter = "(${S_OBJECT_CLASS}=*)"
        var attrs = mutableListOf<String>()
        attrs.addAll(basicAttrs)
        attrs.addAll(options.extraAttrs)

        val searchControls = SearchControls()
        if (options.includeChildren) searchControls.searchScope = SearchControls.SUBTREE_SCOPE
        else searchControls.searchScope = SearchControls.ONELEVEL_SCOPE
        searchControls.returningAttributes = attrs.toTypedArray()
        searchControls.timeLimit = 600 * 1000
        searchControls.countLimit = 0

        var cookie: ByteArray? = null
        var pageSize = 1000L
        if (options.limit > 0) {
            searchControls.countLimit = options.limit
            pageSize = options.limit
        }
        options.ctx.requestControls = arrayOf<Control>(PagedResultsControl(pageSize.toInt(), Control.CRITICAL))
        var items = mutableListOf<LdapItem>()
        do {
            val results = options.ctx.search(options.dn, filter, searchControls)
            while (results.hasMoreElements()) {
                val r = results.next()
                if (r.name != null && r.name.isNotEmpty()) {
                    val item = getItem(r.name, r.attributes as BasicAttributes)
                    items.add(item)
                }
            }
            if (options.limit > 0 && items.size >= options.limit) break
            cookie = null
            options.ctx.responseControls?.forEach {
                if (it is PagedResultsResponseControl) {
                    cookie = it.cookie
                }
            }
            options.ctx.requestControls = arrayOf<Control>(PagedResultsControl(pageSize.toInt(), cookie, Control.CRITICAL))
        } while (cookie != null && cookie!!.isNotEmpty())
        options.ctx.requestControls = null
        if (options.treeResult) {
            items.forEach {
                var item = it
                item.parent = items.find { it.dn == item.dn.parent }
                item.hierarchy = item.dn.hierarchy
                if (item.parent != null) {
                    item.parent!!.children.add(item)
                } else {
                    if (item.dn.parent != null) logger.warn("Item parent not found ${item.dn.dn}")
                }
            }
            return items.filter { it.parent == null }
        }
        return items
    }

    open fun treeDelete(cnf: LdapConfig, ctx: LdapContext, rootDN: String, selfDelete: Boolean) {
        var dn = toDN(rootDN, cnf.rootDN!!).dn
        search(SearchOptions().apply {
            this.cnf = cnf
            this.ctx = ctx
            this.dn = dn
            this.includeChildren = true
            this.treeResult = false
        }).sortedByDescending { it.dn.hierarchy }.forEach {
            deleteItem(cnf, ctx, it, dn)
        }
    }

    open fun fillItem(item: LdapItem) {
        if (item.attrs[S_OBJECT_CLASS] == null) {
            var oc = BasicAttribute(S_OBJECT_CLASS)
            item.objectClasses.forEach {
                oc.add(it)
            }
            item.attrs.put(oc)
        }
    }

    open fun deleteItem(cnf: LdapConfig, ctx: LdapContext, item: LdapItem, vararg rootDN: String) {
        fillItem(item)
        var dn = toDN(item.dn.dn, *rootDN)
        logger.debug("delete ${dn}")
        ctx.destroySubcontext(utf8Escape(dn.dn))
    }

    open fun addItem(cnf: LdapConfig, ctx: LdapContext, item: LdapItem, vararg rootDN: String) {
        fillItem(item)
        var dn = toDN(item.dn.dn, *rootDN)
//        logger.debug("add ${dn}")
        ctx.createSubcontext(utf8Escape(dn.dn), utf8Escape(item.attrs))
    }

    open fun updateItem(cnf: LdapConfig, ctx: LdapContext, item: LdapItem, vararg rootDN: String) {
        fillItem(item)
        var dn = toDN(item.dn.dn, *rootDN)
//        logger.debug("update ${dn} ${item}")
        ctx.modifyAttributes(utf8Escape(dn.dn), DirContext.REPLACE_ATTRIBUTE, utf8Escape(item.attrs))
    }

    fun getDomain(cnf: LdapConfig): String {
        var dn = parseDN(cnf.rootDN!!);
        var names = mutableListOf<String>();
        do {
            var n = dn.names[S_DC];
            if (n != null) names.add(n);
            dn = dn.parent ?: break;
        } while (true);
        return names.joinToString(".").lowercase();
    }
}
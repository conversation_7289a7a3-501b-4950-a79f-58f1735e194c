package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.entities.LdapConfig
import com.vsimtone.pddp.server.entities.QLdapConfig
import com.vsimtone.pddp.server.repositories.LdapConfigRepository
import org.springframework.stereotype.Service

@Service
class LdapConfigService : BaseCrudService<LdapConfig, LdapConfigRepository>(QLdapConfig.a) {

    fun findByDomain(domain: String): List<LdapConfig> {
        return repo.findAll(QLdapConfig.a.domain.eq(domain)).toList()
    }

}
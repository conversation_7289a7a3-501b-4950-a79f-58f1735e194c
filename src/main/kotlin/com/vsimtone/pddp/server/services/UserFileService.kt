package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.entities.QUserFile
import com.vsimtone.pddp.server.entities.QUserFolder
import com.vsimtone.pddp.server.entities.User
import com.vsimtone.pddp.server.entities.UserFile
import com.vsimtone.pddp.server.entities.UserFolder
import com.vsimtone.pddp.server.repositories.UserFileRepository
import com.vsimtone.pddp.server.repositories.UserFolderRepository
import jakarta.annotation.Resource
import org.springframework.stereotype.Service


@Service
class UserFileService : BaseCrudService<UserFile, UserFileRepository>(QUserFile.a) {


    fun getFileType(fileName: String): String {
        val parts = fileName.split(".")
        return if (parts.size > 1) parts.last() else ""
    }


}
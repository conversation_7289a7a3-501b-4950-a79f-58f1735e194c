package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.entities.CommonDataset
import com.vsimtone.pddp.server.entities.QCommonDataset
import com.vsimtone.pddp.server.repositories.CommonDatasetRepository
import com.vsimtone.pddp.server.utils.JSONUtils
import com.vsimtone.pddp.server.utils.YAMLUtils
import org.apache.commons.io.FileUtils
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.util.ResourceUtils

@Service
class CommonDatasetService : BaseCrudService<CommonDataset, CommonDatasetRepository>(QCommonDataset.a) {
    override fun onReady() {
        val internalDatasets = mutableMapOf<String, CommonDataset>()
        repo.findAll(QCommonDataset.a.code.isNotNull).forEach {
            if (internalDatasets[it.code] != null) {
                this.logger.info("Repeat common dataset, delete ${internalDatasets[it.code]}")
                repo.delete(internalDatasets[it.code!!]!!)
            }
            internalDatasets[it.code!!] = it;
            it.disabled = true;
        }
        YAMLUtils.toObject(FileUtils.readFileToString(ResourceUtils.getFile("classpath:common_datasets.yml"), Charsets.UTF_8), Array<CommonDataset>::class.java).forEach {
            val form = it
            val data = internalDatasets[form.code] ?: CommonDataset()
            data.code = form.code
            data.type = form.type
            data.name = form.name
            data.desc = form.desc
            data.values = form.values
            data.disabled = false;
            val isNew = data.id == null
            repo.save(data)
            if (isNew)
                logger.info("Create ${data}")
        }
        val fileExtCategories = JSONUtils.toObject(FileUtils.readFileToString(ResourceUtils.getFile("classpath:file-extension-categories.json"), Charsets.UTF_8), Map::class.java)
        fileExtCategories.forEach { _code, item ->
            val form = item as Map<*, *>
            val code = "file_ext_${_code}";
            val extData = internalDatasets[code] ?: CommonDataset()
            extData.code = code
            extData.type = CommonDataset.Type.FileExt
            extData.name = form["label"] as String
            extData.desc = form["desc"] as String
            extData.values = (form["extensions"] as List<String>).toMutableList()
            extData.disabled = false
            if (extData.values.isEmpty())
                throw RuntimeException("${code} extensions is empty")
            internalDatasets[code] = extData
        }
        val fileMimeCategories = JSONUtils.toObject(FileUtils.readFileToString(ResourceUtils.getFile("classpath:file-mime-categories.json"), Charsets.UTF_8), Map::class.java)
        fileMimeCategories.forEach { _code, item ->
            val form = item as Map<*, *>
            val code = "mime_type_${_code}"
            val mimeData = internalDatasets[code] ?: CommonDataset()
            mimeData.code = code
            mimeData.type = CommonDataset.Type.MimeType
            mimeData.name = form["label"] as String
            mimeData.desc = form["desc"] as String
            mimeData.values = (form["mimes"] as List<String>).toMutableList()
            mimeData.disabled = false
            if (mimeData.values.isEmpty())
                throw RuntimeException("${code} mimes is empty")
            internalDatasets[code] = mimeData
        }
        internalDatasets.forEach { (code, data) ->
            val isNew = data.id == null
            if (isNew) {
                this.repo.save(data)
                this.logger.info("Create ${data}")
            } else {
                this.repo.save(data)
            }
        }
//        var newCategories = mutableMapOf<String, Map<String, Any>>()
//        val fileExtTmp = JSONUtils.toObject(FileUtils.readFileToString(ResourceUtils.getFile("classpath:ext_info.json"), Charsets.UTF_8), Map::class.java)
//        fileExtTmp.forEach { ext, info ->
//            if (fileExtToMimes[ext] == null) return@forEach
//            info as Map<String, String>
//            val description = info["description"]!!
//            val type = info["type"]!!
//            if (newCategories[type] == null) {
//                newCategories[type] = mutableMapOf(
//                    "label" to type,
//                    "desc" to description,
//                    "extensions" to mutableListOf<String>()
//                )
//            }
//            (newCategories[type]!!["extensions"]!! as MutableList<String>).add((ext as String).toLowerCase())
//        }
//        System.out.println("-----------------------------")
//        System.out.println(JSONUtils.toString(newCategories));
//        System.out.println("-----------------------------")
    }

    fun fillValues(values: List<String>): MutableList<String> {
        val newValues = mutableListOf<String>()
        values.forEach {
            if (it.startsWith("@cd:")) {
                val cd = repo.findByIdOrNull(it.substring(4).toLong()) ?: return@forEach
                cd.values.forEach {
                    if (!newValues.contains(it))
                        newValues.add(it)
                }
            } else {
                if (!newValues.contains(it))
                    newValues.add(it)
            }
        }
        return newValues
    }

}

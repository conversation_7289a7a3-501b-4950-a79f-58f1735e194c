package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.repositories.ClientUpgradeFileRepository
import com.vsimtone.pddp.server.utils.*
import org.apache.commons.compress.archivers.ArchiveEntry
import org.apache.commons.compress.archivers.ArchiveInputStream
import org.apache.commons.compress.archivers.ArchiveStreamFactory
import org.apache.commons.compress.compressors.xz.XZCompressorInputStream
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RAtomicLong
import org.redisson.api.RMap
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.util.zip.GZIPInputStream

@Service
class ClientUpgradeFileService : BaseCrudService<ClientUpgradeFile, ClientUpgradeFileRepository>(QClientUpgradeFile.a) {

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var clientUpgradeRecordService: ClientUpgradeRecordService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var orgService: OrgService

    lateinit var allEnabledCache: List<ClientUpgradeFile>;

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService;

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var appConfigService: APPConfigService

    lateinit var deviceUpgradeMatchTime: RMap<String, String>

    var upgradeMatchInterval = DateUtil.minute(15)

    var enableUpgrade = false;

    companion object {
        const val MSG_RELOAD_UPGRADE = "client_upgrade_reload";
        const val CNF_KEY_ALL_MGR_ENABLE = "core.client_upgrade.all_mgr_enable";
        const val CNF_KEY_UPGRADE_MATCH_INTERVAL = "core.client_upgrade.match_interval";
    }

    override fun onReady() {
        loadAllEnabledCache();
        reloadEnabledCache();
        appConfigService.init(CNF_KEY_ALL_MGR_ENABLE, false, APPConfig.Type.Boolean);

        // upgrade to 2.6.0
        findAll(QClientUpgradeFile.a.clientVersionNumber.eq(0)).forEach {
            it.clientVersionNumber = AppUtils.getVersionNumber(it.clientVersion)
            save(it);
        }

        schedule("client_upgrade_cache_reload", DateUtil.hour(1)) {
            reloadEnabledCache();
            upgradeMatchInterval = appConfigService.getLong(CNF_KEY_UPGRADE_MATCH_INTERVAL) ?: upgradeMatchInterval
        }
        val s = schedule("client_upgrade_update", DateUtil.minute(1)) {
            enableUpgrade = appConfigService.getBoolean(CNF_KEY_ALL_MGR_ENABLE) ?: false;
            if (!enableUpgrade) return@schedule;
            emitUpgradeToken();
            updateUpgradeSuccess()
            allEnabledCache.forEach {
                updateUpgradeState(it.id!!);
            }
        }
        s.runInTran = true;
        deviceUpgradeMatchTime = redisService.redisson.getMap("client_upgrade_file:device_match_time", RedisUtils.autoCodec(String::class.java))
    }

    @EventListener(SysService.Companion.AllServerMsgEvent::class)
    fun onReloadMsg(msg: SysService.Companion.AllServerMsgEvent) {
        if (msg.msg == MSG_RELOAD_UPGRADE) {
            loadAllEnabledCache();
        }
    }

    fun loadAllEnabledCache() {
        allEnabledCache = findAll(QClientUpgradeFile.a.enabled.isTrue).map {
//            RestUtil.unwrapBean(ClientUpgradeFile::upgradeFile, it)
            it.putBundle("orgPaths", it.orgs.map {
                orgService.findByIdIf(it.toLong())?.path
            }.filterNotNull())
            it
        };
        logger.info("Do reload all enabled cache")
    }

    fun reloadEnabledCache() {
        sysService.pushMsgToAllServers(MSG_RELOAD_UPGRADE);
    }

    override fun save(d: ClientUpgradeFile) {
        super.save(d)
        runInAfterTranCommitted { reloadEnabledCache() }
    }

    override fun delete(d: ClientUpgradeFile) {
        super.delete(d)
        runInAfterTranCommitted { reloadEnabledCache() }
    }

    fun getTokenCount(data: ClientUpgradeFile): RAtomicLong {
        return redisService.redisson.getAtomicLong("client_upgrade_file:token:${data.id}")
    }

    private fun emitUpgradeToken() {
        allEnabledCache.forEach {
            getTokenCount(it).set(it.upgradeRateInMinute.toLong())
        }
    }

    private fun updateUpgradeSuccess() {
        runInTran {
            val a = QClientUpgradeRecord.a
            allEnabledCache.forEach {
                val q = a.success.isFalse.and(a.pushCount.gt(0))
                if (it.allowedDowngrade) {
                    q.and(a.clientUpgradeFile.clientVersion.eq(a.clientDevice.version))
                } else {
                    q.and(a.clientDevice.versionNum.gt(a.clientUpgradeFile.clientVersionNumber))
                }
                clientUpgradeRecordService.findAll(q).forEach { cur ->
                    cur.success = true;
                    clientUpgradeRecordService.save(cur);
                }
            }
        }
    }

    fun updateUpgradeState(id: Long) {
        runInTran {
            val clientUpgradeFile: ClientUpgradeFile = findById(id)
            val a = QClientUpgradeRecord.a
            clientUpgradeFile.successDeviceCount = clientUpgradeRecordService.count(a.clientUpgradeFile.eq(clientUpgradeFile).and(a.success.isTrue))
            clientUpgradeFile.pushedDeviceCount = clientUpgradeRecordService.count(a.clientUpgradeFile.eq(clientUpgradeFile))

            if (clientUpgradeFile.orgs.isNotEmpty()) {
                val queries = BooleanBuilder()
                queries.and(QClientDevice.a.systemType.eq(clientUpgradeFile.sysType))
                queries.and(QClientDevice.a.systemArch.eq(clientUpgradeFile.sysArch))
                if (clientUpgradeFile.matchSysVersions.isNotEmpty())
                    queries.and(QClientDevice.a.systemVersion.`in`(clientUpgradeFile.matchSysVersions))
                if (clientUpgradeFile.matchClientVersions.isNotEmpty())
                    queries.and(QClientDevice.a.systemVersion.`in`(clientUpgradeFile.matchClientVersions))
                if (clientUpgradeFile.allowedDowngrade) {
                    queries.and(QClientDevice.a.version.ne(clientUpgradeFile.clientVersion))
                } else {
                    queries.and(QClientDevice.a.versionNum.lt(clientUpgradeFile.clientVersionNumber))
                }
                queries.and(QClientDevice.a.status.ne(ClientDevice.Status.Expired))
                queries.and(
                    QClientDevice.a.id.notIn(
                        queryFactory.from(a).select(a.clientDevice.id).where(a.clientUpgradeFile.eq(clientUpgradeFile))
                    )
                )
                val orgQueries = BooleanBuilder()
                clientUpgradeFile.orgs.map {
                    orgService.findByIdIf(it.toLong())
                }.filterNotNull().forEach {
                    orgQueries.or(QClientDevice.a.org.path.startsWith(it.path))
                }
                queries.and(orgQueries)
                clientUpgradeFile.totalDeviceCount = clientDeviceService.count(queries) + clientUpgradeFile.pushedDeviceCount
            } else {
                clientUpgradeFile.totalDeviceCount = clientUpgradeFile.pushedDeviceCount
            }
            if (System.currentTimeMillis() - clientUpgradeFile.updatedAt!!.time >= 3600 * 1000 && clientUpgradeFile.successDeviceCount == clientUpgradeFile.totalDeviceCount) {
                clientUpgradeFile.enabled = false;
                getTokenCount(clientUpgradeFile).delete();
            }
            save(clientUpgradeFile);
        }
    }

    fun isMatchUpgrade(upgradeFile: ClientUpgradeFile, device: ClientDevice): Boolean {
        if (upgradeFile.sysType != device.systemType) return false
        if (upgradeFile.sysArch != device.systemArch) return false
        if (upgradeFile.matchSysVersions.isNotEmpty() && !upgradeFile.matchSysVersions.contains(device.systemVersion)) return false
        if (upgradeFile.matchClientVersions.isNotEmpty() && !upgradeFile.matchClientVersions.contains(device.version)) return false
        if (upgradeFile.allowedDowngrade) {
            if (upgradeFile.clientVersion == device.version) return false;
        } else {
            if (device.versionNum >= upgradeFile.clientVersionNumber) return false;
        }
        if (upgradeFile.orgs.isEmpty()) return false;
        (upgradeFile.getBundle("orgPaths") as List<String>).find {
            device.org?.path?.startsWith(it) ?: false
        } ?: return false
        return true;
    }

    fun getPushRecord(cuf: ClientUpgradeFile, device: ClientDevice): ClientUpgradeRecord? {
        return clientUpgradeRecordService.findOne(QClientUpgradeRecord.a.clientUpgradeFile.eq(cuf).and(QClientUpgradeRecord.a.clientDevice.eq(device)));
    }

    fun getClientUpgrade(device: ClientDevice): Map<String, Any?>? {
        if (!enableUpgrade) return null;
        if (allEnabledCache.isEmpty()) return null;

        var matchResult: Map<String, Any?>? = null;
        val lastMatchTime = (deviceUpgradeMatchTime[device.id!!.toString()] ?: "0").toLong();
        if (lastMatchTime + upgradeMatchInterval > System.currentTimeMillis()) return null;

        allEnabledCache.forEach {
            if (!it.upgradeFileReady || matchResult != null) return@forEach
            val cuf = it;
            val matched = isMatchUpgrade(cuf, device);
            var pushedRecord: ClientUpgradeRecord? = null;
            if (!matched) {
                pushedRecord = getPushRecord(cuf, device)
            }
            if (matched || pushedRecord != null) {
                val t = getTokenCount(cuf).incrementAndGet()
                if (t >= 0) {
                    matchResult = clientUpgradeRecordService.runInTran {
                        val record = pushedRecord ?: getPushRecord(cuf, device) ?: ClientUpgradeRecord();
                        if (record.success) return@runInTran null
                        if (cuf.maxPushCount > 0 && record.pushCount >= cuf.maxPushCount) return@runInTran null
                        record.clientUpgradeFile = cuf;
                        record.clientDevice = device;
                        record.pushCount++;
                        record.success = false;
                        clientUpgradeRecordService.save(record);
                        this.logger.info("Push ${cuf}, ${device}, matched=${matched}, pushCount=${record.pushCount}")
                        return@runInTran mapOf(
                            "version" to cuf.clientVersion,
//                            "sha256sum" to cuf.upgradeFile.sha256sum,
//                            "fileSize" to cuf.upgradeFile.size,
//                            "downToken" to cuf.upgradeFile.token
                        )
                    }
                }
            }
        }
        deviceUpgradeMatchTime[device.id!!.toString()] = System.currentTimeMillis().toString();
        return matchResult
    }

    fun readDebianControlTar(name: String, raw: ByteArray): Map<String, String> {
        var _is: InputStream = ByteArrayInputStream(raw)
        if (name.endsWith(".xz"))
            _is = XZCompressorInputStream(_is)
        if (name.endsWith(".gz"))
            _is = GZIPInputStream(_is)
        val tar = ArchiveStreamFactory().createArchiveInputStream(ArchiveStreamFactory.TAR, _is) as ArchiveInputStream<ArchiveEntry>
        while (true) {
            val entry = tar.nextEntry ?: break
            if (entry.name == "./control" || entry.name == "control") {
                val raw = tar.readNBytes(entry.size.toInt()).toString(Charsets.UTF_8)
                val control = mutableMapOf<String, String>()
                raw.split(Regex("\\r?\\n")).forEach {
                    val kvs = it.split(Regex("\\s*:\\s*"))
                    if (kvs.size == 2) {
                        control[kvs[0].lowercase()] = kvs[1].toString()
                    }
                }
                return control;
            }
        }
        throw RuntimeException("control file not found.")
    }

    fun updateUpgradeFileInfo(data: ClientUpgradeFile, file: InputStream) {
        var findControl = false;
        file.use {
            val debIS = ArchiveStreamFactory().createArchiveInputStream(ArchiveStreamFactory.AR, it) as ArchiveInputStream<ArchiveEntry>
            while (true) {
                val entry = debIS.nextEntry ?: break
                if (entry.isDirectory) continue
                logger.info("Read archive entry ${entry.name}")
                if (entry.name == "debian-binary") {
                    val debianBinary = debIS.readNBytes(entry.size.toInt()).toString(Charsets.UTF_8)
                    logger.info("Debian binary ${debianBinary}")
                    continue
                }
                if (entry.name == "control.tar.xz" || entry.name == "control.tar.gz") {
                    val control = readDebianControlTar(entry.name, debIS.readNBytes(entry.size.toInt()))
                    if (StringUtils.isEmpty(control["version"]))
                        throw RestException(1, "deb文件错误，版本号为空")
                    val arch = clientDeviceService.getSystemArch(control["architecture"]!!)!!
                    if (arch == ClientDevice.SysArch.Unknown)
                        throw RestException(1, "deb文件错误，架构错误：${control["architecture"]}")
                    data.clientVersion = control["version"]!!
                    data.clientVersionNumber = AppUtils.getVersionNumber(data.clientVersion)
                    data.sysArch = arch
                    data.packageInfo = JSONUtils.toString(control);
                    findControl = true;
                    return@use
                }
            }
        }
        if (!findControl)
            throw RestException(1, "deb文件错误，未发现control文件")
    }
}

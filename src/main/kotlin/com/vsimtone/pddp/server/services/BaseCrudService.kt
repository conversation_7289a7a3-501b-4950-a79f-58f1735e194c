package com.vsimtone.pddp.server.services

import com.querydsl.core.types.Expression
import com.querydsl.core.types.Order
import com.querydsl.core.types.OrderSpecifier
import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.EntityPathBase
import com.querydsl.core.types.dsl.NumberPath
import com.querydsl.jpa.impl.JPAQuery
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.PageResult
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.entities.BaseEntity
import com.vsimtone.pddp.server.repositories.BaseRepository
import org.springframework.context.ApplicationContext
import org.springframework.core.GenericTypeResolver
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.ReflectionUtils
import java.util.*

open class BaseCrudService<T : BaseEntity, G : BaseRepository<T>>(open var a: EntityPathBase<T>) : BaseService() {

    open lateinit var cls: Class<T>;
    open lateinit var repoCls: Class<G>;
    open lateinit var repo: G

    init {
        val t = GenericTypeResolver.resolveTypeArguments(javaClass, BaseCrudService::class.java)
        cls = t[0] as Class<T>
        repoCls = t[1] as Class<G>
    }


    fun initCrud(applicationContext: ApplicationContext) {
        repo = applicationContext.autowireCapableBeanFactory.getBean(repoCls) ?: throw RuntimeException("Repo not found ${repoCls}")
    }

    open fun getOrNull(p: Optional<T>): T? {
        if (p.isPresent) return p.get()
        return null
    }

    open fun findById(id: Long): T {
        return repo.findById(id).get()
    }

    open fun findByIdIf(id: Long?): T? {
        if (id == null) return null;
        val p = repo.findById(id)
        if (p.isPresent) return p.get()
        return null
    }

    open fun findByIds(ids: List<Long>): MutableList<T> {
        return ids.map {
            val p = repo.findById(it)
            if (p.isPresent)
                p.get()
            else
                null
        }.filterNotNull().toMutableList()
    }

    @Transactional(propagation = Propagation.MANDATORY)
    open fun save(d: T) {
        repo.save(d)
    }

    open fun delete(d: T) {
        return repo.delete(d)
    }

    open fun findOne(p: Predicate): T? {
        return getOrNull(repo.findOne(p))
    }

    open fun findAll(p: Predicate): Iterable<T> {
        return repo.findAll(p)
    }

    open fun findAll(): Iterable<T> {
        return repo.findAll()
    }

    open fun query(): JPAQuery<T> {
        return queryFactory.from(a) as JPAQuery<T>
    }

    open fun query(select: Expression<*>, where: Predicate): List<Any> {
        return queryFactory.from(a).select(select).where(where).fetch()
    }

    open fun distinctQuery(select: Expression<*>, where: Predicate): List<Any> {
        return queryFactory.from(a).select(select).distinct().where(where).fetch()
    }

    open fun query(select: Expression<*>, where: Predicate, group: Expression<*>): List<Any> {
        return queryFactory.from(a).select(select).where(where).groupBy(group).fetch()
    }

    open fun count(): Long {
        return repo.count()
    }

    open fun count(p: Predicate): Long {
        return repo.count(p)
    }

    open fun countIsZero(p: Predicate): Boolean {
        return repo.findAll(p, PageAttr(1, 1, null).get()).isEmpty
    }

    open fun findAll(queries: Predicate, pageAttr: PageAttr): PageResult<T> {
        val p = pageAttr.get()
        val q = queryFactory.from(a).where(queries).limit(p.pageSize.toLong()).offset(p.offset)
        p.sort.forEach {
            val e = (ReflectionUtils.findField(a.javaClass, it.property) ?: throw RestException(500, "sort field error")).get(a)!! as Expression<Comparable<*>>
            if (it.isAscending)
                q.orderBy(OrderSpecifier(Order.ASC, e))
            else
                q.orderBy(OrderSpecifier(Order.DESC, e))
        }
        val content = q.fetch() as List<T>
        var totalElements = p.offset + content.size;
        if (content.size >= p.pageSize)
            totalElements += 1;
        if (pageAttr.countTotal) {
            val idPath = ReflectionUtils.findField(a.javaClass, "id").get(a) as NumberPath<Long>
            totalElements = queryFactory.from(a).where(queries).select(idPath.count()).fetchOne() ?: 0
        }
        return PageResult(content, p, totalElements)
    }

    open fun getReferenceById(id: Long): T {
        return repo.getReferenceById(id)
    }

}
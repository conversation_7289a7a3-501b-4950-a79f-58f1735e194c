package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.QSysRole
import com.vsimtone.pddp.server.entities.SysRole
import com.vsimtone.pddp.server.repositories.SysRoleRepository
import com.vsimtone.pddp.server.utils.YAMLUtils
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.stereotype.Service
import org.springframework.util.ResourceUtils

@Service
class SysRoleService : BaseCrudService<SysRole, SysRoleRepository>(QSysRole.a) {
    fun initRole() {
        val roles = YAMLUtils.toObject(FileUtils.readFileToString(ResourceUtils.getFile("classpath:sys_role.yml"), Charsets.UTF_8), Array<SysRole>::class.java)
        roles.forEach { form ->
            form.permissions.forEach {
                try {
                    if (!it.startsWith("@"))
                        Permissions::class.java.getField(it)
                } catch (e: Exception) {
                    throw RuntimeException("Permission code not exists: ${it}")
                }
            }
        }
        roles.forEach {
            val form = it
            val data = repo.findByCode(form.code) ?: SysRole()
            val oldName = data.name
            val oldPermissions = data.permissions
            data.code = form.code
            data.name = form.name

            data.permissions = mutableListOf()
            form.permissions.forEach { _perm ->
                if (_perm.startsWith("@")) {
                    val refRole = roles.find { it.code.toString() == _perm.substring(1) } ?: throw RuntimeException("Reference role not found ${_perm}")
                    refRole.permissions.forEach {
                        if (it.startsWith("@")) throw RuntimeException("Unsupported circle reference")
                        if (!data.permissions.contains("ROLE_${it}"))
                            data.permissions.add("ROLE_${it}")
                    }
                } else {
                    data.permissions.add("ROLE_${_perm}")
                }
            }
            data.permissions.sort()
            oldPermissions.sort()
            if (data.id == null || !StringUtils.equals(data.name, oldName) || !oldPermissions.toTypedArray().contentEquals(data.permissions.toTypedArray())) {
                logger.info("Update sys role ${data.name} ${data.code} ${data.permissions.joinToString(",")}")
                repo.save(data)
            }
        }
    }

    fun getPermissions(roles: List<SysRole>): List<String> {
        val permissions = mutableListOf<String>()
        roles.forEach { sysRole ->
            sysRole.permissions.forEach {
                if (permissions.indexOf(it) == -1)
                    permissions.add(it)
            }
        }
        return permissions
    }

    fun getRolesByPerm(perm: String): List<SysRole> {
        return findAll().filter { it.permissions.indexOf(perm) != -1 }
    }

}

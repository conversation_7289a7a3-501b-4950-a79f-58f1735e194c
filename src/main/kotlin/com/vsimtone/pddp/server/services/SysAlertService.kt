package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.beans.PerfMonStats
import com.vsimtone.pddp.server.entities.QEdgeNode
import com.vsimtone.pddp.server.utils.DateUtil
import com.vsimtone.pddp.server.utils.RedisUtils
import org.redisson.Redisson
import org.redisson.api.RList
import org.redisson.api.redisnode.RedisNodes
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.sql.Timestamp
import kotlin.math.abs
import kotlin.reflect.KMutableProperty

@Service
class SysAlertService : BaseService() {

    @Autowired
    lateinit var redisson: Redisson

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var sysService: SysService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var perfMonService: PerfMonService

    lateinit var sysAlerts: RList<SysAlert>

    var alertProviders = mutableMapOf<String, () -> List<SysAlert>>()

    companion object {

        class AlertRule {
            lateinit var msg: String
            lateinit var name: String
            lateinit var valueField: KMutableProperty<Double>
            var lessThan: Double? = null
            var graterThan: Double? = null
            var size = 1

            constructor(msg: String, name: String, valueField: KMutableProperty<Double>, lessThan: Double?, graterThan: Double?, size: Int) {
                this.msg = msg
                this.name = name
                this.valueField = valueField
                this.lessThan = lessThan
                this.graterThan = graterThan
                this.size = size
            }
        }

        private val alertRules = mutableListOf<AlertRule>()

        init {
            alertRules.add(AlertRule("90%消息处理耗时过高(大于1秒)", "msg_process_time", PerfMonStats::p90, null, 1000.0, 5));
            alertRules.add(AlertRule("90%消息调用耗时过高(大于30秒)", "msg_invoke_time", PerfMonStats::p90, null, 1000.0 * 30, 5));
            alertRules.add(AlertRule("平均消息调用耗时过高(大于15秒)", "msg_invoke_time", PerfMonStats::avg, null, 1000.0 * 15, 5));
            alertRules.add(AlertRule("CPU使用率过高(大于90%)", "sys.cpu.usage", PerfMonStats::avg, null, 90.0, 2));
            alertRules.add(AlertRule("内存使用率过高(大于90%)", "sys.mem.usage", PerfMonStats::avg, null, 90.0, 2));
            alertRules.add(AlertRule("数据磁盘使用率过高(大于90%)", "sys.data_disk.usage", PerfMonStats::last, null, 90.0, 1));
            alertRules.add(AlertRule("根磁盘使用率过高(大于90%)", "sys.root_disk.usage", PerfMonStats::last, null, 90.0, 1));
        }

        class SysAlert : java.io.Serializable {
            var time: Long = System.currentTimeMillis()
            lateinit var provider: String
            lateinit var host: String
            lateinit var msg: String

            constructor()
            constructor(host: String, msg: String) {
                this.host = host
                this.msg = msg
                this.provider = ""
            }

            override fun toString(): String {
                return "SysAlert(time=$time, provider='$provider', host='$host', msg='$msg')"
            }
        }
    }

    fun addAlertProvider(name: String, cb: () -> List<SysAlert>) {
        if (alertProviders.containsKey(name)) throw RuntimeException("Sys alert repeat.")
        alertProviders[name] = cb;
    }

    override fun onInit() {
        sysAlerts = redisson.getList("sys.alerts", RedisUtils.autoCodec(SysAlert::class.java))
        alertProviders.put("redis_time_check") {
            val alerts = mutableListOf<SysAlert>()
            val t = redisService.redisson.getRedisNodes(RedisNodes.SINGLE).instance.time().seconds * 1000L;
            val diff = abs(System.currentTimeMillis() - t)
            if (diff >= 300 * 1000) {//时间差大于5分钟
                alerts.add(SysAlert("Redis服务器", "时间差过大: ${diff / 1000}秒"))
            }
            return@put alerts
        }
        alertProviders.put("db_time_check") {
            val alerts = mutableListOf<SysAlert>()
            val t = entityManager.createNativeQuery("select now()").singleResult as Timestamp
            val diff = abs(System.currentTimeMillis() - t.time)
            if (diff >= 300 * 1000) {//时间差大于5分钟
                alerts.add(SysAlert("数据库服务器", "时间差过大: ${diff / 1000}秒"))
            }
            return@put alerts
        }
        addAlertProvider("perf_mon_alert") {
            this.doAlert()
        }
        addAlertProvider("check_server_time") {
            this.doAlertServerTime()
        }

    }

    override fun onReady() {
        schedule("sys.alert.check", DateUtil.minute(1)) {
            val alerts = mutableListOf<SysAlert>()
            alertProviders.forEach { p ->
                p.value().forEach { sysAlert ->
                    sysAlert.provider = p.key
                    logger.warn("$sysAlert")
                    alerts.add(sysAlert)
                }
            }
            sysAlerts.clear()
            sysAlerts.addAll(alerts)
        }
    }

    private fun doAlertServerTime(): List<SysAlert> {
        var baseOnlineTime = 0L;
        val alerts = mutableListOf<SysAlert>();
        val nodes = sysService.serverNodes.values
        val scores = mutableMapOf<String, Int>()
        nodes.forEach { n1 ->
            nodes.forEach { n2 ->
                if (scores[n1.id] == null) scores[n1.id] = 0
                if (scores[n2.id] == null) scores[n2.id] = 0
                if (n1 != n2 && Math.abs(n1.onlineTime - n2.onlineTime) >= 30 * 1000) {
                    scores[n1.id] = scores[n1.id]!! + 1
                    scores[n2.id] = scores[n2.id]!! + 1
                }
            }
        }
        val maxScore = scores.values.maxOf { it }
        sysService.serverNodes.forEach {
            if (scores[it.value.id] != null && scores[it.value.id]!! > 0 && scores[it.value.id]!! >= maxScore) {
                alerts.add(SysAlert("主服务" + it.value.hostName, "系统时间差异超过30秒,请检查系统时间"))
            }
        }
        return alerts;
    }

    private fun doAlert(): List<SysAlert> {

        var targets = mutableListOf<Array<String>>()
        var names = alertRules.map { it.name };

        edgeNodeService.findAll(QEdgeNode.a.cluster.isNotNull).forEach {
            targets.add(arrayOf("edge_node_client:${it.id}", "代理节点-${it.hostname}"))
        }
        sysService.serverNodes.forEach {
            targets.add(arrayOf("main_server:${it.value.hostName}", "主服务-${it.value.hostName}"))
        }
        val alerts = mutableListOf<SysAlert>();
        targets.forEach f1@{ target ->
            names.forEach f2@{ name ->
                val alertRule = alertRules.find { it.name == name } ?: return@f2
                val filterTime = DateUtil.nowDate(DateUtil.minute(-(alertRule.size + 2).toLong())).time;
                val stats = mutableListOf<PerfMonStats>()
                perfMonService.findAll(target[0], name, false) {
                    if (it.time < filterTime) return@findAll false;
                    if (stats.size >= alertRule.size) return@findAll false;
                    stats.add(it);
                    return@findAll true;
                }
                val hit = stats.find {
                    val v = alertRule.valueField.getter.call(it)
                    if (alertRule.lessThan != null && v < alertRule.lessThan!!) return@find true
                    if (alertRule.graterThan != null && v > alertRule.graterThan!!) return@find true
                    false
                }
                if (hit != null) {
                    // do alert
                    logger.info("alert ... " + target + " " + name + " " + alertRule.name + " " + alertRule.valueField.getter.call(hit));
                    val sysAlert = SysAlert()
                    sysAlert.host = target[0]
                    sysAlert.msg = alertRule.msg
                    alerts.add(sysAlert)
                }
            }
        }

        return alerts;
    }

}
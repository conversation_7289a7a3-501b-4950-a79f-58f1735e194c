package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.utils.DateUtil
import com.vsimtone.pddp.server.utils.JSONUtils
import jakarta.annotation.PostConstruct
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.io.IOException
import java.net.URLEncoder
import java.util.concurrent.TimeUnit

@Service
class HTTPService {
    private val logger = LoggerFactory.getLogger("http")
    lateinit var client: OkHttpClient

    @PostConstruct
    fun init() {
        var builder = OkHttpClient.Builder();
        builder.readTimeout(60, TimeUnit.SECONDS)
        builder.connectTimeout(15, TimeUnit.SECONDS)
        builder.writeTimeout(15, TimeUnit.SECONDS)
        client = builder.build()
    }

    fun mapToQueryString(params: Map<String, Any?>): String {
        var qs = mutableListOf<String>()
        params.forEach {
            if (it.value != null)
                qs.add(URLEncoder.encode(it.key, "UTF-8") + "=" + URLEncoder.encode(it.value.toString(), "UTF-8"))
        }
        return qs.joinToString("&")
    }

    fun urlConcat(_url: String, qs: String): String {
        var url = _url;
        if (url.indexOf("?") == -1)
            url = url + "?" + qs;
        else
            url = url + "&" + qs;
        return url;
    }
    
    @Throws(IOException::class)
    fun invoke(url: String, body: RequestBody?): String {
        val time = System.currentTimeMillis()
        var _url: String = url
        var method: String? = null;
        var request: Request? = null
        val requestBuilder = Request.Builder().url(_url)
        if (body != null) {
            method = "POST";
            request = requestBuilder.post(body).build()
        } else {
            method = "GET";
            request = requestBuilder.get().build()
        }
        val response = client.newCall(request).execute()
        var result = response.body!!.string()
        result = result.replace("err-code", "err_code")
        result = result.replace("err-msg", "err_msg")
        if (result.length <= 512)
            logger.debug(method + " " + _url + ", use  " + DateUtil.useTimeToHum(System.currentTimeMillis() - time) + ", result is " + result)
        else
            logger.debug(method + " " + _url + ", use  " + DateUtil.useTimeToHum(System.currentTimeMillis() - time) + ", result length is " + result.length)
        return result;
    }

    fun get(_url: String, params: Map<String, Any?>, c: Class<*>): Any {
        var qs = mapToQueryString(params)
        return JSONUtils.toObject(invoke(urlConcat(_url, qs), null), c);
    }

}
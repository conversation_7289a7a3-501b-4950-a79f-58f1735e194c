package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.entities.APPConfig
import com.vsimtone.pddp.server.utils.DateUtil
import jakarta.mail.internet.MimeMessage
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RBlockingQueue
import org.redisson.api.RedissonClient
import org.redisson.client.codec.LongCodec
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.JavaMailSenderImpl
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.stereotype.Service

@Service
class MailService : BaseService() {

    @Autowired
    lateinit var redissonClient: RedissonClient

    companion object {
        const val CNF_NAME_MAIL_HOST = "core.mail.host";
        const val CNF_NAME_MAIL_PORT = "core.mail.port";
        const val CNF_NAME_MAIL_USERNAME = "core.mail.username";
        const val CNF_NAME_MAIL_PASSWORD = "core.mail.password";
        const val CNF_NAME_MAIL_PROTOCOL = "core.mail.protocol";
        const val CNF_NAME_MAIL_CRYPT_PROTOCOL = "core.mail.crypt_protocol";
        const val CNF_NAME_MAIL_SEND_INTERVAL = "core.mail.send_interval";

        class MailMsg : java.io.Serializable {
            lateinit var title: String
            lateinit var content: String
            lateinit var to: String

            constructor()
            constructor(title: String, content: String, to: String) {
                this.title = title
                this.content = content
                this.to = to
            }
        }
    }

    @Autowired
    private lateinit var appConfigService: APPConfigService


    private lateinit var queue: RBlockingQueue<MailMsg>

    override fun onReady() {
        queue = redissonClient.getBlockingQueue("mail.send.queue")
        appConfigService.init(CNF_NAME_MAIL_HOST, "", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_PORT, 465, APPConfig.Type.Integer)
        appConfigService.init(CNF_NAME_MAIL_USERNAME, "", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_PASSWORD, "", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_PROTOCOL, "smtp", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_CRYPT_PROTOCOL, "ssl", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_SEND_INTERVAL, 0, APPConfig.Type.Integer)
        schedule("mail.send", DateUtil.minute(1)) {
            doSend()
        }
    }

    private fun doSend() {
        var msg: MailMsg? = null
        val sendTimeBucket = redissonClient.getBucket<Long>("mail.send.do_send_time", LongCodec())
        var sendTime = sendTimeBucket.get() ?: 0;
        val sendInterval = appConfigService.getInteger(CNF_NAME_MAIL_SEND_INTERVAL)!!
        var sendCount = 0;
        var mailSender: JavaMailSender? = null
        while (true) {
            try {
                if (mailSender == null || sendCount % 10 == 0)
                    mailSender = getJavaMailSender()
                msg = queue.poll()
                if (msg != null) {
                    if (mailSender == null) {
                        logger.info("Mail " + msg.title + " " + msg.to + " send skip, mail config not set.")
                        continue
                    }
                    if (sendInterval > 0) {
                        val waitTime = sendInterval - System.currentTimeMillis() - sendTime
                        if (waitTime > 0L) {
                            Thread.sleep(waitTime)
                        }
                    }
                    sendTime = System.currentTimeMillis()
                    mailSender.send(createMsg(msg.title, msg.content, msg.to))
                    sendCount++
                    logger.info("Mail " + msg.title + " " + msg.to + " send success, use time " + DateUtil.useTimeToHum(System.currentTimeMillis() - sendTime) + ", current queue size " + queue.size + "")
                } else {
                    break
                }
                msg = null
            } catch (e: Exception) {
                if (e is InterruptedException) return
                if (e !is NullPointerException) logger.error("Mail send fail", e)
                if (msg != null) logger.error("Mail " + msg.title + " " + msg.to + " send error", e)
                break;
            }
        }
        sendTimeBucket.set(sendTime)
    }

    fun getJavaMailSender(): JavaMailSender? {
        val mailSender = JavaMailSenderImpl();
        mailSender.host = appConfigService.getString(CNF_NAME_MAIL_HOST);
        mailSender.port = appConfigService.getInteger(CNF_NAME_MAIL_PORT)!!;
        mailSender.username = appConfigService.getString(CNF_NAME_MAIL_USERNAME);
        mailSender.password = appConfigService.getString(CNF_NAME_MAIL_PASSWORD);
        if (StringUtils.isAllEmpty(mailSender.host) || StringUtils.isAllEmpty(mailSender.username) || StringUtils.isAllEmpty(mailSender.password)) {
            return null;
        }
        val props = mailSender.javaMailProperties
        props["mail.transport.protocol"] = appConfigService.getString(CNF_NAME_MAIL_PROTOCOL);
        props["mail.smtp.auth"] = "true"
        props["mail.debug"] = "false"
        val ep = appConfigService.getString(CNF_NAME_MAIL_CRYPT_PROTOCOL);
        if (ep == "starttls") props["mail.smtp.starttls.enable"] = "true";
        if (ep == "ssl") {
            props["mail.smtp.ssl.enable"] = "true";
        }
        return mailSender;
    }

    fun createMsg(subject: String, content: String, to: String): MimeMessage {
        val mailSender = getJavaMailSender() ?: throw RuntimeException("Mail config not set")
        val mimeMessage = mailSender.createMimeMessage()
        val helper = MimeMessageHelper(mimeMessage, true, "UTF-8")
        helper.setFrom(appConfigService.getString(CNF_NAME_MAIL_USERNAME)!!)
        helper.setTo(to)
        helper.setSubject(subject)
        helper.setText(content, true)
        return mimeMessage
    }

    fun lazySend(subject: String, content: String, to: String) {
        queue.add(MailMsg(subject, "<pre>$content</pre>", to))
    }

    fun directSend(subject: String, content: String, to: String) {
        val mailSender = getJavaMailSender() ?: throw RuntimeException("Mail config not set")
        mailSender.send(createMsg(subject, "<pre>$content</pre>", to))
    }

}
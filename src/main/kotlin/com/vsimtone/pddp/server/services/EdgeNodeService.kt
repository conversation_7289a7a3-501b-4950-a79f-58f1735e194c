package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.controllers.edge_node.msg_handlers.EdgeNodePingMsgHandler
import com.vsimtone.pddp.server.entities.APPConfig
import com.vsimtone.pddp.server.entities.BaseEntity
import com.vsimtone.pddp.server.entities.FileInfo
import com.vsimtone.pddp.server.entities.EdgeNode
import com.vsimtone.pddp.server.entities.EdgeNodeCluster
import com.vsimtone.pddp.server.entities.QEdgeNode
import com.vsimtone.pddp.server.repositories.EdgeNodeRepository
import com.vsimtone.pddp.server.utils.CryptUtils
import com.vsimtone.pddp.server.utils.JSONUtils
import com.vsimtone.pddp.server.utils.RateLimitOutput
import com.vsimtone.pddp.server.utils.RestUtil
import jakarta.websocket.CloseReason
import jakarta.websocket.Session
import org.apache.commons.codec.binary.Hex
import org.apache.commons.codec.digest.MessageDigestAlgorithms
import org.apache.commons.compress.compressors.zstandard.ZstdCompressorInputStream
import org.apache.commons.compress.compressors.zstandard.ZstdCompressorOutputStream
import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.StringUtils
import org.msgpack.core.MessagePack
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.Serializable
import java.security.MessageDigest
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream
import kotlin.jvm.java

@Service
class EdgeNodeService : BaseCrudService<EdgeNode, EdgeNodeRepository>(QEdgeNode.a) {

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var sysService: SysService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var orgService: OrgService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var sysAlertService: SysAlertService

    companion object {

        enum class WSMsgType { Request, Response }

        enum class WSMsgDataFormat { BASE64, CERT_CRYPT, GZIP, ZSTD }

        const val CNF_KEY_GLOBAL_EDGE_NODE_CLUSTER_CONFIG = "core.global.edge_node_cluster_config"

        val PACK_CRYPT_FORMAT = listOf(WSMsgDataFormat.ZSTD, WSMsgDataFormat.CERT_CRYPT)
        val PACK_FORMAT = listOf(WSMsgDataFormat.ZSTD)
        var edgeNodeGlobalConfig = EdgeNodeCluster.Companion.EdgeNodeClusterConfig()

        open class WSMsgHandler<T> {
            lateinit var argsType: Class<T>
            lateinit var handler: (data: T, msg: WSMsg, sess: WSSession) -> Any
        }

        class WSMsg : Serializable, Cloneable {
            lateinit var id: String
            lateinit var type: WSMsgType
            lateinit var method: String
            var priority = RateLimitOutput.Companion.Priority.Normal
            var createTime: Long? = null
            var expireTime: Long? = null
            var writeTime: Long? = null
            var error: String? = null
            var sign: String? = null
            var data: ByteArray? = null
            var dataFormat: List<WSMsgDataFormat>? = null
            var dataEnd = false
            override fun toString(): String {
                return "WSMsg(id='$id', type=$type, method='$method', priority=$priority, createTime=$createTime, writeTime=$writeTime, expireTime=$expireTime, error=$error, sign=$sign, dataSize=${data?.size}, dataFormat=$dataFormat, dataEnd=$dataEnd)"
            }
        }
    }

    init {
        readyInvokeOrder = READY_INVOKE_LOW;
    }

    override fun onReady() {
        appConfigService.init(
            CNF_KEY_GLOBAL_EDGE_NODE_CLUSTER_CONFIG,
            JSONUtils.toString(EdgeNodeCluster.Companion.EdgeNodeClusterConfig()),
            APPConfig.Type.String
        );
        findAll(QEdgeNode.a.cluster.belongOrg.isNull).forEach {
            it.cluster!!.belongOrg = orgService.getSysOrg(OrgService.SYS_ORG_KEY_ROOT);
            save(it);
            this.logger.info("set org to root ${it}")
        }
        sysAlertService.addAlertProvider("edge_node_alive_check") {
            val alters = mutableListOf<SysAlertService.Companion.SysAlert>()
            findAll(QEdgeNode.a.cluster.isNotNull).forEach {
                if (it.status == EdgeNode.Status.Offline) {
                    alters.add(SysAlertService.Companion.SysAlert("代理节点-${it.hostname}", "已离线"))
                }
            }
            alters
        }
        updateGlobalConfig();
    }

    @Scheduled(fixedDelay = 60L * 1000L, initialDelay = 30 * 1000L)
    fun edgeNodeLoadBalance() {
        if (!ready) return;
        if (this.sysService.thisNodeInfo.edgeNodeCount <= 1) return;
        if (!this.sysService.thisNodeInfo.runtimeConfig.enableProxyLoadBalance) return;
        val thisEdgeNodeCount = this.sysService.thisNodeInfo.edgeNodeCount;
        var minEdgeNodeCount = thisEdgeNodeCount;
        this.sysService.serverNodes.values.forEach {
            if (minEdgeNodeCount > it.edgeNodeCount)
                minEdgeNodeCount = it.edgeNodeCount
        }
        if (thisEdgeNodeCount - minEdgeNodeCount >= 2) {
            val sess = wsSessions.values.randomOrNull()
            if (sess != null) {
                this.logger.info("Close edge node by load balance: ${sess.edgeNode}, $thisEdgeNodeCount $minEdgeNodeCount")
                sess.session.close(CloseReason(CloseReason.CloseCodes.SERVICE_RESTART, "Load balance restart."))
            }
        }
    }

    @Scheduled(fixedDelay = 30 * 1000L)
    fun updateGlobalConfig() {
        edgeNodeGlobalConfig = JSONUtils.toObject(appConfigService.getString(CNF_KEY_GLOBAL_EDGE_NODE_CLUSTER_CONFIG)!!, EdgeNodeCluster.Companion.EdgeNodeClusterConfig::class.java)
    }

    fun getGlobalConfig(): EdgeNodeCluster.Companion.EdgeNodeClusterConfig {
        return edgeNodeGlobalConfig;
    }

    override fun save(d: EdgeNode) {
        super.save(d);
        removeCache("edge_node:findById", d.id.toString());
    }

    override fun delete(d: EdgeNode) {
        super.delete(d)
        removeCache("edge_node:findById", d.id.toString());
    }

    fun findByNodeId(nodeId: String): EdgeNode? {
        return repo.findByNodeId(nodeId)
    }

    fun getCryptDownData(node: EdgeNode, f: FileInfo?): Map<String, Any> {
        if (f == null) throw RestException(1, "File token not found")
//        if (f.savedOn != FileInfo.SavedOn.EdgeNode) throw RestException(1, "File not in edge_node")
        var outCryptKey = Hex.encodeHexString(CryptUtils.genKey())
        var outCryptIV = Hex.encodeHexString(CryptUtils.genKey())
        var data = mutableMapOf<String, Any>(
            "time" to System.currentTimeMillis(),
            "expiredIn" to 300 * 1000,// 5分钟过期时间
//            "token" to f.token,
            "cryptKey" to outCryptKey,
            "cryptIV" to outCryptIV,
            "cryptChunkSize" to 1024 * 1024 * 1,  // 1MB
        )
        node.certKeyPair._keyPair = certKeyPairService.toKeyPair(node.certKeyPair)
        return mapOf(
            "downPageUrl" to node.httpUrl + "pnd/",
            "nodeName" to node.hostname,
//            "b3sum" to f.b3sum,
            "decryptKey" to outCryptKey,
            "decryptIV" to outCryptIV,
            "cryptChunkSize" to 1024 * 1024 * 1,  // 1MB
            "requestData" to CryptUtils.base64encode(
                certKeyPairService.encodeData(
                    JSONUtils.toString(data).toByteArray(Charsets.UTF_8), node.certKeyPair
                )
            )
        )
    }


    // websocket process

    var msgHandlers = mutableMapOf<String, WSMsgHandler<*>>()

    var wsSessions = mutableMapOf<String, WSSession>()

    var pendingWSMsg = mutableMapOf<String, WSMsg>()

    var msgHandlerThreadPool = ThreadPoolExecutor(10, 6000, 60, TimeUnit.SECONDS, ArrayBlockingQueue(1024 * 1024));

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var certKeyPairService: CertKeyPairService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var redisService: RedisService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var perfMonService: PerfMonService

    inner class WSSession {
        var session: Session
        var edgeNode: EdgeNode? = null
        var rateLimitOutput: RateLimitOutput
        var closed = false
        var fromIP = ""

        constructor(_session: Session) {
            this.session = _session
            this.rateLimitOutput = RateLimitOutput(1024 * 1024)
            this.fromIP = (_session.userProperties.get("ClientIP")!!).toString()
        }

        fun msgPerfMon(msg: WSMsg, name: String, value: Double) {
            if (this.edgeNode == null || (this.edgeNode?.cluster?.config?.perfMonUploadType ?: getGlobalConfig().perfMonUploadType) == EdgeNode.PerfMonUploadType.None) return;
            var target = "main_server:" + sysService.thisNodeInfo.hostName
            if (StringUtils.isNotEmpty(msg.error)) return
            var attrs = mutableMapOf(
                "id" to msg.id,
                "type" to msg.type.toString(),
                "method" to msg.method,
                "edgeNode" to this.edgeNode!!.id!!.toString()
            )
            perfMonService.addPerfMon(target, name, attrs, value);
        }

        fun packData(msg: WSMsg, _data: Any, formats: List<WSMsgDataFormat>): ByteArray {
            var started = System.currentTimeMillis()
            var data: ByteArray
            if (_data is ByteArray)
                data = _data
            else if (_data is String)
                data = _data.toByteArray(Charsets.UTF_8)
            else
                data = JSONUtils.toString(_data).toByteArray(Charsets.UTF_8)
            var srcLen = data.size
            formats.forEach {
                if (it == WSMsgDataFormat.GZIP) {
                    val baos = ByteArrayOutputStream()
                    val gzip = GZIPOutputStream(baos)
                    gzip.write(data)
                    gzip.close()
                    data = baos.toByteArray()
                } else if (it == WSMsgDataFormat.ZSTD) {
                    val baos = ByteArrayOutputStream()
                    val zstd = ZstdCompressorOutputStream(baos)
                    zstd.write(data)
                    zstd.close()
                    data = baos.toByteArray()
                } else if (it == WSMsgDataFormat.BASE64) {
                    data = CryptUtils.base64encode(data).toByteArray(Charsets.UTF_8)
                } else if (it == WSMsgDataFormat.CERT_CRYPT) {
                    data = certKeyPairService.encodeData(data, edgeNode!!.certKeyPair)
                } else {
                    throw RuntimeException("Unsupported data format")
                }
            }
            msgPerfMon(msg, "msg_pack_time", (System.currentTimeMillis() - started).toDouble());
            msgPerfMon(
                msg,
                "msg_compress_rate",
                perfMonService.percentage(srcLen.toDouble(), data.size.toDouble())
            );
            msgPerfMon(msg, "msg_data_size", srcLen.toDouble());
            return data
        }

        fun <T> unpackData(msg: WSMsg, _data: ByteArray, _formats: List<WSMsgDataFormat>, cls: Class<T>): T {
            var started = System.currentTimeMillis()
            val formats = _formats.reversed()
            var data: ByteArray = _data;
            formats.forEach {
                if (it == WSMsgDataFormat.GZIP) {
                    val gzip = GZIPInputStream(ByteArrayInputStream(data))
                    data = IOUtils.toByteArray(gzip)
                } else if (it == WSMsgDataFormat.ZSTD) {
                    val zstd = ZstdCompressorInputStream(ByteArrayInputStream(data))
                    data = IOUtils.toByteArray(zstd)
                } else if (it == WSMsgDataFormat.BASE64) {
                    data = CryptUtils.base64decode(data.decodeToString())
                } else if (it == WSMsgDataFormat.CERT_CRYPT) {
                    data = certKeyPairService.decodeData(data, certKeyPairService.serverCertKeyPair)
                } else {
                    throw RuntimeException("Unpack data fail: Unsupported data format")
                }
            }
            if (cls == ByteArray::class.java) {
                return data as T;
            }
            if (cls == String::class.java) {
                return data.toString(Charsets.UTF_8) as T;
            }
            val rd = JSONUtils.toObject(data.decodeToString(), cls);
            msgPerfMon(msg, "msg_unpack_time", (System.currentTimeMillis() - started).toDouble());
            return rd;
        }

        fun getMsgHash(msg: WSMsg, data: ByteArray): ByteArray {
            val digest = MessageDigest.getInstance(MessageDigestAlgorithms.SHA_256)
            digest.update(
                ("${msg.id},${msg.method},${msg.type.name},${msg.createTime ?: 0L},${msg.expireTime ?: 0L}#").toByteArray(
                    Charsets.UTF_8
                )
            )
            digest.update(data)
            return digest.digest()
        }

        fun checkSign(msg: WSMsg, data: ByteArray) {
            val offset = System.currentTimeMillis() - msg.createTime!!
            if (offset > 1000 * 600)
                throw RuntimeException("Msg time verify failed. create=${BaseEntity.DEFAULT_DATE_FORMAT.format(msg.createTime)}, offset=${offset}")
            val msgHash = getMsgHash(msg, data)
            var signOk =
                certKeyPairService.verifySign(msgHash, CryptUtils.base64decode(msg.sign!!), edgeNode!!.certKeyPair)
            if (!signOk)
                throw RuntimeException("Msg sign verify failed : " + Hex.encodeHexString(msgHash))
        }

        private fun sendData(p: RateLimitOutput.Companion.Priority, msg: String, data: ByteArray) {
//            log.info("Write msg " + msg + " data " + Hex.encodeHexString(data))
            val buf = MessagePack.newDefaultBufferPacker()
            buf.packString(msg)
            buf.packBinaryHeader(data.size)
            buf.addPayload(data)
            this.rateLimitOutput.writeByPriority({
                session.basicRemote.sendStream
            }, p, buf.toByteArray())
        }


        private fun writeMsg(msg: WSMsg, data: ByteArray) {
            var sendTime = System.currentTimeMillis()
            var serverDownSpeedLimit = edgeNode!!.cluster?.config?.serverDownSpeedLimit ?: getGlobalConfig().serverDownSpeedLimit;
            var buf: ByteArray
            var bufLen = (serverDownSpeedLimit / 100).toInt(); // min 4KB max 1MB
            if (bufLen < 1024 * 4)
                bufLen = 1024 * 4;
            if (bufLen >= 1024 * 1024)
                bufLen = 1024 * 1024;
            var sliceFrom = 0;
            var sliceTo: Int;
            var dataEndIdx = data.size - 1
            var splitCount = 0;
            while (true) {
                sliceTo = sliceFrom + bufLen - 1
                if (sliceTo > dataEndIdx)
                    sliceTo = dataEndIdx
                buf = data.sliceArray(IntRange(sliceFrom, sliceTo))
                sliceFrom = sliceTo + 1
                var isEnd = sliceFrom > dataEndIdx
                if (splitCount++ == 0) {
                    msg.dataEnd = isEnd
                    this.sendData(msg.priority, JSONUtils.toString(msg), buf)
                } else {
                    val splitMsg = WSMsg()
                    splitMsg.id = msg.id
                    splitMsg.type = msg.type
                    splitMsg.method = msg.method
                    splitMsg.dataEnd = isEnd
                    splitMsg.priority = msg.priority
                    this.sendData(msg.priority, JSONUtils.toString(splitMsg), buf)
                }
                if (isEnd) break
            }
            msgPerfMon(msg, "msg_send_time", (System.currentTimeMillis() - sendTime).toDouble());
            msgPerfMon(msg, "msg_process_time", (System.currentTimeMillis() - msg.createTime!!).toDouble());
        }

        fun writeResult(msg: WSMsg, _data: Any) {
            val respMsg = WSMsg()
            respMsg.id = msg.id
            respMsg.method = msg.method
            respMsg.type = WSMsgType.Response
            respMsg.dataFormat = PACK_CRYPT_FORMAT
            respMsg.createTime = System.currentTimeMillis()
            respMsg.expireTime = 0L
            respMsg.priority = msg.priority
            val data = packData(msg, _data, PACK_CRYPT_FORMAT)
            respMsg.sign = CryptUtils.base64encode(
                certKeyPairService.signData(
                    getMsgHash(respMsg, data),
                    certKeyPairService.serverCertKeyPair
                )
            )
            this.writeMsg(respMsg, data)
        }

        fun writeError(msg: WSMsg, _error: String) {
            val respMsg = WSMsg()
            respMsg.id = msg.id
            respMsg.method = msg.method
            respMsg.type = WSMsgType.Response
            respMsg.error = _error
            respMsg.createTime = System.currentTimeMillis()
            respMsg.dataEnd = true
            return this.sendData(msg.priority, JSONUtils.toString(respMsg), ByteArray(0))
        }

        override fun toString(): String {
            return "WSSession(session=${session.id}, edgeNode=$edgeNode), fromIP=${fromIP}"
        }

    }


    private fun invokeHandler(msg: WSMsg, wsSess: WSSession) {
        val handler = (msgHandlers[msg.method] ?: throw RuntimeException("Method ${msg.method} not found")) as WSMsgHandler<Any>
        val args = wsSess.unpackData(msg, msg.data!!, msg.dataFormat!!, handler.argsType)
        try {
            val invokeStarted = System.currentTimeMillis()
            val resp = handler.handler(args, msg, wsSess);
            wsSess.msgPerfMon(msg, "msg_invoke_time", (System.currentTimeMillis() - invokeStarted).toDouble());
            wsSess.writeResult(msg, resp);
        } catch (e: Exception) {
            if (!wsSess.closed)
                wsSess.writeError(msg, e.message ?: "invoke unknown error")
            logger.error("invoke handler error ${msg.method}", e);
        }
    }

    fun <T> addMsgHandler(name: String, f: WSMsgHandler<T>) {
        if (msgHandlers[name] != null)
            throw RuntimeException("EdgeNodeController Msg handler repeat : $name")
        logger.info("Add msg handler ${name}")
        msgHandlers[name] = f
    }

    fun onWSOpen(session: Session) {
        if (!AppReady) throw RuntimeException("Server not ready")
        val wsSess = WSSession(session)
        appCtx.autowireCapableBeanFactory.autowireBean(this)
        appCtx.autowireCapableBeanFactory.autowireBean(wsSess)
        wsSessions[session.id] = wsSess
        logger.info("on open : ${wsSess}")
    }

    fun onWSMessage(session: Session, binaryMessage: ByteArray) {
        val wsSess = wsSessions[session.id]
        if (wsSess == null) {
            logger.error("Session not found : ${session}")
            session.close(CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "session not found"))
            return
        }
        val msgDecoder = MessagePack.newDefaultUnpacker(binaryMessage)
        val rawMsg = msgDecoder.unpackString()
        val data = msgDecoder.readPayload(msgDecoder.unpackBinaryHeader())

        var msg = JSONUtils.toObject(rawMsg, WSMsg::class.java)
        msg.data = data

        if (msg.expireTime!! > 0 && msg.writeTime != null && msg.writeTime!! > 0 && msg.writeTime!! > msg.expireTime!!) {
            wsSess.msgPerfMon(msg, "msg_timeout", 1.0);
            logger.error("${wsSess.edgeNode} Msg ${msg} expire, drop it.");
            return;
        }

        if (msg.type == WSMsgType.Response)
            TODO("Not support")

        var pendingMsg = pendingWSMsg[msg.id]
        if (!msg.dataEnd) {
            if (pendingMsg != null)
                pendingMsg.data = pendingMsg.data!!.plus(msg.data!!)
            else {
                pendingWSMsg[msg.id] = msg
            }
//            logger.info("on message : ${wsSess} ${rawMsg} ${data.size} ${pendingMsg.data!!.size}")
            return
        } else if (pendingMsg != null) {
            pendingMsg.data = pendingMsg.data!!.plus(msg.data!!)
            msg = pendingMsg
        }
        try {

            if (wsSess.edgeNode != null)
                wsSess.checkSign(msg, msg.data!!)

            if (msg.method == EdgeNodePingMsgHandler.NAME && (wsSess.edgeNode == null)) {
                invokeHandler(msg, wsSess)
                return;
            }

            if (wsSess.edgeNode == null) {
                return wsSess.writeError(msg, "edge_node_auth_fail")
            }

            if (wsSess.edgeNode!!.cluster != null && !wsSess.edgeNode!!.cluster!!.enabled) {
                return wsSess.writeError(msg, "edge_node_cluster_disabled")
            }

            msgHandlerThreadPool.submit() {
                try {
                    invokeHandler(msg, wsSess)
                } catch (e: Exception) {
                    logger.error("Invoke ${msg} error", e)
                    if (!wsSess.closed)
                        wsSess.writeError(msg, e.message ?: "msg unknown error")
                }
            }

        } catch (e: Exception) {
            logger.error("OnMessage ${msg} error", e)
            if (!wsSess.closed)
                return wsSess.writeError(msg, e.message ?: "msg unknown error")
        }
    }

    fun onWSClose(session: Session, reason: CloseReason) {
        val weSess = wsSessions[session.id]
        weSess?.closed = true;
        wsSessions.remove(session.id)
        logger.info("on close: ${weSess} ${reason}")
        val pid = weSess?.edgeNode?.id ?: return;
        runInTran {
            val edgeNode = findById(pid)
            edgeNode.status = EdgeNode.Status.Offline;
            save(edgeNode)
        }
    }

    fun onWSError(session: Session, e: Throwable) {
        val weSess = wsSessions[session.id]
        weSess?.closed = true;
        wsSessions.remove(session.id)
        logger.error("on error: ${weSess}", e)
    }

    fun checkConfig(cnf: EdgeNodeCluster.Companion.EdgeNodeClusterConfig) {
        if (cnf.serverDownSpeedLimit <= 160 * 1024) cnf.serverDownSpeedLimit = 160 * 1024
        if (cnf.serverUpSpeedLimit <= 160 * 1024) cnf.serverUpSpeedLimit = 160 * 1024
        if (cnf.clientConcurrentLimit < 10) cnf.clientConcurrentLimit = 10
        if (cnf.clientDownSpeedLimit < 10 * 1024) cnf.clientDownSpeedLimit = 10 * 1024
    }

    fun findByCluster(cluster: EdgeNodeCluster): List<EdgeNode> {
        return findAll(QEdgeNode.a.cluster.eq(cluster)).toMutableList()
    }


}
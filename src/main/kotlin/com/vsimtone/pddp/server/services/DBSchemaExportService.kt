package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.utils.Log
import jakarta.persistence.EntityManager
import org.apache.commons.lang3.StringUtils
import org.hibernate.boot.MetadataSources
import org.hibernate.engine.spi.SessionFactoryImplementor
import org.hibernate.metamodel.model.domain.internal.EntityTypeImpl
import org.hibernate.tool.hbm2ddl.SchemaUpdate
import org.hibernate.tool.schema.TargetType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.io.File
import java.util.*


@Service
class DBSchemaExportService {

    protected var logger = Log[this.javaClass];

    @Value("\${app.schema_update_file}")
    var schemaUpdateFile: String? = null

    @Autowired
    lateinit var entityManager: EntityManager

    fun doGenerate() {
        if (StringUtils.isAllBlank(schemaUpdateFile)) return
        logger.info("Generate schema update to ${schemaUpdateFile} start")
        val file = File(schemaUpdateFile!!)
        if (file.exists()) file.delete()
        val emf = entityManager.entityManagerFactory
        val sessionFactory = emf.unwrap(SessionFactoryImplementor::class.java)
        val serviceRegistry = sessionFactory.serviceRegistry.parentServiceRegistry
        val metadataSources = MetadataSources(serviceRegistry)
        entityManager.metamodel.entities.forEach {
            metadataSources.addAnnotatedClasses((it as EntityTypeImpl).javaType)
        }

//        val validate = SchemaValidator()
//        validate.validate(metadataSources.buildMetadata())

//        val export = SchemaExport()
//        export.setOutputFile(schemaUpdateFile)
//        export.createOnly(EnumSet.of(TargetType.SCRIPT), metadataSources.buildMetadata())

        val update = SchemaUpdate()
        update.setOutputFile(schemaUpdateFile)
        update.execute(EnumSet.of(TargetType.SCRIPT), metadataSources.buildMetadata());

        logger.info("Generate schema update success.")
        file.readLines().forEach {
            logger.info("Generate schema update sql: ${it}")
        }
    }
}

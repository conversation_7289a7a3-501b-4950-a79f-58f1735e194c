package com.vsimtone.pddp.server.services

import com.querydsl.core.types.Order
import com.querydsl.core.types.OrderSpecifier
import com.vsimtone.pddp.server.entities.EdgeNodeRoute
import com.vsimtone.pddp.server.entities.QEdgeNodeRoute
import com.vsimtone.pddp.server.repositories.EdgeNodeRouteRepository
import com.vsimtone.pddp.server.utils.DateUtil
import com.vsimtone.pddp.server.utils.RedisUtils
import org.apache.commons.net.util.SubnetUtils
import org.redisson.api.RMap
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service
import java.io.Serializable

@Service
class EdgeNodeRouteService : BaseCrudService<EdgeNodeRoute, EdgeNodeRouteRepository>(QEdgeNodeRoute.a) {

    companion object {
        const val MSG_RELOAD_ROUTE = "edge_node_route_reload";

        data class RouteCache(val route: EdgeNodeRoute, val net: SubnetUtils) : Serializable
        data class MissingRouteItem(val time: Long, val uuid: String?, val user: String?, val hostName: String?) : Serializable
    }

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var edgeNodeClusterService: EdgeNodeClusterService

    var enabledRoutesCache = mutableListOf<RouteCache>()

    lateinit var missingRoutes: RMap<String, MissingRouteItem>;

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var sysAlertService: SysAlertService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var orgService: OrgService

    override fun onReady() {
        missingRoutes = this.redisService.redisson.getMap("edge_node_route:missing_routes", RedisUtils.autoCodec(MissingRouteItem::class.java, String::class.java))
        findAll(QEdgeNodeRoute.a.belongOrg.isNull).forEach {
            it.belongOrg = orgService.getSysOrg(OrgService.SYS_ORG_KEY_ROOT)
            save(it);
            this.logger.info("set org to root ${it}")
        }
        schedule("edge_node_route_force_reload", DateUtil.hour(1)) {
            reloadRoutesCache()
        }
        schedule("edge_node_route_remove_missing_route", DateUtil.minute(1)) {
            val clientConfig = clientDeviceService.getClientConfig()
            missingRoutes.keySet(100).forEach {
                val item = missingRoutes[it] ?: return@forEach
                if (item.time < System.currentTimeMillis() - clientConfig.daemonLoopInterval) {
                    missingRoutes.fastRemove(it)
                }
            }
        }
        sysAlertService.addAlertProvider("edge_node_route_missing") {
            val alerts = mutableListOf<SysAlertService.Companion.SysAlert>()
            missingRoutes.randomKeys(10).forEach { ip ->
                val item = missingRoutes[ip] ?: return@forEach
                alerts.add(SysAlertService.Companion.SysAlert("主服务", "代理节点路由缺失: ${item.hostName} ${ip}"))
            }
            return@addAlertProvider alerts;
        }
        reloadRoutesCache()
    }

    @EventListener(SysService.Companion.AllServerMsgEvent::class)
    fun onReloadMsg(msg: SysService.Companion.AllServerMsgEvent) {
        if (msg.msg == MSG_RELOAD_ROUTE) {
            var _cache = mutableListOf<RouteCache>()
            getEnabledRoutes().forEach {
                try {
                    it.edgeNodeClusters = it.edgeNodeClusters.filter {
                        try {
                            val cluster = edgeNodeClusterService.findById(it)
                            if (cluster.enabled) true
                        } catch (e: Exception) {
                            logger.error("Find edge node ${it} fail", e)
                            false
                        }
                        false
                    }.toMutableList()
                    if (it.edgeNodeClusters.isNotEmpty()) _cache.add(RouteCache(it, SubnetUtils(it.network)));
                } catch (e: Exception) {
                    logger.error("Load route error : " + it.network, e)
                }
            }
            _cache.sortWith { b: RouteCache, a: RouteCache ->
                if (a.route.sortIndex == b.route.sortIndex) {
                    return@sortWith a.route.network.compareTo(b.route.network)
                }
                return@sortWith a.route.sortIndex - b.route.sortIndex
            }
            enabledRoutesCache = _cache
            logger.info("Do reload route cache")
        }
    }

    fun reloadRoutesCache() {
        sysService.pushMsgToAllServers(MSG_RELOAD_ROUTE)
    }

    fun getEnabledRoutes(): List<EdgeNodeRoute> {
        return queryFactory
            .from(QEdgeNodeRoute.a)
            .select(QEdgeNodeRoute.a)
            .where(QEdgeNodeRoute.a.status.eq(EdgeNodeRoute.Status.Enabled))
            .orderBy(OrderSpecifier(Order.DESC, QEdgeNodeRoute.a.sortIndex))
            .fetch()
    }

    fun checkRepeat(routes: List<EdgeNodeRoute>, r1: EdgeNodeRoute): List<EdgeNodeRoute> {
        val repeats = mutableListOf<EdgeNodeRoute>()
        val s1 = SubnetUtils(r1.network)
        routes.forEachIndexed f2@{ j, r2 ->
            if (r2 == r1) return@f2
            val s2 = SubnetUtils(r2.network)
            if (s1.info.isInRange(s2.info.highAddress) || s1.info.isInRange(s2.info.lowAddress)) repeats.add(r2)
            if (s2.info.isInRange(s1.info.highAddress) || s2.info.isInRange(s1.info.lowAddress)) repeats.add(r2)
        }
        return repeats
    }

    fun route(ips: Array<String?>): EdgeNodeRoute? {
        ips.forEach { ip ->
            if (ip.isNullOrEmpty()) return@forEach
            enabledRoutesCache.forEach {
                if (it.net.info.cidrSignature == "$ip/32" || it.net.info.isInRange(ip)) {
                    return it.route;
                }
            }
        }
        return null;
    }

    fun getNetworks(edgeNodeClusterId: Long): List<String> {
        return enabledRoutesCache.filter {
            it.route.edgeNodeClusters.contains(edgeNodeClusterId)
        }.map { it.route.network }
    }
}

package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.entities.APPConfig
import com.vsimtone.pddp.server.entities.QAPPConfig
import com.vsimtone.pddp.server.repositories.APPConfigRepository
import com.vsimtone.pddp.server.utils.JSONUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import kotlin.concurrent.thread

/**
 * Created by zhang<PERSON>n on 16-3-28.
 */
@Service
open class APPConfigService : BaseCrudService<APPConfig, APPConfigRepository>(QAPPConfig.a) {

    companion object {
        const val CACHE_KEY_FindByKey = "app_config:findByKey"
        const val CACHE_KEY_ListByKey = "app_config:listByKey"
    }

    @Value("\${app.config.delaySaveThreadSize}")
    var delaySaveThreadSize = 30

    @Value("\${app.config.clientActionThreadSize}")
    var clientActionThreadSize = 30

    @Value("\${app.config.clientActionTimeoutSec}")
    var clientActionTimeoutSec = 60

    @Value("\${app.config.robotClientAuthKey}")
    var robotClientAuthKey = ""

    @Value("\${app.config.machineLearnWorkDir}")
    var machineLearnWorkDir = "machine-learn-work"

    @Value("\${app.config.keytabConfigDir}")
    var keytabConfigDir = "keytab-config"

    @Value("\${app.config.license_req_include_all_machine_id}")
    var licenseReqIncludeOfflineMachineID = false

    override fun onReady() {
        thread {
            var i = 0;
            while (i++ < 10) {
                if (StringUtils.isEmpty(robotClientAuthKey) || robotClientAuthKey.trim().length <= 64) {
                    robotClientAuthKey = ""
                } else {
                    logger.warn("\n------------------第${i}次警告：机器人客户端认证密钥已设置，请确保当前是开发模式，其他模式请关闭此选项----------------")
                    Thread.sleep(3000);
                }
            }
        }
    }

    fun init(name: String, value: Any, type: APPConfig.Type) {
        var dd = findByKey(name)
        if (dd == null)
            setConfig(name, value.toString(), type)
    }


    fun findByKey(key: String): APPConfig? {
        return getByCache(CACHE_KEY_FindByKey, key, APPConfig::class.java) {
            repo.findOneByKey(key)
        }
    }

    fun listByKey(key: String): List<APPConfig> {
        return (getByCache(CACHE_KEY_ListByKey, key, Array<APPConfig>::class.java) {
            repo.findAllByKeyLike(key).toTypedArray()
        })?.toList() ?: listOf()
    }


    @Transactional
    fun setConfig(key: String, `val`: String?, type: APPConfig.Type?) {
        var ac = repo.findOneByKey(key)
        if (ac == null && type == null)
            throw RuntimeException("Set config ${key} not init.")
        if (ac == null) {
            ac = APPConfig()
            ac.key = key
        }
        ac.`val` = `val`
        if (type != null && ac.id == null)
            ac.type = type
        getVal(ac); // check can read
        repo.save(ac)
        repo.flush()
        removeCache(CACHE_KEY_FindByKey, key)
        removeCache(CACHE_KEY_ListByKey)
    }

    fun remove(vararg key: String) {
        var a = findByKey(StringUtils.join(key, "."))
        if (a != null)
            repo.delete(a)
        removeCache(CACHE_KEY_FindByKey, *key)
        removeCache(CACHE_KEY_ListByKey)
    }

    open inner class ConfigWrapper(private val prefix: String) {

        fun getKey(vararg key: String): String {
            return prefix + "." + StringUtils.join(key, ".")
        }

        fun findByKey(key: String): APPConfig? {
            return <EMAIL>(getKey(key))
        }

        fun getFloat(vararg key: String): Float? {
            return <EMAIL>(getKey(*key))
        }

        @Transactional
        open fun remove(vararg key: String) {
            <EMAIL>(getKey(*key))
        }

        fun getMap(vararg key: String): Map<*, *>? {
            return <EMAIL>(getKey(*key))
        }

        fun search(vararg keys: String): Map<String, Any?> {
            return <EMAIL>(getKey(*keys))
        }

        fun getInteger(vararg key: String): Int? {
            return <EMAIL>(getKey(*key))
        }

        fun getLong(vararg key: String): Long? {
            return <EMAIL>(getKey(*key))
        }

        fun getArray(vararg key: String): List<*>? {
            return <EMAIL>(getKey(*key))
        }

        fun getBoolean(vararg key: String): Boolean? {
            return <EMAIL>(getKey(*key))
        }

//        @Transactional
//        open fun setConfig(key: String, `val`: String, type: APPConfig.Type) {
//            <EMAIL> = (getKey(key), `val`, type)
//        }

        fun listByKey(key: String): List<APPConfig> {
            return <EMAIL>(getKey(key))
        }

        fun getVal(c: APPConfig): Any? {
            return <EMAIL>(c)
        }

        operator fun get(vararg key: String): Any? {
            return this@APPConfigService[getKey(*key)]
        }

//        @Transactional
//        open fun setConfig(cs: HashMap<String, Any>) {
//            <EMAIL> = (cs)
//        }

        fun getString(vararg key: String): String? {
            return <EMAIL>(getKey(*key))
        }

    }

    fun wrapper(prefix: String): ConfigWrapper {
        return ConfigWrapper(prefix)
    }

    fun getVal(c: APPConfig?): Any? {
        if (c == null) return c
        if (c.`val` == null || c.`val`!!.trim { it <= ' ' }.isEmpty()) return null
        if (c.type == APPConfig.Type.Boolean)
            return java.lang.Boolean.parseBoolean(c.`val`)
        if (c.type == APPConfig.Type.String)
            return c.`val`
        if (c.type == APPConfig.Type.Integer)
            return Integer.parseInt(c.`val`)
        if (c.type == APPConfig.Type.Float)
            return java.lang.Float.parseFloat(c.`val`)
        if (c.type == APPConfig.Type.Long)
            return java.lang.Long.parseLong(c.`val`)
        if (c.type == APPConfig.Type.JsonMap)
            return JSONUtils.toObject(c.`val`!!, Map::class.java)
        if (c.type == APPConfig.Type.JsonArray)
            return JSONUtils.toObject(c.`val`!!, List::class.java)
        throw IllegalArgumentException("error type : " + c.type)
    }

    operator fun get(vararg key: String): Any? {
        return getVal(findByKey(StringUtils.join(key, ".")))
    }


    fun search(vararg keys: String): Map<String, Any?> {
        val configs = mutableMapOf<String, Any?>()
        if (keys.isEmpty())
            return configs
        val hasLike = keys.find { it.contains("%") } != null
        if (!hasLike) {
            keys.forEach {
                val data = findByKey(it) ?: return@forEach
                configs[data.key] = getVal(findByKey(it))
            }
            return configs;
        }

        val query = BooleanBuilder()
        keys.forEach {
            query.or(QAPPConfig.a.key.like(it))
        }
        val vals = repo.findAll(query)
        vals.forEach {
            configs[it.key] = getVal(findByKey(it.key))
        }
        return configs
    }

    @Transactional
    fun setConfig(cs: HashMap<String, Any>) {
        for (key in cs.keys) {
            setConfig(key, cs[key])
        }
    }

    @Transactional
    fun setConfig(key: String, `val`: String, type: String) {
        setConfig(key, `val`, APPConfig.Type.valueOf(type))
    }

    fun getBoolean(vararg key: String): Boolean? {
        return get(*key) as Boolean?
    }

    fun getRawString(vararg key: String): String? {
        return findByKey(StringUtils.join(key, "."))?.`val`
    }

    fun getString(vararg key: String): String? {
        return get(*key) as String?
    }

    fun getInteger(vararg key: String): Int? {
        return get(*key) as Int?
    }

    fun getFloat(vararg key: String): Float? {
        return get(*key) as Float?
    }

    fun getLong(vararg key: String): Long? {
        return get(*key) as Long?
    }

    fun getMap(vararg key: String): Map<*, *>? {
        return get(*key) as Map<*, *>?
    }

    @Transactional
    fun setConfig(key: String, `val`: Any?) {
        if (`val` == null)
            setConfig(key, null, null as APPConfig.Type?)
        else if (`val` is String || `val` is StringBuffer)
            setConfig(key, `val`.toString(), APPConfig.Type.String)
        else if (`val` is Collection<*> || `val`.javaClass.isArray)
            setConfig(key, JSONUtils.toString(`val`), APPConfig.Type.JsonArray)
        else if (`val` is Map<*, *>)
            setConfig(key, JSONUtils.toString(`val`), APPConfig.Type.JsonMap)
        else if (`val` is Float)
            setConfig(key, `val`.toString(), APPConfig.Type.Float)
        else if (`val` is Long)
            setConfig(key, `val`.toString(), APPConfig.Type.Long)
        else if (`val` is Boolean)
            setConfig(key, `val`.toString(), APPConfig.Type.Boolean)
        else if (`val` is Int)
            setConfig(key, `val`.toString(), APPConfig.Type.Integer)
        else
            throw IllegalArgumentException("Not support val type : " + `val` + "," + `val`.javaClass)
    }

    fun <T> getBean(c: Class<T>, vararg key: String): T? {
        var a = findByKey(StringUtils.join(key, ".")) ?: return null
        return JSONUtils.toObject(a.`val`!!, c)
    }

    fun getList(vararg key: String): List<*>? {
        return get(*key) as List<*>?
    }


}

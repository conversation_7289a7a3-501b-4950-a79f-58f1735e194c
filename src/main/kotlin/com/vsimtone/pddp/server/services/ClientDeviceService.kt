package com.vsimtone.pddp.server.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.module.SimpleModule
import com.querydsl.core.BooleanBuilder
import com.querydsl.core.Tuple
import com.vsimtone.pddp.server.beans.ClientDeviceClientConfig
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.repositories.ClientDeviceHistoryRepository
import com.vsimtone.pddp.server.repositories.ClientDeviceRepository
import com.vsimtone.pddp.server.utils.*
import com.vsimtone.pddp.server.utils.RedisUtils.Companion.autoCodec
import org.apache.commons.io.FileUtils
import org.apache.commons.lang3.StringUtils
import org.apache.tomcat.util.threads.ThreadPoolExecutor
import org.redisson.api.MapCacheOptions
import org.redisson.api.RBlockingQueue
import org.redisson.api.RMap
import org.springframework.beans.BeanUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.util.ResourceUtils
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import kotlin.reflect.full.declaredMemberProperties

@Service
class ClientDeviceService : BaseCrudService<ClientDevice, ClientDeviceRepository>(QClientDevice.a) {

    @Autowired
    lateinit var redisService: RedisService;

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var edgeNodeClusterService: EdgeNodeClusterService

    lateinit var cacheObjectMapper: ObjectMapper


    lateinit var deviceCacheById: RMap<Long, String>
    lateinit var idCacheByDid: RMap<String, String>


    lateinit var lazySaveHeights: RMap<Long, Int>
    lateinit var lazySaveQueue: RBlockingQueue<Array<Long>>
    lateinit var lazyLocalSaveQueue: LocalCacheQueue<Long>

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var orgService: OrgService

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var clientDeviceHistoryRepository: ClientDeviceHistoryRepository

    var needResetDeviceIdMap = ConcurrentHashMap<String, Long>()

    lateinit var threadPoolExecutor: ThreadPoolExecutor

    lateinit var localLocalConfigCache: ClientDeviceClientConfig

    companion object {
        const val RSA_ALGORITHM = "RSA"
        const val CNF_CLIENT_CONFIG = "core.client_device.client.config"
        const val CNF_EXPIRE_TIME = "core.client_device.expire.time"
        const val CNF_LAST_UPDATE_OFFLINE_TIME = "core.client_device.last_update_offline_time"
        const val CNF_HISTORY_SIZE_MAX = "core.client_device.history_size_max"

        var DEFAULT_PATH_WHITELIST = mutableListOf<String>()
        var DEFAULT_PROCESS_WHITELIST = mutableListOf<String>()

        class ClientDefaultWhitelistItem {
            var desc: String = ""
            var type: String = ""
            var values = mutableListOf<String>()
        }

        class FSListItem {
            var dev: String = ""
            var total: Long = 0
            var used: Long = 0
            var mountTo: String = ""
        }
    }

    fun searchSQL(prefix: String, search: String): Array<Any> {
        return arrayOf(
            "( $prefix.deviceId = ?1 or $prefix.fromIP like ?0 or $prefix.hostname = ?1 or $prefix.localIPMAC = ?1)", "$search%", search
        )
    }

    fun getClientConfig(): ClientDeviceClientConfig {
        return localLocalConfigCache;
    }

    @Scheduled(fixedDelay = 1000 * 10)
    fun reloadClientConfigCache() {
        localLocalConfigCache = JSONUtils.toObject(
            appConfigService.getString(CNF_CLIENT_CONFIG)!!, ClientDeviceClientConfig::class.java
        )
    }

    override fun onInit() {
        deviceCacheById = redisService.redisson.getMap("client_device:cache_by_id", autoCodec(String::class.java), MapCacheOptions.defaults())
        idCacheByDid = redisService.redisson.getMap("client_device:id_cache_by_did", autoCodec(String::class.java), MapCacheOptions.defaults())
        lazySaveHeights = redisService.redisson.getMap("client_device:lazy_save_heights", autoCodec(Int::class.java, Long::class.java), MapCacheOptions.defaults())
        lazySaveQueue = redisService.redisson.getBlockingQueue("client_device:lazy_save_queue", autoCodec(Array<Long>::class.java))
        lazyLocalSaveQueue = LocalCacheQueue("ClientDeviceLazySaveQueue", 1000000, 100, 10 * 1000) { // 100个一组，10秒处理一次
            while (lazySaveQueue.size >= 100000) { // 10W
                logger.warn("lazy save queue full, waiting. save_queue_size=,${lazySaveQueue.size}, local_queue_size=${lazyLocalSaveQueue.cachedSize()}")
                Thread.sleep(1000);
            }
            lazySaveQueue.add(it.toTypedArray());
        }
        YAMLUtils.toObject(FileUtils.readFileToString(ResourceUtils.getFile("classpath:client_default_whitelist.yml"), Charsets.UTF_8), Array<ClientDefaultWhitelistItem>::class.java).forEach { item ->
            if (item.type == "process") {
                DEFAULT_PROCESS_WHITELIST.addAll(item.values.filter { it.trim().isNotEmpty() })
            } else if (item.type == "path") {
                DEFAULT_PATH_WHITELIST.addAll(item.values.filter { it.trim().isNotEmpty() })
            } else {
                throw RuntimeException("Unknown whitelist type ${item.type}")
            }
        }
        this.logger.info("Default process whitelist: ${DEFAULT_PROCESS_WHITELIST.size}")
        this.logger.info("Default path whitelist: ${DEFAULT_PATH_WHITELIST.size}")
    }

    override fun onReady() {
        appConfigService.init(CNF_LAST_UPDATE_OFFLINE_TIME, "0", APPConfig.Type.Long);
        appConfigService.init(CNF_HISTORY_SIZE_MAX, "100", APPConfig.Type.Integer);
        appConfigService.init(CNF_EXPIRE_TIME, DateUtil.day(30), APPConfig.Type.Long)
        appConfigService.init(
            CNF_CLIENT_CONFIG, JSONUtils.toString(ClientDeviceClientConfig()), APPConfig.Type.String
        )
        reloadClientConfigCache();
        cacheObjectMapper = JSONUtils.om.copy()
        val sm = SimpleModule()
        sm.addSerializer(User::class.java, BaseEntity.Companion.OnlySpecKeySerializer(User::id, User::username, User::nickname))
        sm.addSerializer(Organization::class.java, BaseEntity.Companion.OnlySpecKeySerializer(Organization::id, Organization::path))
        sm.addSerializer(EdgeNode::class.java, BaseEntity.Companion.OnlyIDSerializer())
        cacheObjectMapper.registerModule(sm)
        var poolSize = appConfigService.delaySaveThreadSize;
        logger.info("Delay save thread pool size ${poolSize}")
        threadPoolExecutor = ThreadPoolExecutor(
            poolSize / 5, poolSize, 5, TimeUnit.MINUTES, ArrayBlockingQueue(1000000)
        )
    }

    private fun getFromCache(id: Long): ClientDevice? {
        val raw = deviceCacheById[id] ?: return null
        return cacheObjectMapper.readValue(raw, ClientDevice::class.java)
    }

    private fun saveToCache(d: ClientDevice) {
        deviceCacheById.fastPut(d.id, cacheObjectMapper.writeValueAsString(d));
    }

    fun findByIdInCache(id: Long): ClientDevice? {
        var device = this.getFromCache(id)
        if (device != null) return device;
        device = super.findByIdIf(id);
        if (device != null && device.status != ClientDevice.Status.Expired) {
            RestUtil.unwrapBean(ClientDevice::user, device);
            RestUtil.unwrapBean(ClientDevice::org, device);
            putToCache(device);
        }
        return device;
    }

    fun findByDeviceIdInCache(deviceId: String): ClientDevice? {
        var id = idCacheByDid[deviceId]
        if (id != null) {
            var device = this.getFromCache(id.toLong())
            device?.loadFromDB = false;
            if (device != null) return device;
        }
        var dp = repo.findOne(BooleanBuilder().and(QClientDevice.a.deviceId.eq(deviceId)));
        var d: ClientDevice? = null
        if (dp.isPresent) d = dp.get()
        if (d != null && d.status != ClientDevice.Status.Expired) {
            RestUtil.unwrapBean(ClientDevice::user, d);
            RestUtil.unwrapBean(ClientDevice::org, d);
            putToCache(d);
        }
        return d;
    }

    fun saveDelay(device: ClientDevice, canIgnore: Boolean) {
        device.updatedAt = Date();
        putToCache(device)
        if (lazyLocalSaveQueue.contains(device.id!!)) {
            this.logger.warn("Lazy local save queue contains, ignore: ${device}")
            return;
        }
        val height = lazySaveHeights.addAndGet(device.id, 1).toInt();
        var ignoreAdd = true;
        if (sysService.thisNodeInfo.startedAt!!.time + getClientConfig().daemonLoopInterval * 2 > System.currentTimeMillis()) {// 重启一段时间内忽略
            ignoreAdd = false;
        }
        if (ignoreAdd && !canIgnore) {
            ignoreAdd = false;
        }
        if (ignoreAdd && height <= 1) { // 已处理的不忽略
            ignoreAdd = false;
        }
        if (ignoreAdd && lazyLocalSaveQueue.cachedSize() <= lazyLocalSaveQueue.batchProcessSize && height >= 3) { // 本地队列不多，最多忽略3次
            ignoreAdd = false;
        }
        if (ignoreAdd && height >= 10) { // 最多忽略10此
            ignoreAdd = false;
        }
        if (ignoreAdd) {
            this.logger.warn("ignore put to lazy save queue: ${device}, canIgnore=${canIgnore}, height=${height}")
            return;
        }
        lazyLocalSaveQueue.add(device.id!!);
    }

    fun beforeSave(d: ClientDevice) {
        if (d.user == null) {
            d.org = null;
        }
        if (d.org == null) d.org = orgService.getSysOrg(OrgService.SYS_ORG_KEY_UNKNOWN_CLIENT)
    }

    fun putToCache(d: ClientDevice): ClientDevice {
        beforeSave(d)
        this.saveToCache(d);
        idCacheByDid.fastPut(d.deviceId, d.id.toString());
        return d;
    }

    fun removeFromCache(d: ClientDevice): ClientDevice {
        deviceCacheById.fastRemove(d.id);
        idCacheByDid.fastRemove(d.deviceId);
        lazySaveHeights.fastRemove(d.id);
        return d;
    }

    override fun save(device: ClientDevice) {
        super.save(device);
        if (device.status == ClientDevice.Status.Expired) {
            removeFromCache(device)
        } else putToCache(device);
    }

    @Scheduled(fixedDelay = 5 * 1000L)
    fun doDelaySave() {

        if (!sysService.thisNodeInfo.runtimeConfig.enableDeviceDelaySave) return;

        if (!ready) return
        if (userService.isSyncing()) return
        val delaySaveInfoBucket = this.redisService.redisson.getBucket<String>("delay_save_info", autoCodec(String::class.java))
        fun log(err: Boolean, msg: String, e: Throwable? = null) {
            if (err) {
                if (e != null) this.logger.error("DelaySave: $msg", e);
                else this.logger.error("DelaySave: $msg");
            } else this.logger.info("DelaySave: $msg");
            delaySaveInfoBucket.set(
                JSONUtils.toString(
                    mapOf(
                        "time" to BaseEntity.DEFAULT_DATE_FORMAT.format(Date()), "msg" to msg, "err" to err, "hostName" to this.sysService.thisNodeInfo.hostName, "nodeId" to this.sysService.thisNodeInfo.nodeId
                    )
                )
            )
        }
        redisService.runInLock("client_device:do_delay_save", 0) {
            log(false, "started");
            try {
                var offlineUpdateFullDone = false;
                while (!shutdowning) {
                    if (!sysService.thisNodeInfo.runtimeConfig.enableDeviceDelaySave) return@runInLock;
                    if (userService.isSyncing()) {
                        return@runInLock;
                    }
                    val running = AtomicInteger()
                    val dbSaveDoneCount = AtomicInteger()
                    if (threadPoolExecutor.activeCount != 0) throw RuntimeException("Running thread not zero.")
                    fun submitToSave(idList: List<Long>) {
                        running.addAndGet(1);
                        threadPoolExecutor.submit {
                            val devices = mutableListOf<ClientDevice>()
                            val needSaveDevices = mutableListOf<ClientDevice>()
                            val needSaveHistories = mutableListOf<ClientDeviceHistory>()
                            val beforeLazySaveHeights = mutableMapOf<Long, Int>();
                            var reputToQueueSize = 0L;
                            val times = mutableListOf<String>();
                            val beginTime = System.currentTimeMillis();
                            var prevTime = beginTime;
                            fun putTime(msg: String) {
                                val now = System.currentTimeMillis()
                                times.add(msg + "=" + DateUtil.useTimeToHum(now - prevTime))
                                prevTime = now;
                            }
                            try {
                                devices.addAll(repo.findAll(QClientDevice.a.id.`in`(idList)))
                                putTime("query_db");
                                devices.forEach {
                                    val device = it;
                                    val cacheDevice = getFromCache(device.id!!) ?: return@forEach
                                    val lazySaveHeight = (lazySaveHeights[device.id!!] ?: 0).toInt();
                                    autoAddHistory(needSaveHistories, device, cacheDevice);
                                    if (cacheDevice.user != null) cacheDevice.user = userService.repo.getReferenceById(cacheDevice.user!!.id!!)
                                    if (cacheDevice.org != null) cacheDevice.org = orgService.repo.getReferenceById(cacheDevice.org!!.id!!)
                                    cacheDevice.dbVersion = device.dbVersion;
                                    BeanUtils.copyProperties(cacheDevice, device);
                                    needSaveDevices.add(device);
                                    beforeLazySaveHeights[device.id!!] = lazySaveHeight;
                                }
                                putTime("copy_data");
                                runInTran {
                                    needSaveDevices.forEach {
                                        val device = it;
                                        this.save(device);
                                    }
                                    needSaveHistories.forEach {
                                        clientDeviceHistoryRepository.save(it);
                                    }
                                }
                                putTime("save_db");
                                needSaveDevices.forEach {
                                    val device = it;
                                    if (device.status == ClientDevice.Status.Expired) return@forEach;
                                    val beforeHeight = beforeLazySaveHeights[device.id!!]!!;
                                    val nowHeight = (lazySaveHeights[device.id] ?: 0L).toInt();
                                    if (nowHeight != beforeHeight) {
                                        lazyLocalSaveQueue.add(device.id!!);
                                        reputToQueueSize++;
                                    } else {
                                        lazySaveHeights[device.id] = 0;
                                    }
                                }
                                putTime("set_time");
                                idList.filter { it1 -> devices.find { it2 -> it2.id == it1 } == null }.forEach {
                                    deviceCacheById.remove(it);
                                }
                            } catch (e: Exception) {
                                var removeCount = 0;
                                idList.forEach {
                                    val cacheDevice = getFromCache(it) ?: return@forEach
                                    removeFromCache(cacheDevice)
                                    removeCount++;
                                }
                                log(true, "submit error, clean ${removeCount} cache device.", e);
                            } finally {
                                val totalUseTime = System.currentTimeMillis() - beginTime
                                if (totalUseTime >= 2000) {
                                    log(false, "submit slow, " + idList.size + " devices [" + idList.first() + " - " + idList.last() + "], times(" + times.joinToString(",") + ", total=" + DateUtil.useTimeToHum(totalUseTime) + ")")
                                }
                                if (reputToQueueSize > 0) {
                                    log(false, "reput to queue size: ${reputToQueueSize}");
                                }
                                dbSaveDoneCount.addAndGet(idList.size)
                                running.addAndGet(-1);
                            }
                        }
                    }

                    val startAt = System.currentTimeMillis();
                    var totalUseTime = 0L;
                    var latestTime = startAt;
                    val timePoints = mutableListOf<String>();
                    val putTimePoint = fun(msg: String) {
                        val now = System.currentTimeMillis();
                        val useTime = now - latestTime;
                        timePoints.add(msg + ": " + DateUtil.useTimeToHum(useTime));
                        latestTime = now
                        totalUseTime = now - startAt
                    }

                    val maxPollWait = lazyLocalSaveQueue.batchProcessInterval;
                    val dbBatchCount = lazyLocalSaveQueue.batchProcessSize;
                    val maxPollCount = lazySaveQueue.size * lazyLocalSaveQueue.batchProcessSize;

                    var polledIdCount = 0;
                    val polledIdList = mutableListOf<Long>();
                    val polledIdMap = mutableMapOf<Long, Boolean>();
                    while (true) {
                        val ids = lazySaveQueue.poll() ?: arrayOf()
                        ids.forEach {
                            if (polledIdMap[it] == null) {
                                polledIdMap[it] = true;
                                polledIdList.add(it);
                            }
                        }
                        polledIdCount += ids.size
                        val timeout = maxPollWait - (System.currentTimeMillis() - startAt)
                        if (timeout <= 0 || polledIdList.size >= maxPollCount) break
                        if (ids.size < 100 && !offlineUpdateFullDone) break;
                        if (ids.isEmpty()) Thread.sleep(1000);
                    }
                    putTimePoint("PollQueue");
                    if (polledIdList.size != 0) {
                        polledIdList.sort()
                        polledIdList.chunked(dbBatchCount).forEach {
                            submitToSave(it);
                        }
                        var waitTime = 0;
                        while (running.get() > 0) {
                            Thread.sleep(100);
                            waitTime += 100
                            if (waitTime >= 10000 && waitTime % 10000 == 0) {
                                val c = dbSaveDoneCount.get()
                                log(true, "very slow ${c}/${polledIdList.size},${(c * 10000 / polledIdList.size) / 100.0}%")
                            }
                        }
                        putTimePoint("SaveToDB")
                    }
                    val lastUpdateOfflineTime = appConfigService.getLong(CNF_LAST_UPDATE_OFFLINE_TIME) ?: 0
                    if (!offlineUpdateFullDone || System.currentTimeMillis() - lastUpdateOfflineTime >= getClientConfig().daemonLoopInterval / 2) {
                        val updateTime = Date().time
                        val fullQuery = polledIdList.size < maxPollCount / 2;
                        offlineUpdateFullDone = offlineUpdate(lastUpdateOfflineTime, fullQuery);
                        if (offlineUpdateFullDone) appConfigService.setConfig(CNF_LAST_UPDATE_OFFLINE_TIME, updateTime.toString(), APPConfig.Type.Long);
                        if (lastUpdateOfflineTime == 0L || fullQuery) putTimePoint("FullOfflineUpdate");
                        else putTimePoint("QuickOfflineUpdate");
                    }
                    val needSaveCount = lazySaveQueue.size
                    if (polledIdList.size > 0 || System.currentTimeMillis() - startAt >= 1000 * 30 || needSaveCount > 0) log(
                        false, "done ${polledIdList.size}/${polledIdCount}, free ${needSaveCount}, total use " + DateUtil.useTimeToHum(totalUseTime) + ", time points: [" + timePoints.joinToString(", ") + "]"
                    )
                }
            } finally {
                log(false, "stopped")
            }
        }
    }

    fun autoAddHistory(histories: MutableList<ClientDeviceHistory>, oldDevice: ClientDevice, newDevice: ClientDevice) {
        fun getEdgeNodeName(d: ClientDevice): String {
            return if (d.edgeNodeClusterId != null) edgeNodeClusterService.findById(d.edgeNodeClusterId!!).name else ""
        }

        fun getStatusName(d: ClientDevice): String {
            if (d.status == ClientDevice.Status.Online) return "在线"
            if (d.status == ClientDevice.Status.Offline) return "离线"
            if (d.status == ClientDevice.Status.NoUserSession) return "无会话"
            if (d.status == ClientDevice.Status.Expired) return "已失效"
            return ""
        }

        fun getLoginTypeName(d: ClientDevice): String {
            if (d.loginType == ClientDevice.LoginType.Auto) return "自动"
            if (d.loginType == ClientDevice.LoginType.Manual) return "手动"
            return ""
        }

        fun getOrgName(d: ClientDevice): String {
            if (d.org == null) return ""
            var org = d.org!!
            if (org.name.isEmpty() && org.path.isNotEmpty()) org = orgService.findById(org.id!!)
            return orgService.getNames(org).joinToString("|")
        }

        fun boolName(b: Boolean): String {
            if (b) return "是"
            return "否"
        }

        fun fieldValueToString(v: Any): String {
            if (v is Boolean) return boolName(v);
            return v.toString();
        }

        fun getPendingActions(d: ClientDevice): String {
            return d.pendingActions.map { actionToMsgInfo(it, true) }.joinToString(",")
        }

        val detectors = mutableListOf<Array<String>>(
            arrayOf(ClientDevice::fromIP.name, "访问IP"),
            arrayOf(ClientDevice::hostname.name, "主机名"),
            arrayOf(ClientDevice::localIPMAC.name, "MAC地址"),
            arrayOf("edgeNode.name", "代理节点", getEdgeNodeName(oldDevice), getEdgeNodeName(newDevice)),
            arrayOf(ClientDevice::status.name, "状态", getStatusName(oldDevice), getStatusName(newDevice)),
            arrayOf(ClientDevice::version.name, "终端版本"),
            arrayOf(ClientDevice::username.name, "本地用户"),
            arrayOf(ClientDevice::loginType.name, "绑定类型", getLoginTypeName(oldDevice), getLoginTypeName(newDevice)),
            arrayOf(ClientDevice::systemVersion.name, "系统版本"),
            arrayOf(ClientDevice::systemKernel.name, "内核版本"),
            arrayOf(ClientDevice::systemMemTotal.name, "内存总数"),
            arrayOf(ClientDevice::pendingActions.name, "待下发操作", getPendingActions(oldDevice), getPendingActions(newDevice))
        )
        if (oldDevice.user != newDevice.user) {
            detectors.add(arrayOf("user.username", "绑定用户", oldDevice.user?.username ?: "", newDevice.user?.username ?: ""));
        }
        if (oldDevice.org != newDevice.org) {
            detectors.add(arrayOf("org.fullname", "绑定机构", getOrgName(oldDevice), getOrgName(newDevice)));
        }
        if (detectors.size > 0) {
            val a = QClientDeviceHistory.a;
            var minIdx = 0L;
            var maxIdx = 0L;
            val r = (queryFactory.from(a).select(a.dataIdx.min(), a.dataIdx.max()).where(a.clientDevice.eq(newDevice)).createQuery().singleResult as Tuple?)
            if (r != null) {
                minIdx = r.get(0, Long::class.java) ?: minIdx;
                maxIdx = r.get(1, Long::class.java) ?: maxIdx;
            }
            var nxtIdx = maxIdx + 1;
            val maxHistorySize = appConfigService.getInteger(CNF_HISTORY_SIZE_MAX)!!;
            detectors.forEach { d ->
                val fieldKey = d[0];
                val fieldName = d[1];
                var oldValue = "";
                var newValue = "";
                if (d.size == 2) {
                    val field = ClientDevice::class.declaredMemberProperties.find { it.name == fieldKey }!!;
                    val rawOldValue = field.get(oldDevice) ?: "";
                    val rawNewValue = field.get(newDevice) ?: "";
                    oldValue = fieldValueToString(rawOldValue)
                    newValue = fieldValueToString(rawNewValue)
                } else {
                    oldValue = d[2]
                    newValue = d[3]
                }
                if (oldValue != newValue) {
                    val h = ClientDeviceHistory();
                    h.clientDevice = newDevice;
                    h.fieldKey = fieldKey
                    h.fieldName = fieldName
                    h.oldValue = oldValue
                    h.newValue = newValue
                    h.dataIdx = nxtIdx++;
                    histories.add(h);
                }
            }
            val overflowSize = (nxtIdx - minIdx) - maxHistorySize
            if (overflowSize > 0) {
                clientDeviceHistoryRepository.findAll(a.clientDevice.eq(newDevice).and(a.dataIdx.goe(minIdx)).and(a.dataIdx.lt(minIdx + overflowSize))).forEach {
                    clientDeviceHistoryRepository.delete(it);
                }
            }
        }
    }

    private fun offlineUpdate(lastUpdatedAt: Long, fullQuery: Boolean): Boolean {
        if (!ready) return false
        if (userService.isSyncing()) return false;
        val maxOfflineCount = 1000L;

        val clientConfig = getClientConfig()
        val expireTime = DateUtil.nowDate(0 - clientConfig.daemonLoopInterval * 2);

        val queries = BooleanBuilder();
        if (!fullQuery && lastUpdatedAt > 0) queries.and(QClientDevice.a.lastOnlineTime.gt(Date(lastUpdatedAt - clientConfig.daemonLoopInterval * 2)))
        queries.and(QClientDevice.a.lastOnlineTime.lt(expireTime));
        queries.and(QClientDevice.a.status.`in`(listOf(ClientDevice.Status.Online, ClientDevice.Status.NoUserSession)));
        val ids = runInReadOnlyAndNoTran {
            queryFactory.select(QClientDevice.a.id).from(QClientDevice.a).where(queries).offset(0).limit(maxOfflineCount).fetch()
        }
        var okCount = 0;
        ids.forEach {
            val d = findByIdInCache(it) ?: return@forEach
            val lastOnlineTime = d.lastOnlineTime?.time ?: 0
            if (lastOnlineTime > expireTime.time) return@forEach
            if (!fullQuery && d.status != ClientDevice.Status.Online && d.status != ClientDevice.Status.NoUserSession) return@forEach
            d.status = ClientDevice.Status.Offline
            saveDelay(d, true);
            okCount++;
        }
        val done = ids.size < maxOfflineCount
        if (ids.size > 0) logger.info("Offline update [$queries] ${okCount}/${ids.size} $done")
        return done;
    }

    fun updateHealthScore(device: ClientDevice, info: MutableMap<String, String>?) {
        if (info.isNullOrEmpty()) {
            device.healthScore = -1
        } else {
            var subScore = 0;
            var totalScore = 0;
            var totalMemUse = 0L;
            info.forEach { (key, value) ->
                val keys = key.split(":")
                val values = value.split(":").toMutableList()
                if (keys.size != 2) return@forEach
                if (values.size == 0) values.add("")
                totalScore++
                if (keys[0] == "service_state" && values.size == 3) {
                    if (values[0] != "active") {
                        subScore++;
                    }
                }
                if (keys[0] == "process_pid") {
                    if (values[0].isEmpty() || values[0] == "0") {
                        subScore++;
                    } else {
                        if (values.size >= 2) {
                            totalMemUse += values[1].toLong();
                        }
                    }
                }
                if (keys[0] == "kernel_module") {
                    if (values[0] != "active") {
                        subScore++;
                    }
                }
            }
            device.healthScore = 100 - (subScore * 100.0 / totalScore).toInt();
            device.totalMemUse = totalMemUse;
        }
    }

    fun actionToMsgInfo(action: String, add: Boolean): String {
        if (action.startsWith(ClientDevice.Action.UploadLog.name)) return (if (add) "上传日志" else "取消上传日志");
        if (action.startsWith(ClientDevice.Action.Uninstall.name)) return (if (add) "卸载设备" else "取消卸载设备");
        if (action.startsWith(ClientDevice.Action.ResetID.name)) return (if (add) "重置ID" else "取消重置ID");
        if (action.startsWith(ClientDevice.Action.ResetLogin.name)) return (if (add) "重置登录" else "取消重置登录");
        if (action.startsWith(ClientDevice.Action.ResetRoute.name)) return (if (add) "重新匹配二级节点" else "取消重新匹配二级节点");
        return "";
    }

    fun getSystemType(_type: String): ClientDevice.SysType? {
        val type = _type.lowercase()
        if (type == "uos") return ClientDevice.SysType.UOS
        if (type == "kylin") return ClientDevice.SysType.Kylin
        return ClientDevice.SysType.Unknown
    }

    fun getSystemArch(_arch: String): ClientDevice.SysArch? {
        val arch = _arch.lowercase()
        if (arch == "x86_64" || arch == "x64" || arch == "amd64" || arch == "i686_64") return ClientDevice.SysArch.X86_64
        if (arch == "aarch64" || arch == "arm64") return ClientDevice.SysArch.ARM64
        if (arch == "x86" || arch == "i386" || arch == "i686") return ClientDevice.SysArch.X86
        if (arch == "aarch" || arch == "aarch32" || arch == "arm" || arch == "arm32" || arch == "armv7") return ClientDevice.SysArch.ARM32
        return ClientDevice.SysArch.Unknown;
    }

    fun updateSystemVersion(clientDevice: ClientDevice, verStr: String?) {
        if (verStr == null || StringUtils.isEmpty(verStr)) return;
        val info = verStr.split(":")
        if (info.size < 3) {
            logger.error("Unsupported ver str : ${clientDevice} ${verStr}")
            return;
        }
        clientDevice.systemType = getSystemType(info[0].lowercase(Locale.getDefault()));
        clientDevice.systemArch = getSystemArch(info[1].lowercase(Locale.getDefault()));
        clientDevice.systemVersion = info[2];
        if (info.size == 4) clientDevice.systemKernel = info[3];
    }

    fun updateUptime(clientDevice: ClientDevice, _uptime: String?) {
        if (_uptime == null || StringUtils.isEmpty(_uptime)) return;
        var uptime = _uptime.replace(Regex("[\\s\\t\\r]+"), "")
        val formats = mutableListOf(
            //  14:59:18 up 35 days,  1:58,  1 user,  load average: 0.83, 1.26, 1.38
            "(\\d{2}:\\d{2}:\\d{2})up(\\d+)days?,(\\d+:\\d+|\\d+min),(\\d+)users?,loadaverages?:(.*?),(.*?),(.*?)$",
            //  14:59:18,  1:58,  1 user,  load average: 0.83, 1.26, 1.38
            "(\\d{2}:\\d{2}:\\d{2})(?:,|up)(\\d+:\\d+||\\d+min),(\\d+)users?,loadaverages?:(.*?),(.*?),(.*?)$"
        )

        var _result: MatchResult? = null
        var format = -1;
        formats.forEachIndexed { idx, it ->
            if (_result == null) {
                format = idx
                _result = Regex(it).findAll(uptime).firstOrNull()
            }
        }
        if (_result == null) {
            logger.error("Unsupported uptime result : ${clientDevice} ${uptime}")
            return;
        }
        try {
            var result = _result!!;
            var groupIdx = 1;

            val nowTime = result.groupValues[groupIdx++]
            var upDays = 0;

            if (format == 0) {
                upDays = result.groupValues[groupIdx++].toInt()
            }
            var upHMRaw = result.groupValues[groupIdx++]
            if (upHMRaw.endsWith("min")) upHMRaw = upHMRaw.substring(0, upHMRaw.length - 3) + ":00"

            val upHM = upHMRaw.split(":")

            val loginUsers = result.groupValues[groupIdx++].toInt()
            val sysLoad1m = result.groupValues[groupIdx++].toFloat()
            val sysLoad5m = result.groupValues[groupIdx++].toFloat()
            val sysLoad15m = result.groupValues[groupIdx++].toFloat()
            val fullDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            val clientNowDate = fullDateFormat.parse(SimpleDateFormat("yyyy-MM-dd").format(Date()) + " " + nowTime)
            val clientNowSec = Math.floor(clientNowDate.time / 1000.0).toLong()
            val startTimeSec = clientNowSec - clientNowSec % 60 - (upDays * 3600L * 24 + upHM[0].toInt() * 3600L + upHM[1].toInt() * 60)
            clientDevice.systemStartTime = Date(startTimeSec * 1000)
            if (clientDevice.systemCpuCount != null && clientDevice.systemCpuCount!! > 0) {
                clientDevice.systemCpuUsage1M = sysLoad1m / clientDevice.systemCpuCount!! * 100
                clientDevice.systemCpuUsage5M = sysLoad5m / clientDevice.systemCpuCount!! * 100
                clientDevice.systemCpuUsage15M = sysLoad15m / clientDevice.systemCpuCount!! * 100
            }
            clientDevice.systemLoginUsers = loginUsers;

        } catch (e: Exception) {
            logger.error("Uptime decode fail: ${clientDevice} '${uptime}'", e)
        }
    }

    fun parseFSList(fsList: String): List<FSListItem> {
        return fsList.split("\n").map {
            try {
                val arr = it.split("|")
                if (arr.size != 4) return@map null
                val item = FSListItem()
                item.dev = arr[0]
                item.total = arr[1].toLong()
                item.used = arr[2].toLong()
                item.mountTo = arr[3]
                item
            } catch (e: Exception) {
                logger.error("Parse fs list item error: ${it}")
                null
            }
        }.filterNotNull()
    }

}
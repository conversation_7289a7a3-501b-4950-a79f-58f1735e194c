package com.vsimtone.pddp.server.services

import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

@Service
class LuaService : BaseService() {
    private var luaBinary = "";

    @Autowired
    lateinit var externalToolsService: ExternalToolsService
    override fun onReady() {
        luaBinary = externalToolsService.getTool("lua", "lua54", "lua54.exe")
        val luaVersion = getLuaVersion()
        if (luaVersion.isEmpty())
            throw RuntimeException("Lua version check fail: ${luaBinary}")
        logger.info("Use lua binary ${externalToolsService.printPath(luaBinary)}, version ${luaVersion}")
    }

    fun getLuaVersion(): String {
        val pb = ProcessBuilder().command(luaBinary, "-v")
        val p = pb.start()
        val exited = p.waitFor(10, TimeUnit.SECONDS)
        if (!exited) p.destroy()
        if (p.exitValue() == 0) {
            return IOUtils.toString(p.inputStream, Charsets.UTF_8).replace(Regex("\n"), "")
        }
        return ""
    }

    fun runLuaScript(script: String): String {
        val runOpts = ExternalToolsService.Companion.RunOpts()
        runOpts.stdin = script
        return externalToolsService.runTool(luaBinary, arrayOf("-"), runOpts)
    }
}
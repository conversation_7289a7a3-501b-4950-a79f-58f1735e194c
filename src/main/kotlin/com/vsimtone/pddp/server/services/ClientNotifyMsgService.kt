package com.vsimtone.pddp.server.services

import com.vsimtone.pddp.server.entities.ClientDevice
import com.vsimtone.pddp.server.entities.ClientNotifyMsg
import com.vsimtone.pddp.server.entities.QClientNotifyMsg
import com.vsimtone.pddp.server.repositories.ClientNotifyMsgRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
class ClientNotifyMsgService : BaseCrudService<ClientNotifyMsg, ClientNotifyMsgRepository>(QClientNotifyMsg.a) {

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Transactional(propagation = Propagation.MANDATORY)
    fun newMsg(_device: ClientDevice, type: ClientNotifyMsg.Type, target: String, title: String, info: String) {
        var device = _device;
        if (!device.loadFromDB) device = clientDeviceService.findById(_device.id!!);
        val msg = ClientNotifyMsg()
        msg.type = type;
        msg.title = title;
        msg.msg = info;
        msg.notified = false
        msg.device = device;
        msg.target = target;
        val a = QClientNotifyMsg.a
        msg.msgIdx = device.maxNotifyMsgIdx + 1;
        save(msg);
        device.maxNotifyMsgIdx = msg.msgIdx
        clientDeviceService.save(device);
    }
}

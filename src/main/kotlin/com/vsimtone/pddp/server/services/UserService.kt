package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.BooleanExpression
import com.querydsl.core.types.dsl.StringPath
import com.vsimtone.pddp.server.beans.CurrAuthedUser
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.configs.AdminSecurityConfig
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.repositories.UserRepository
import com.vsimtone.pddp.server.utils.AppUtils
import com.vsimtone.pddp.server.utils.CryptUtils
import com.vsimtone.pddp.server.utils.RedisUtils
import org.apache.commons.codec.digest.DigestUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.context.event.EventListener
import org.springframework.core.io.FileSystemResource
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.support.CronExpression
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent
import org.springframework.security.authentication.event.AuthenticationSuccessEvent
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.session.SessionRegistry
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.kerberos.authentication.sun.SunJaasKerberosTicketValidator
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.StopWatch
import java.io.File
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*
import javax.security.auth.kerberos.KerberosPrincipal
import javax.security.auth.kerberos.KeyTab


@Service
class UserService : BaseCrudService<User, UserRepository>(QUser.a) {

    @Autowired
    lateinit var passwordEncoder: PasswordEncoder

    @Autowired
    lateinit var sessionRegistry: SessionRegistry

    @Autowired
    lateinit var ldapService: LDAPService

    @Autowired
    lateinit var orgService: OrgService

    @Autowired
    lateinit var ldapConfigService: LdapConfigService

    @Autowired
    lateinit var ldapSyncRecordService: LdapSyncRecordService

    @Autowired
    @Lazy
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var sysRoleService: SysRoleService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var optLogService: OptLogService

    @Autowired
    lateinit var bocP6TableService: BOCP6TableService

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var sysAlertService: SysAlertService

    private var _isSyncing = false;

    private var krbValidators = mutableMapOf<String, SunJaasKerberosTicketValidator>();

    data class OutOrgItem(
        var id: String = "",
        var name: String = "",
        var parent: String? = null,
        var hierarchy: Int = 0
    )

    data class OutUserItem(
        var org: String? = null,
        var uid: String = "",
        var nickname: String = "",
        var email: String = ""
    )

    init {
        readyInvokeOrder = READY_INVOKE_HIGH;
    }

    @Transactional
    @EventListener
    fun authenticationFailed(event: AuthenticationFailureBadCredentialsEvent) {
        val ud = event.authentication as AdminSecurityConfig.AdminLoginAuthToken
        logger.info("Authentication failed ${ud}")
        val username = ud.username
        val user = findByUsername(username) ?: return;
        if (user.manageRoles.find { it.code == SysRole.Code.SuperManager } == null)
            user.loginFailCount++;
        if (user.loginFailCount >= 10) user.locked = true;
        save(user);
        optLogService.addOptLog(username, "登录失败", "系统", user.nickname, user);
    }

    @Transactional
    @EventListener
    fun authenticationSuccess(event: AuthenticationSuccessEvent) {
        val ud = event.authentication as AdminSecurityConfig.AdminLoginAuthToken
        logger.info("Authentication success ${ud}")
        val user = findById((ud.details as CurrAuthedUser).id)
        if (user.loginFailCount != 0) {
            user.loginFailCount = 0;
            save(user);
        }
        optLogService.addOptLog(user.username, "登录成功", "系统", user.nickname, user);
    }

    fun onChange(id: Long, _data: User?) {
        var data = findById(id)
        expireUserSession(data)
    }

    @Scheduled(fixedRate = 10 * 1000L)
    fun updateIsSyncing() {
        _isSyncing = redisService.redisson.getBucket<String?>("lock:user_sync", RedisUtils.autoCodec(String::class.java)).isExists;
    }

    fun isSyncing(): Boolean {
        return _isSyncing;
    }

    fun expireUserSession(user: User) {
        var u = org.springframework.security.core.userdetails.User(user.username, "", listOf())
        sessionRegistry.getAllSessions(u, false).forEach {
            it.expireNow()
        }
    }

    fun currUser(): CurrAuthedUser? {
        val auth = SecurityContextHolder.getContext().authentication ?: return null;
        if (auth is AdminSecurityConfig.AdminLoginAuthToken) {
            return auth.details!! as CurrAuthedUser
        }
        return null;
    }

    fun mgrOrgQuery(mgrOrgs: List<Organization>, path: StringPath): Predicate? {
        val q = mutableListOf<BooleanExpression>()
        val paths = mutableListOf<String>()
        mgrOrgs.sortedBy { it.path.length }.forEach {
            val p = it.path
            if (paths.find { p.startsWith(it) } == null) paths.add(p)
        }
        paths.forEach {
            if (orgService.getSysOrg(OrgService.SYS_ORG_KEY_ROOT).path == it) {
                q.clear();
                return@forEach
            }
            q.add(path.like("$it%"))
        }
        if (q.isEmpty()) return null;
        return BooleanBuilder().andAnyOf(*q.toTypedArray())
    }

    fun findByUsername(username: String?): User? {
        if (username == null) return null
        val queries = BooleanBuilder();
        queries.and(QUser.a.username.eq(username));
        return getOrNull(repo.findOne(queries));
    }

    fun findByUsernameAndDomain(_username: String, domain: String?): User? {
        var username = _username;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(domain)) username = "${username}@${domain}"
        return findByUsername(username)
    }

    fun findManager(orgId: Long, roles: List<SysRole>, limit: Long = 10): List<User> {
        val a = QUser.a
        val queries = BooleanBuilder()
        queries.and(a.enabled.eq(true))
        queries.and(a.manager.eq(true))
        var parentIds = mutableListOf<Long>()
        var _org: Organization? = orgService.findById(orgId)
        while (_org != null) {
            parentIds.add(_org.id!!)
            _org = _org.parent
        }
        queries.and(a.manageOrgs.any().id.`in`(parentIds))
        queries.and(a.manageRoles.any().id.`in`(roles.map { it.id }))
        return findAll(queries, PageAttr(PageAttr.FIRST_NUM, limit, null, false)).content
    }

    fun findManager(orgId: Long, code: SysRole.Code, limit: Long = 10): List<User> {
        val a = QUser.a
        val queries = BooleanBuilder()
        queries.and(a.enabled.eq(true))
        queries.and(a.manager.eq(true))
        var parentIds = mutableListOf<Long>()
        var _org: Organization? = orgService.findById(orgId)
        while (_org != null) {
            parentIds.add(_org.id!!)
            _org = _org.parent
        }
        queries.and(a.manageOrgs.any().id.`in`(parentIds))
        queries.and(a.manageRoles.any().code.eq(code))
        return findAll(queries, PageAttr(PageAttr.FIRST_NUM, limit, null, false)).content
    }

    fun getKrbKeyInfo(principal: String, keytabFile: File): String? {
        val keyPrincipal = KerberosPrincipal(principal);
        val keytab = KeyTab.getInstance(keyPrincipal, keytabFile);
        if (!keytab.exists()) throw RuntimeException("${principal} ${keytabFile.absolutePath} 解析失败");
        val keys = keytab.getKeys(keyPrincipal);
        if (keys.isEmpty()) throw RuntimeException("${principal} ${keytabFile.absolutePath} 无可用私钥");
        var infos = "${keys.size}";
        keys.forEach {
            infos += "|${it.keyType}-${it.versionNumber}-${it.algorithm}-${it.format}"
        }
        return infos;
    }

    fun getKrbValidator(authWithKrbPrincipal: String, authWithKrbKeytabHash: String, authWithKrbKeytab: String): SunJaasKerberosTicketValidator? {
        var validator = krbValidators[authWithKrbKeytabHash];
        if (validator == null) {
            synchronized(krbValidators) {
                validator = SunJaasKerberosTicketValidator();
                val keytabFile = File(appConfigService.keytabConfigDir + File.separator + authWithKrbKeytabHash + ".keytab");
                if (!keytabFile.exists() || keytabFile.length() == 0L) {
                    val decodeKey = CryptUtils.base64decode(authWithKrbKeytab);
                    if (CryptUtils.sha256hex(decodeKey) != authWithKrbKeytabHash) throw RuntimeException("${authWithKrbPrincipal} ${authWithKrbKeytabHash} hash check failed");
                    keytabFile.writeBytes(decodeKey);
                    logger.info("Write keytab file ${keytabFile.absolutePath}");
                }
                try {
                    val keyInfo = getKrbKeyInfo(authWithKrbPrincipal, keytabFile);
                    if (logger.isDebugEnabled) validator!!.setDebug(true);
                    validator!!.setKeyTabLocation(FileSystemResource(keytabFile));
                    validator!!.setServicePrincipal(authWithKrbPrincipal);
                    validator!!.setHoldOnToGSSContext(true);
                    validator!!.afterPropertiesSet();
                    krbValidators[authWithKrbKeytabHash] = validator!!;
                    this.logger.info("Load keytab principal=${authWithKrbPrincipal}, hash=${authWithKrbKeytabHash}, file=${keytabFile.absolutePath}, validator=${validator?.hashCode()}, keys=${keyInfo}");
                } catch (e: Exception) {
                    logger.error("Load keytab ${authWithKrbPrincipal} ${authWithKrbKeytabHash} ${keytabFile.absolutePath} error", e);
                }
            }
        }
        return validator;
    }

    fun checkPassword(user: User, pass: String): String? {
        if (user.type == User.Type.Local) {
            if (passwordEncoder.matches(pass, user.password!!)) return null;
            return "密码验证失败"
        }
        val uid = user.username.split("@")[0]
        val domain = user.username.split("@")[1]
        val cnf = ldapConfigService.findByDomain(domain).filter { it.syncTo != null }.firstOrNull { user.org.path.startsWith(it.syncTo!!.path) } ?: return "域配置未找到"
        var userkey = user.username

        if (pass.startsWith("kylin_gss_tgt:") || pass.startsWith("uos_gss_tgt:")) { // kerberos ticket
            if (cnf.authWithKrbKeytab == null || cnf.authWithKrbPrincipal == null || cnf.authWithKrbKeytabHash == null)
                return "域配置KRB未配置";
            val info = pass.split(":");
            if (info.size != 2) return "KRB密码格式错误";
            val tgt = info[1];
            var decodeTgt: ByteArray? = null;
            var validator: SunJaasKerberosTicketValidator? = null;
            try {
                validator = getKrbValidator(cnf.authWithKrbPrincipal!!, cnf.authWithKrbKeytabHash!!, cnf.authWithKrbKeytab!!) ?: throw RuntimeException("加载Krb验证器失败");
                decodeTgt = CryptUtils.base64decode(tgt);
                val validateResult = validator.validateTicket(decodeTgt);
                val validateUser = validateResult.username().split("@");
                if (validateUser.size != 2) throw RuntimeException("Validate username error ${validateUser.joinToString("@")}")
                val validateUserName = validateUser[0]
                val validateUserDomain = validateUser[1]
                if (("$validateUserName@$validateUserDomain").lowercase() == user.username.lowercase()) return null
                return "KRB用户验证失败: ${validateUserName}@${validateUserDomain} != ${user.username}"
            } catch (e: Exception) {
                this.logger.error("Krb ticket validate exception: '${user}', '${pass}', '${decodeTgt?.size}', '${validator?.hashCode()}'", e)
                return "KRB验证异常:${e.message}";
            }
        }
        if (cnf.type == LdapConfig.Type.Ldap && cnf.authWithAdHost == null)
            userkey = "cn=" + uid + "," + user.org.key!!.substring(user.org.key!!.indexOf("dn:") + 4) + "," + cnf.syncDN!! + "," + cnf.rootDN!!;
        if (!ldapService.checkUserPassword(cnf, userkey, pass)) return "LDAP协议密码认证失败"
        return null
    }

    @Transactional
    fun delUser(user: User): Boolean {
        var ok = true;
        if (ok) {
            val devices = clientDeviceService.findAll(QClientDevice.a.user.eq(user))
            devices.forEach { d ->
                d.user = null;
                d.org = orgService.getSysOrg(OrgService.SYS_ORG_KEY_UNKNOWN_CLIENT)
                clientDeviceService.beforeSave(d);
                clientDeviceService.save(d);
                this.logger.info("Set device user to null, ${d}")
            }
            repo.flush();
            repo.delete(user);
            repo.flush();
            this.logger.info("Deleted ${user}")
        }
        return ok;
    }

    private fun _sync(cnf: LdapConfig, record: LdapSyncRecord) {
        logger.info("Sync: ${cnf}, wait 60s to start.")
        Thread.sleep(60 * 1000L);
        runInTran {
            logger.info("Sync: Start")
            val domain = cnf.domain;
            val stopWatch = StopWatch("User sync $cnf")
            val watchPoint = fun(str: String) {
                repo.flush();
                logger.info("Sync: $str");
                stopWatch.stop()
                stopWatch.start(str)
            }
            val progressReporter = AppUtils.ProgressReport("", this.logger, 0);
            val progress = fun(str: String, curr: Int, total: Int) {
                if (progressReporter.prefix != str || progressReporter.total != total)
                    progressReporter.reportTime = 0L;
                progressReporter.total = total;
                progressReporter.prefix = str;
                progressReporter.report(curr);
            }
            stopWatch.start("Init");
            watchPoint("Fix endpoint org miss");
            this.clientDeviceService.findAll(QClientDevice.a.user.isNotNull.and(QClientDevice.a.org.ne(QClientDevice.a.user.org))).forEach {
                logger.warn("${it} org ${it.org} miss. reset it to ${it.user!!.org}.")
                it.org = it.user!!.org
                this.clientDeviceService.beforeSave(it)
                this.clientDeviceService.save(it)
            }
            record.config = cnf;
            record.createdAt = Date();
            val outOrgs = mutableMapOf<String, OutOrgItem>()
            val outUsers = mutableMapOf<String, OutUserItem>()
            if (cnf.type == LdapConfig.Type.Ldap || cnf.type == LdapConfig.Type.ActiveDirectory) {
                val ldapCtx = ldapService.getLdapCtx(cnf)
                try {
                    watchPoint("Get ldap users");
                    val dnToOrgKey = fun(dn: String?): String? {
                        if (dn != null)
                            return "${cnf.id.toString()}:dn:$dn"
                        return null;
                    }
                    watchPoint("Get ldap orgs");
                    ldapService.search(LDAPService.Companion.SearchOptions().apply {
                        this.cnf = cnf
                        this.ctx = ldapCtx
                        this.dn = ldapService.toDN(cnf.syncDN!!, cnf.rootDN!!).dn
                        this.includeChildren = true
                        this.treeResult = false
                        this.filters = arrayOf(LDAPService.d(cnf).getOrgFilter())
                    }).filter { it.type == LDAPService.Companion.LdapItem.Type.Org }.forEach {
                        val id = dnToOrgKey(it.dn.dn)!!
                        val parentId = dnToOrgKey(it.dn.parent?.dn)
                        outOrgs[id] = OutOrgItem(id, it.attrs[LDAPService.S_OU].get().toString(), parentId, it.hierarchy)
                    }
                    ldapService.search(LDAPService.Companion.SearchOptions().apply {
                        this.cnf = cnf
                        this.ctx = ldapCtx
                        this.dn = ldapService.toDN(cnf.syncDN!!, cnf.rootDN!!).dn
                        this.includeChildren = true
                        this.treeResult = false
                        this.filters = arrayOf(LDAPService.d(cnf).getUserFilter())
                    }).filter { it.type == LDAPService.Companion.LdapItem.Type.User }.forEach {
                        val uniqueName = it.attrs[LDAPService.d(cnf).userUniqueName]?.get()?.toString() ?: return@forEach
                        if (uniqueName.isNotEmpty()) {
                            val orgKey = dnToOrgKey(it.dn.parent?.dn)
                            val displayName = it.attrs[LDAPService.S_DISPLAY_NAME]?.get()?.toString();
                            outUsers["$uniqueName@$domain"] = OutUserItem(
                                orgKey,
                                "$uniqueName@$domain",
                                displayName ?: "",
                                it.attrs[LDAPService.S_MAIL]?.get()?.toString() ?: ""
                            )
                        }
                    }
                } finally {
                    ldapCtx.close()
                }
            } else {
                val bocP6Result = bocP6TableService.loadBocP6OrgAndUsers(cnf);
                bocP6Result.users.forEach {
                    outUsers[it.uid] = it
                }
                bocP6Result.orgs.forEach {
                    outOrgs[it.id] = it
                }
            }
            outOrgs.values.forEach {
                it.hierarchy = -1
                var p: OutOrgItem? = it
                while (p != null) {
                    it.hierarchy++
                    if (p.parent == null) break
                    p = outOrgs[p.parent] ?: throw RuntimeException("Org parent not found '${p.parent}'")
                }
            }

            watchPoint("Get db orgs");
            val dbOrgByKeyMap = mutableMapOf<String, Organization>()
            val dbOrgByIDMap = mutableMapOf<Long, Organization>()
            orgService.findAll(
                QOrganization.a.type.eq(Organization.Type.Ldap).and(QOrganization.a.path.like(cnf.syncTo!!.path + "%"))
            ).forEach {
                dbOrgByKeyMap[it.key!!] = it;
                dbOrgByIDMap[it.id!!] = it;
            }
            val repeatOrgKeys = outOrgs.keys.filter { !dbOrgByKeyMap.containsKey(it) }
            repeatOrgKeys.chunked(300).forEachIndexed { idx, orgKeys ->
                val queries = BooleanBuilder();
                queries.and(QOrganization.a.key.`in`(orgKeys));
                orgService.findAll(queries).forEach {
                    dbOrgByKeyMap[it.key!!] = it;
                    dbOrgByIDMap[it.id!!] = it;
                }
                progress("Get db repeat key orgs", idx * 300, repeatOrgKeys.size);
            }

            watchPoint("Get db users");
            val users = mutableMapOf<String, User>()
            this.findAll(QUser.a.org.path.startsWith(cnf.syncTo!!.path)).forEach {
                users[it.username] = it
            }
            val repeatUserKeys = outUsers.keys.filter { !users.containsKey(it) }
            repeatUserKeys.chunked(300).forEachIndexed { idx, usernames ->
                val queries = BooleanBuilder();
                queries.and(QUser.a.username.`in`(usernames));
                repo.findAll(queries).forEach {
                    users[it.username] = it
                }
                progress("Get db repeat key users", idx * 300, repeatUserKeys.size);
            }

            var flashCount = 0;
            watchPoint("Save ${outOrgs.size} orgs");
            // save orgs
            val parentChangeOrgs = mutableListOf<Organization>()
            val savedOrgs = mutableMapOf<Long, Organization>()
            outOrgs.values.sortedBy { it.hierarchy }.forEachIndexed { idx, it2 ->
                progress("Save org", idx, outOrgs.size);
                val outOrg = it2
                val orgKey = outOrg.id
                val org = dbOrgByKeyMap[orgKey] ?: Organization();
                fun orgChangeID(): String {
                    return "${org.type}:${org.name}:${org.parent?.id}";
                }

                val beforeOrgChangeID = orgChangeID()
                val beforeParent = org.parent
                org.name = outOrg.name
                org.type = Organization.Type.Ldap
                org.key = orgKey;
                val ldapOrgParent = outOrg.parent
                if (ldapOrgParent == null) {
                    org.parent = cnf.syncTo!!
                } else {
                    org.parent = dbOrgByKeyMap[ldapOrgParent] ?: throw RuntimeException("${org.key} parent not found ${ldapOrgParent}")
                }
                val orgChanged = orgChangeID() != beforeOrgChangeID

                if (org.id != null && !orgChanged) return@forEachIndexed

                if (org.id == null) record.addOrgCount++
                else {
                    if (org.parent != beforeParent)
                        parentChangeOrgs.add(org)
                    record.updateOrgCount++
                }
                orgService.save(org);
                savedOrgs[org.id!!] = org;
                dbOrgByKeyMap[orgKey] = org;
                flashCount++;
                if (flashCount >= 300) {
                    repo.flush();
                    flashCount = 0;
                }
            }
            watchPoint("Update ${parentChangeOrgs.size} org path")
            parentChangeOrgs.forEachIndexed { pIdx, pOrg ->
                progress("Update org parent", pIdx, parentChangeOrgs.size);
                val childOrg = dbOrgByIDMap.values.filter { it.path.contains("|${pOrg.id}|") }
                childOrg.forEachIndexed { idx, it ->
                    progress("Update ${pOrg} children", idx, childOrg.size);
                    orgService.save(it)
                    if (savedOrgs[it.id] == null) {
                        record.updateOrgCount++
                    }
                }
            }
            watchPoint("Save ${outUsers.size} users");
            val orgChangeUsers = mutableListOf<User>()
            // save users
            outUsers.keys.forEachIndexed { idx, key ->
                progress("Save user", idx, outUsers.size);
                val username = key
                val outUser = outUsers[key]!!
                var user = users[username] ?: User()
                var nickname = outUser.nickname
                if (org.apache.commons.lang3.StringUtils.isAllBlank(nickname)) {
                    logger.warn("User nickname is null: ${outUser}")
                    record.skipUserCount++;
                    return@forEachIndexed
                }
                fun userChangeID(): String {
                    return "${user.type}:${user.username}:${user.nickname}:${user.enabled}:${user.email}:${user.org?.id}";
                }

                var beforeUserChanged = userChangeID();
                user.type = User.Type.Ldap;
                user.username = username;
                user.nickname = nickname;
                user.enabled = true;
                user.email = outUser.email
                var oldOrg = user.org;

                if (outUser.org != null) user.org = dbOrgByKeyMap[outUser.org!!]!!
                else user.org = cnf.syncTo!!
                var userChanged = userChangeID() != beforeUserChanged
                if (user.id != null && !userChanged) return@forEachIndexed

                if (user.id != null) {
                    record.updateUserCount++
                } else record.addUserCount++

                save(user);
                if (user.id != null && oldOrg != user.org) {
                    orgChangeUsers.add(user)
                }

                users[username] = user;
                flashCount++;
                if (flashCount >= 300) {
                    repo.flush();
                    flashCount = 0;
                }
            }
            val chunkedOrgChangeUsers = orgChangeUsers.chunked(300)
            chunkedOrgChangeUsers.forEachIndexed { idx, chunkedUsers ->
                progress("Batch update client device", idx * 300, orgChangeUsers.size);
                val queries = BooleanBuilder();
                queries.and(QClientDevice.a.user.id.`in`(chunkedUsers.map { it.id }.toList()));
                clientDeviceService.findAll(queries).forEach {
                    val realDevice = it;
                    val user = chunkedUsers.find { it.id == realDevice.user?.id };
                    realDevice.user = user;
                    realDevice.org = user?.org ?: orgService.getSysOrg(OrgService.SYS_ORG_KEY_UNKNOWN_CLIENT);
                    clientDeviceService.beforeSave(realDevice);
                    clientDeviceService.save(realDevice);
                }
                repo.flush()
            }
            watchPoint("Filter del user");
            // delete user
            val waitDeletedUsers = mutableListOf<User>()
            users.forEach { (username, user) ->
                if (!outUsers.containsKey(username)) {
                    waitDeletedUsers.add(user)
                }
            }
            if (cnf.syncMaxDeleteSize != null && waitDeletedUsers.size >= cnf.syncMaxDeleteSize!!) {
                throw RuntimeException("Delete user over limit ${waitDeletedUsers.size} > ${cnf.syncMaxDeleteSize}");
            }

            watchPoint("Do del ${waitDeletedUsers.size} user");
            waitDeletedUsers.forEachIndexed { idx, user ->
                progress("Delete user ", idx, waitDeletedUsers.size)
                val ok = delUser(user)
                if (ok)
                    record.removeUserCount++
            }
            watchPoint("Filter del org");
            // delete org
            val orgValues = dbOrgByKeyMap.values.toList()

            val orgTree = mutableMapOf<Long, MutableList<Long>>()
            orgValues.forEach {
                var parent = dbOrgByIDMap[it.parent?.id]
                if (parent != null) {
                    val children = orgTree[parent.id] ?: mutableListOf()
                    children.add(it.id!!)
                    orgTree[parent.id!!] = children
                }
            }
            val waitDeleteOrgs = mutableListOf<Organization>()
            fun addToDelOrg(org: Organization) {
                if (waitDeleteOrgs.contains(org)) return;
                orgTree[org.id!!]?.forEach {
                    addToDelOrg(dbOrgByIDMap[it]!!)
                }
                waitDeleteOrgs.add(org)
            }

            orgValues.forEach { org ->
                if (!waitDeleteOrgs.contains(org) && !outOrgs.containsKey(org.key!!)) {
                    addToDelOrg(org)
                }
            }
            if (cnf.syncMaxDeleteSize != null && waitDeleteOrgs.size >= cnf.syncMaxDeleteSize!!) {
                throw RuntimeException("Delete org over limit ${waitDeleteOrgs.size} > ${cnf.syncMaxDeleteSize}");
            }

            watchPoint("Do del ${waitDeleteOrgs.size} org");
            val waitDeleteOrgsSorted = waitDeleteOrgs.sortedByDescending { it.path.length }
            waitDeleteOrgsSorted.forEachIndexed { idx, org ->
                progress("Delete org", idx, waitDeleteOrgsSorted.size);
                val ok = orgService.delOrg(org);
                if (ok)
                    record.removeOrgCount++;
            }
            repo.flush()
            stopWatch.stop()
            record.success = true;
            logger.info("Sync ${cnf} done ${record}\n" + stopWatch.prettyPrint())
        }
    }

    @Async
    fun sync(id: Long) {
        val locked = redisService.runInLock("user_sync", 0) {
            val record = LdapSyncRecord();
            try {
                _sync(ldapConfigService.findById(id), record)
            } catch (_e: Throwable) {
                var e = _e
                while (e.cause != null) e = e.cause!!;
                record.success = false;
                record.errmsg = e.toString()
                logger.error("Sync fail", _e);
            }
            Thread.sleep(1000 * 1L);
            runInTran {
                record.updatedAt = Date();
                ldapSyncRecordService.save(record);
            }
        }
        if (!locked) logger.warn("Sync ${id} wait lock timeout.")
    }

    fun getNextExecTime(lastExecTime: Date?, scheduleCron: String?): Date? {
        if (scheduleCron == null) return null
        val t = ZonedDateTime.ofInstant((lastExecTime ?: Date()).toInstant(), ZoneId.systemDefault());
        return Date(CronExpression.parse(scheduleCron).next(t)!!.toInstant().toEpochMilli());
    }

    override fun onReady() {
        sysRoleService.initRole()
        orgService.initOrg()
        val c = repo.count();
        if (c == 0L) {
            val u = User();
            u.nickname = "超级管理员"
            u.username = "SuperAdmin"
            u.locked = false
            u.enabled = true
            u.type = User.Type.Local
            var pwd = DigestUtils.sha1Hex(System.currentTimeMillis().toString() + "_" + System.currentTimeMillis())
                .substring(0, 16);
            pwd = pwd.substring(0, 8) + pwd.substring(8, 11).uppercase(Locale.getDefault()) + pwd.substring(11)
            u.password = passwordEncoder.encode(pwd)
            u.manager = true;
            u.org = orgService.getSysOrg(OrgService.SYS_ORG_KEY_ROOT)
            u.manageOrgs = mutableListOf(orgService.getSysOrg(OrgService.SYS_ORG_KEY_ROOT))
            u.manageRoles = sysRoleService.findAll(QSysRole.a.code.`in`(SysRole.Code.SuperManager)).toMutableList()
            repo.save(u)
            repo.flush()
            logger.info("Created default user : ${u.username} ${pwd}")
        } else {
            val queries = BooleanBuilder();
            queries.and(QUser.a.type.eq(User.Type.Local));
            queries.and(QUser.a.org.id.isNull);
            repo.findAll(queries).forEach {
                it.org = orgService.getSysOrg(OrgService.SYS_ORG_KEY_ROOT);
                save(it);
            }
        }
        sysAlertService.addAlertProvider("user_sync_alert") {
            val alerts = mutableListOf<SysAlertService.Companion.SysAlert>()
            this.ldapConfigService.findAll(QLdapConfig.a.syncTo.isNotNull).forEach {
                val lastRecord = this.ldapSyncRecordService.findAll(
                    QLdapSyncRecord.a.config.eq(it),
                    PageAttr(1, 1, arrayListOf("desc_createdAt"))
                ).firstOrNull()
                if (lastRecord != null && !lastRecord.success) {
                    alerts.add(
                        SysAlertService.Companion.SysAlert(
                            "主服务",
                            "域配置用户同步失败：${it.host}:${it.port} 同步到 ${this.orgService.getNames(it.syncTo!!).joinToString("->")}"
                        )
                    )
                }
            }
            return@addAlertProvider alerts;
        }
        val keytabConfigDir = File(appConfigService.keytabConfigDir)
        if (!keytabConfigDir.exists()) {
            if (!keytabConfigDir.mkdirs()) throw RuntimeException("Create ${keytabConfigDir.absolutePath} failed");
        }

        ldapConfigService.findAll(QLdapConfig.a.authWithKrbPrincipal.isNotEmpty.and(QLdapConfig.a.authWithKrbKeytabHash.isNotEmpty)).forEach {
            getKrbValidator(it.authWithKrbPrincipal!!, it.authWithKrbKeytabHash!!, it.authWithKrbKeytab!!);
        }
    }


}
package com.vsimtone.pddp.server.services

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.repositories.UserFileRepository
import com.vsimtone.pddp.server.repositories.UserFileShareRepository
import com.vsimtone.pddp.server.repositories.UserFolderRepository
import jakarta.annotation.Resource
import org.springframework.stereotype.Service


@Service
class UserFileShareService : BaseCrudService<UserShare, UserFileShareRepository>(QUserShare.a) {



}
package com.vsimtone.pddp.server.convert


import com.vsimtone.pddp.server.utils.JSONUtils
import jakarta.persistence.AttributeConverter
import java.io.Serializable

open class JsonConverter<T>(impl: Class<T>) : AttributeConverter<T, String>, Serializable {

    private val implCls: Class<T>?

    init {
        this.implCls = impl
    }

    override fun convertToDatabaseColumn(attribute: T): String {
        return JSONUtils.om.writeValueAsString(attribute)
    }

    override fun convertToEntityAttribute(dbData: String?): T? {
        if (dbData == null || dbData.isEmpty()) return null
        return JSONUtils.om.readValue<T>(dbData, implCls)
    }
}

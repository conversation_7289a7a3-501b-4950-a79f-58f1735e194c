package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.ClientDevice
import com.vsimtone.pddp.server.entities.QClientDeviceHistory
import com.vsimtone.pddp.server.services.ClientDeviceHistoryService
import com.vsimtone.pddp.server.utils.RestUtil
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("client_device_histories")
class ClientDeviceHistoryController : BaseHttpController() {

    @Autowired
    lateinit var clientDeviceHistoryService: ClientDeviceHistoryService;

    @Secured(Permissions.CLIENT_VIEW)
    @RequestMapping("")
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("fromIP", required = false) fromIP: String?,
        @RequestParam("fieldName", required = false) fieldName: String?,
        @RequestParam("systemType", required = false) systemType: ClientDevice.SysType?,
        @RequestParam("hostname", required = false) hostname: String?,
    ): RestResp {
        val queries = BooleanBuilder();
        val a = QClientDeviceHistory.a;
        if (!StringUtils.isEmpty(fromIP?.trim()))
            queries.and(a.clientDevice.fromIP.like("%$fromIP%"));

        if (!StringUtils.isEmpty(fieldName?.trim()))
            queries.and(a.fieldName.like("%$fieldName%"));

        if (systemType != null)
            queries.and(a.clientDevice.systemType.eq(systemType))

        if (!StringUtils.isEmpty(hostname?.trim()))
            queries.and(a.clientDevice.hostname.like("%$hostname%"));
        pageAttr.sort = arrayListOf("desc_createdAt");
        val p = clientDeviceHistoryService.findAll(queries, pageAttr);
        p.forEach {
            it.putBundle("clientDevice", RestUtil.unwrapBean(it.clientDevice))
        }
        return jsonOut(p);
    }

}
package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.StringPath
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.entities.Organization
import com.vsimtone.pddp.server.entities.QOrganization
import com.vsimtone.pddp.server.entities.User
import com.vsimtone.pddp.server.services.OptLogService
import com.vsimtone.pddp.server.services.OrgService
import com.vsimtone.pddp.server.services.UserService
import com.vsimtone.pddp.server.utils.Log
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.transaction.NoTransactionException
import org.springframework.transaction.interceptor.TransactionAspectSupport

open class BaseController {
    protected open var logger = Log[this.javaClass];

    @Autowired
    lateinit var userService: UserService;

    @Autowired
    lateinit var orgService: OrgService;

    @Autowired
    lateinit var optLogService: OptLogService;

    fun rollbackTransaction() {
        try {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } catch (e: NoTransactionException) {

        }
    }

    fun addOptLog(
        type: String,
        moduleName: String,
        objName: String,
        _details: Any?
    ) {
        val user = userService.currUser()
        optLogService.addOptLog(user?.username, type, moduleName, objName, _details)
    }

    open fun throwErrorAccess() {
        throw RestException(403, "非法数据访问")
    }

    open fun throwErrorStatus() {
        throw RestException(409, "信息已过期，请刷新界面")
    }

    fun throwErrOut(code: Int, data: String) {
        throw RestException(code, data)
    }

    fun jsonOut(code: Int, data: Any?): RestResp {
        if (code != 0)
            rollbackTransaction();
        var rp = RestResp(code, data)
        return rp
    }

    fun jsonOut(code: Int, data: String): RestResp {
        if (code != 0)
            rollbackTransaction();
        var rp = RestResp(code, data)
        return rp
    }

    fun jsonOut(data: Any?): RestResp {
        var rp = RestResp(data)
        return rp
    }

    fun currUser(): User {
        return userService.findById(userService.currUser()!!.id)
    }

    fun currUserMgrOrgQuery(a: QOrganization): Predicate? {
        return currUserMgrOrgQuery(a.path)
    }

    fun currUserMgrOrgQuery(path: StringPath): Predicate? {
        val currUser = userService.currUser()!!
        if (!currUser.manager) throwErrorAccess()
        return userService.mgrOrgQuery(currUser.manageOrgs, path)
    }

    fun getCurrUserCanMgrOrg(_organization: Organization): Boolean {
        var org = _organization
        if (org.id == null)
            throw RestException(500, "Org id is null")
        if (!org.loadFromDB)
            org = orgService.findById(org.id!!)
        return getCurrUserCanMgrOrg(org.path);
    }

    fun getCurrUserCanMgrOrg(path: String): Boolean {
        val currUser = userService.currUser()!!
        currUser.manageOrgs.forEach {
            if (path.startsWith(it.path))
                return true
        }
        return false;
    }

    fun currUserMgrOrgRequire(_organization: Organization) {
        if (_organization.id == null)
            throw RestException(403, "请选择机构")

        var org = _organization
        if (!org.loadFromDB)
            org = orgService.findById(org.id!!)
        if (!getCurrUserCanMgrOrg(org))
            throw RestException(403, "您没有 ${org.name} 的管理权限")
    }

    fun currUserMgrOrgRequire(path: String, name: String) {
        if (!getCurrUserCanMgrOrg(path))
            throw RestException(403, "您没有 ${name} 的管理权限")
    }
}
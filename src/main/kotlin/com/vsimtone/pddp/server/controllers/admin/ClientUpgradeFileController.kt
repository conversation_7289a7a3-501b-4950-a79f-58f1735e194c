package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.AppUtils
import com.vsimtone.pddp.server.utils.RestUtil
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.util.*

@RestController("ClientUpgradeFileController")
@RequestMapping("client_upgrade_files")
class ClientUpgradeFileController : BaseHttpController() {

    @Autowired
    lateinit var service: ClientUpgradeFileService

    @Autowired
    lateinit var fileService: FileService

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var clientUpgradeRecordService: ClientUpgradeRecordService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService


    @RequestMapping("")
    @Secured(Permissions.CLIENT_VIEW)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("sysType", required = false) sysType: ClientDevice.SysType?,
        @RequestParam("clientVersion", required = false) clientVersion: String?
    ): RestResp {
        val a = QClientUpgradeFile.a
        val queries = BooleanBuilder()
        if (search != null) {
        }

        if(sysType !=null)
            queries.and(a.sysType.eq(sysType));
        if(!StringUtils.isEmpty(clientVersion?.trim()))
            queries.and(a.clientVersion.eq(clientVersion));

        val p = service.findAll(queries, pageAttr)
        p.content.forEach {
            it.putBundle("downFileToken", it.upgradeFileToken);
            val upgradeOrgs = mutableListOf<Organization>();
            it.orgs.forEach { upit ->
                upgradeOrgs.add(orgService.findById(java.lang.Long.valueOf(upit)))
            }
            it.putBundle("upgradeOrgs", upgradeOrgs)
            it.putBundle("freeToken", service.getTokenCount(it).get())
        }
        return jsonOut(p);
    }

    @RequestMapping("{id:\\d+}")
    @Secured(Permissions.CLIENT_VIEW)
    fun view(@PathVariable("id") id: Long): RestResp {
        return jsonOut(service.findById(id));
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.CLIENT_EDIT)
    @Transactional
    fun delete(@PathVariable("id") id: Long): RestResp {
        val data = service.findById(id)
        if (data.enabled)
            return jsonOut(1, "请先禁用该升级才能删除")
        clientUpgradeRecordService.findAll(QClientUpgradeRecord.a.clientUpgradeFile.eq(data)).forEach {
            clientUpgradeRecordService.delete(it)
        }
        service.delete(data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.CLIENT_EDIT)
    @Transactional
    fun edit(
        @ModelAttribute("form") form: ClientUpgradeFile,
        @RequestParam("file", required = false) file: MultipartFile?
    ): RestResp {
        val data: ClientUpgradeFile;
        form.orgs.forEach {
            currUserMgrOrgRequire(orgService.findById(it.toLong()))
        }
        if (form.id != null) {
            data = service.findById(form.id!!);
        } else {
            if (file == null) return jsonOut(1, "请上传升级文件")
            data = ClientUpgradeFile();
            if (file.originalFilename?.indexOf("_uos", 0, true) != -1 && form.sysType != ClientDevice.SysType.UOS) {
                return jsonOut(1, "您上传的升级包中含有UOS字样，但是选择的系统类型不是UOS，请确认操作无误")
            }
            if (file.originalFilename?.indexOf("_kylin", 0, true) != -1 && form.sysType != ClientDevice.SysType.Kylin) {
                return jsonOut(1, "您上传的升级包中含有Kylin字样，但是选择的系统类型不是Kylin，请确认操作无误")
            }
            data.sysType = form.sysType
            try {
                service.updateUpgradeFileInfo(data, file.inputStream);
            } catch (e: Exception) {
                if (e is RestException)
                    throw e;
                logger.error("Upgrade file parse error ${form}", e);
                throw RestException(1, "解析上传包失败：请上传正确的终端升级包");
            }
            val f = fileService.upload(file);
//            val exists = service.findOne(QClientUpgradeFile.a.upgradeFileToken.eq(f.sha256sum))
//            if (exists != null)
//                throw RestException(1, "该升级包已存在，请不要重复上传");
//            data.upgradeFileToken = f.token;
        }
        data.enabled = form.enabled;
        data.anonymousDownloadable = form.anonymousDownloadable;
        data.upgradeInfo = form.upgradeInfo;
        data.matchClientVersions = form.matchClientVersions;
        data.matchSysVersions = form.matchSysVersions;
        data.maxPushCount = form.maxPushCount;
        data.orgs = form.orgs;
        data.upgradeRateInMinute = form.upgradeRateInMinute;
        data.allowedDowngrade = form.allowedDowngrade;
        val isNew = data.id == null;
        service.save(data);
        service.runInAfterTranCommitted {
            if (data.enabled)
                service.updateUpgradeState(data.id!!);
        }
        if (isNew)
            addOptLog(OptLog.ADD, "终端升级", data.sysType.name + "_" + data.sysArch.name + "_" + data.clientVersion, data);
        else
            addOptLog(OptLog.EDIT, "终端升级", data.sysType.name + "_" + data.sysArch.name + "_" + data.clientVersion, data);
        return jsonOut(data);
    }


    @RequestMapping(value = ["push_upgrade"], method = [RequestMethod.POST])
    @Secured(Permissions.CLIENT_EDIT)
    @Transactional
    fun pushUpgrade(
        @RequestParam("id") uid: Long,
        @RequestParam("deviceIds") deviceIds: Array<Long>
    ): RestResp {
        val upgradeItem = service.findById(uid);
        var successCount = 0;
        deviceIds.forEach {
            val device = clientDeviceService.findById(it);
            currUserMgrOrgRequire(device.org!!)
            if (device.systemArch != upgradeItem.sysArch) return@forEach
            if (device.systemType != upgradeItem.sysType) return@forEach

            if (upgradeItem.matchSysVersions.isNotEmpty() && !upgradeItem.matchSysVersions.contains(device.systemVersion)) return@forEach
            if (upgradeItem.matchClientVersions.isNotEmpty() && !upgradeItem.matchClientVersions.contains(device.version)) return@forEach
            if (!upgradeItem.allowedDowngrade && device.versionNum >= AppUtils.getVersionNumber(upgradeItem.clientVersion)) return@forEach

            val e = clientUpgradeRecordService.findOne(QClientUpgradeRecord.a.clientUpgradeFile.eq(upgradeItem).and(QClientUpgradeRecord.a.clientDevice.eq(device)))
            if (e != null) return@forEach;
            val record = ClientUpgradeRecord();
            record.clientUpgradeFile = upgradeItem;
            record.clientDevice = device;
            record.manualPush = true;
            clientUpgradeRecordService.save(record);
            successCount++;
        }
        upgradeItem.updatedAt = Date()
        service.save(upgradeItem)
        service.runInAfterTranCommitted {
            service.updateUpgradeState(upgradeItem.id!!);
        }
        return jsonOut(successCount);
    }

    @Secured(Permissions.CLIENT_VIEW)
    @RequestMapping("{id:\\d+}/records")
    fun records(@PathVariable("id") id: Long, @ModelAttribute("page") pageAttr: PageAttr,
                @RequestParam("status", required = false) status: Array<ClientDevice.Status>?,
                @RequestParam("manualPush", required = false) manualPush: Boolean?,
                @RequestParam("success", required = false) success: Boolean?): RestResp {
        val queries = BooleanBuilder();
        queries.and(QClientUpgradeRecord.a.clientUpgradeFile.id.eq(id))
        if (status != null && status.size !=0)
            queries.and(QClientUpgradeRecord.a.clientDevice.status.`in`(status.toList()))
        if (manualPush != null) {
            queries.and(QClientUpgradeRecord.a.manualPush.eq(manualPush))
        }
        if (success != null) {
            queries.and(QClientUpgradeRecord.a.success.eq(success))
        }
        var p = clientUpgradeRecordService.findAll(queries, pageAttr)
        p.content.forEach {
            RestUtil.unwrapBean(ClientDevice::user, it.clientDevice)
            RestUtil.unwrapBean(ClientDevice::org, it.clientDevice)
            it.putBundle("lastMatchTime", (service.deviceUpgradeMatchTime[it.clientDevice.id!!.toString()])?.toLong())
        }
        return jsonOut(p);
    }
}

package com.vsimtone.pddp.server.controllers.admin

import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.SysRole
import com.vsimtone.pddp.server.services.LicenseService
import com.vsimtone.pddp.server.services.MailService
import com.vsimtone.pddp.server.services.SysAlertService
import com.vsimtone.pddp.server.services.SysService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import jakarta.servlet.http.HttpServletRequest

@RestController("adminStatsController")
@RequestMapping("/sys")
class SysController : BaseHttpController() {

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var sysService: SysService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var sysAlertService: SysAlertService

    @Autowired
    lateinit var licenseService: LicenseService

    @Autowired
    lateinit var mailService: MailService

    @RequestMapping("server_nodes")
    @Secured(Permissions.SYS_STATS)
    fun serverNodes(): RestResp {
        return jsonOut(
            PageImpl(
                sysService.serverNodes.readAllValues().toMutableList(),
                PageAttr().get(),
                sysService.serverNodes.size.toLong()
            )
        )
    }

    @RequestMapping("userIsSyncing")
    @Secured(Permissions.SYS_STATS)
    fun userIsSyncing(): RestResp {
        return jsonOut(userService.isSyncing())
    }

    @RequestMapping("alerts")
    @Secured(Permissions.SYS_STATS)
    fun alerts(request: HttpServletRequest): RestResp {
        request.setAttribute("SKIP_LOG", true)
        val isSuperManager = currUser().manageRoles.find { it.code == SysRole.Code.SuperManager } != null
        if (!isSuperManager || licenseService.getLicenseStatus() != LicenseService.Companion.LicenseStatus.Active)
            return jsonOut(arrayOf<String>());
        if (userService.isSyncing())
            return jsonOut(arrayOf(SysAlertService.Companion.SysAlert("主服务", "用户同步中,请注意!")));
        return jsonOut(sysAlertService.sysAlerts)
    }

    @PostMapping("send_test_mail")
    @Secured(Permissions.APP_CONFIG_EDIT)
    fun sendTestMail(@RequestParam("to") to: String): RestResp {
        return try {
            mailService.directSend("邮件配置测试", "您的邮件配置正确，可以正常收到邮件。", to)
            this.jsonOut(true)
        } catch (e: Exception) {
            this.logger.error("Mail test fail", e)
            this.jsonOut(e.message)
        }
    }
}
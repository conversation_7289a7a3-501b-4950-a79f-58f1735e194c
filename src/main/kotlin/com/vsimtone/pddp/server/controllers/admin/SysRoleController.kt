package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.entities.QSysRole
import com.vsimtone.pddp.server.entities.SysRole
import com.vsimtone.pddp.server.services.SysRoleService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController("SysRoleController")
@RequestMapping("sys_roles")
class SysRoleController : BaseHttpController() {

    @Autowired
    lateinit var service: SysRoleService

    @RequestMapping("")
    @Secured()
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("perm", required = false) perm: String?
    ): RestResp {
        val q = QSysRole.a
        val queries = BooleanBuilder()
        val p = service.findAll(queries, pageAttr);
        val content = p.mutableContent()
        content.removeIf { it.code == SysRole.Code.SuperManager }
        if (StringUtils.isNotEmpty(perm)) {
            content.removeIf { it.permissions.indexOf(perm) == -1 }
        }
        return jsonOut(p);
    }

}

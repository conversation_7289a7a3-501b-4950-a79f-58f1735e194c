package com.vsimtone.pddp.server.controllers.edge_node

import com.vsimtone.pddp.server.configs.WebsocketConfig
import com.vsimtone.pddp.server.services.BaseService
import com.vsimtone.pddp.server.services.LicenseService
import com.vsimtone.pddp.server.services.EdgeNodeService
import com.vsimtone.pddp.server.utils.Log
import jakarta.websocket.*
import jakarta.websocket.server.ServerEndpoint
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@ServerEndpoint("/edge_node_endpoint", configurator = WebsocketConfig.Companion.Configurator::class)
@Component("edgeNodeControllerEndpoint")
class EdgeNodeController {

    protected open var logger = Log[this.javaClass];

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var licenseService: LicenseService

    @OnOpen
    fun onOpen(session: Session) {
        BaseService.appCtx.autowireCapableBeanFactory.autowireBean(this)
        if (licenseService.getLicenseStatus() != LicenseService.Companion.LicenseStatus.Active) {
            this.logger.warn("Disconnect edge node. license not active.")
            session.close(CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "License not active."));
            return;
        }
        edgeNodeService.onWSOpen(session)
    }

    @OnMessage
    fun onMessage(session: Session, binaryMessage: ByteArray) {
        if (licenseService.getLicenseStatus() != LicenseService.Companion.LicenseStatus.Active) {
            session.close(CloseReason(CloseReason.CloseCodes.CANNOT_ACCEPT, "License not active."));
            return;
        }
        edgeNodeService.onWSMessage(session, binaryMessage)
    }

    @OnClose
    fun onClose(session: Session, reason: CloseReason) {
        if (licenseService.getLicenseStatus() != LicenseService.Companion.LicenseStatus.Active) {
            return;
        }
        edgeNodeService.onWSClose(session, reason)
    }

    @OnError
    fun onError(session: Session, e: Throwable) {
        if (licenseService.getLicenseStatus() != LicenseService.Companion.LicenseStatus.Active) {
            return;
        }
        edgeNodeService.onWSError(session, e)
    }
}
package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.repositories.UserFolderRepository
import com.vsimtone.pddp.server.services.FileService
import com.vsimtone.pddp.server.services.UserFolderService
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RequestMapping("user_folder")
@RestController
class UserFolderController : BaseHttpController() {


    @Resource
    lateinit var userFolderService: UserFolderService


    @Secured(Permissions.USER_FOLDER_VIEW)
    @RequestMapping("list")
    @JsonView(JsonViews.SpecList::class)
    fun list(
        @RequestParam("parentId", required = false) parentId: Long?,
        @RequestParam("name", required = false) name: String?
    ): RestResp {
        val user:User = currUser()
        val queries = BooleanBuilder();
        val a = QUserFolder.a;
        queries.and(a.owner.eq(user))
        if (StringUtils.isNotEmpty( name))
            queries.and(a.name.like("%${name}%"))
        if(parentId == null){
            queries.and(a.parent.isNull)
        }else{
            val parent: UserFolder = userFolderService.findById(parentId)

            queries.and(a.parent.eq(parent))
        }
        return jsonOut(userFolderService.findAll(queries));
    }

    @Secured(Permissions.USER_FOLDER_VIEW)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.SpecView::class)
    fun get(@PathVariable("id") id: Long): RestResp {
        val user: User = currUser()
        val folder = userFolderService.findById(id)
        if (folder.owner.id != user.id) {
            return jsonOut(0, "没有权限")
        }
        return jsonOut(folder)
    }



    // 创建或修改目录
    @Secured(Permissions.USER_FOLDER_EDIT)
    @PostMapping("")
    @Transactional
    fun edit(@ModelAttribute("form") form: UserFolder): RestResp {

        val user: User = currUser()

        if (StringUtils.isEmpty(form.name)) {
            return jsonOut(0, "请输入目录名称");
        }
        var userFolder: UserFolder? = null
        if (form.id != null) {
            userFolder = userFolderService.findById(form.id!!)

            userFolder.name = form.name
            userFolder.path = userFolder.parent!!.path + form.name
            userFolderService.save(userFolder)
        } else {
            if(form.parent == null)
                return jsonOut(0, "请选择上级目录");
            userFolder =userFolderService.createFolder(user,  form.parent, form.name)
        }

        return jsonOut(userFolder);
    }


    @Secured(Permissions.USER_FOLDER_EDIT)
    @DeleteMapping("")
    @Transactional
    fun delete(@RequestParam("id",  required = true) id: Long): RestResp {
        val userFolder: UserFolder = userFolderService.findById(id)
        val user: User = currUser()
        if (userFolder.owner.id != user.id) {
            return jsonOut(0, "没有权限")
        }
        userFolderService.delete(userFolder)
        return jsonOut(id)
    }



}
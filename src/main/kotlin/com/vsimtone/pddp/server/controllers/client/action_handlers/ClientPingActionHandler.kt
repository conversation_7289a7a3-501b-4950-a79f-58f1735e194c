package com.vsimtone.pddp.server.controllers.client.action_handlers

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.ClientDeviceClientConfig
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.controllers.edge_node.msg_handlers.ClientActionMsgHandler
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.AppUtils
import com.vsimtone.pddp.server.utils.CryptUtils
import com.vsimtone.pddp.server.utils.JSONUtils
import com.vsimtone.pddp.server.utils.Log
import jakarta.annotation.PostConstruct
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Component
import java.util.*
import kotlin.math.max

@Component
class ClientPingActionHandler {
    @Autowired
    lateinit var clientActionMsgHandler: ClientActionMsgHandler

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var orgService: OrgService

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var ldapConfigService: LdapConfigService

    @Autowired
    lateinit var passwordEncoder: PasswordEncoder

    @Autowired
    lateinit var ldapService: LDAPService

    @Autowired
    lateinit var clientNotifyMsgService: ClientNotifyMsgService

    var logger = Log.get(this.javaClass)

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var clientUpgradeFileService: ClientUpgradeFileService

    @Autowired
    lateinit var licenseService: LicenseService

    @Autowired
    lateinit var edgeNodeRouteService: EdgeNodeRouteService

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var edgeNodeClusterService: EdgeNodeClusterService


    @PostConstruct
    fun init() {
        clientActionMsgHandler.addActionHandler("btest") { it, sess ->
            val delay = (it.query["delay"]?.firstOrNull() ?: "0").toString().toInt()
            if (delay > 0) {
                Thread.sleep(delay.toLong())
            }
            val hash = CryptUtils.sha256hex(it.body)
            JSONUtils.toString(
                mapOf(
                    "name" to it.query["name"],
                    "hash" to hash,
                    "method" to it.action,
                    "query" to it.query,
                    "delay" to delay,
                    "body" to it.body,
                    "fromIP" to it.fromIP,
                    "realIP" to it.fromIP
                )
            )
        }
        clientActionMsgHandler.addActionHandler("clientlogin") { it, sess ->
            this.onLogin(it, sess)
        }
        clientActionMsgHandler.addActionHandler("clientloopquery") { it, sess ->
            this.onPing(it, sess)
        }
        clientActionMsgHandler.addActionHandler("list-domain") { it, sess ->
            this.onListDomain(it, sess)
        }
    }

    companion object {
        class DomainInfo {
            var domain: String? = null
            var gssSPN: String? = null
        }

        class PingParams {
            var appVersion: String? = null
            var userSessions: List<Map<String, Any>>? = null
            var username: String? = null
            var orgPath: String? = null
            var uuid: String? = null
            var host: String? = null
            var os: String? = null
            var ip: String? = null
            var mac: String? = null
            var cpuCount: Int? = null
            var memTotal: Long? = null
            var memUsed: Long? = null
            var swapTotal: Long? = null
            var swapUsed: Long? = null
            var uptime: String? = null
            var notifiedMsgIdx = 0L
            var healthState: Map<String, String>? = null
            var fsList: String? = null
        }

        class PingRespBody : ClientActionMsgHandler.Companion.ClientActionBaseResp() {
            var orgPath: String? = null
            var username: String? = null
            var nickname: String? = null
            var pendingAction: String? = null
            var notifyMsgIdx: Long? = null
            var notifyMsgs: List<Map<String, Any>>? = null
            var clientUpgrade: Map<String, Any?>? = null
            var domains: List<DomainInfo>? = null
        }

        class LoginParams {
            var type: Int? = null
            var user: String? = null
            var pass: String? = null
            var uuid: String? = null
            var robotAuthMsg: String? = null
            var robotAuthHash: String? = null
        }

        class LoginRespBody : ClientActionMsgHandler.Companion.ClientActionBaseResp() {
            var username = ""
            var orgPath = ""
        }

        class ListDomainParams {
            var uuid: String? = null
            var type: String? = null
            var user: String? = null
            var domain: String? = null
        }

        class ListDomainRespBody : ClientActionMsgHandler.Companion.ClientActionBaseResp() {
            var items: List<DomainInfo>? = null
        }

    }

    fun onListDomain(
        request: ClientActionMsgHandler.Companion.ClientActionRequest,
        sess: EdgeNodeService.WSSession
    ): Any {
        var params = JSONUtils.toObject(request.body, ListDomainParams::class.java)
        var resp = ListDomainRespBody()
        resp.items =
            ldapConfigService.findAll(QLdapConfig.a.syncTo.isNotNull.and(QLdapConfig.a.domain.isNotNull).and(QLdapConfig.a.authWithKrbKeytabHash.isNotNull))
                .map {
                    val info = DomainInfo()
                    info.domain = it.domain
                    info.gssSPN = it.authWithKrbPrincipal
                    info
                }
        if (StringUtils.isNotEmpty(params.domain)) {
            resp.items = resp.items!!.toMutableList().filter { it.domain!!.lowercase() == params.domain!!.lowercase() }
        }
        return resp;
    }

    fun onLogin(request: ClientActionMsgHandler.Companion.ClientActionRequest, sess: EdgeNodeService.WSSession): Any {
        var params = JSONUtils.toObject(request.body, LoginParams::class.java)
        var respBody = LoginRespBody()
        var errorResp = fun(err: String): LoginRespBody {
            respBody.info = err
            respBody.code = 1
            return respBody
        }
        if (userService.isSyncing()) return errorResp("用户同步中")
        if (StringUtils.isEmpty(params.uuid) || StringUtils.isEmpty(params.user) || params.type == null)
            return errorResp("参数错误")

        var clientDevice = clientDeviceService.findByDeviceIdInCache(params.uuid!!) ?: return errorResp("终端不存在")
        if (clientDevice.status == ClientDevice.Status.Expired) return errorResp("终端已失效")
        var usernames = params.user!!.split(Regex("@"), 2);
        var user: User
        if (usernames.size == 2)
            user = userService.findByUsernameAndDomain(usernames[0], usernames[1]) ?: return errorResp("域用户不存在")
        else
            user = userService.findByUsername(usernames[0]) ?: return errorResp("用户不存在")

        var robotLoginDone = false;

        if (StringUtils.isNotEmpty(appConfigService.robotClientAuthKey) && StringUtils.isNotEmpty(params.robotAuthMsg) && StringUtils.isNotEmpty(params.robotAuthHash)) {
            var hashSrc =
                "${appConfigService.robotClientAuthKey}" +
                    "[" +
                    "${params.uuid}:" +
                    "${Math.floor(System.currentTimeMillis() / (600 * 1000.0)).toLong()}:" +
                    "${params.user}:" +
                    "${params.pass}:" +
                    "${params.robotAuthMsg}" +
                    "]" +
                    "${appConfigService.robotClientAuthKey}"
            var hash = CryptUtils.sha256hex(hashSrc)
            if (hash != params.robotAuthHash) {
                throw RuntimeException("Robot client login fail: ${request.fromIP} ${params.uuid}")
            }
            robotLoginDone = true;
        }
        if (!robotLoginDone) {
            val errMsg = userService.checkPassword(user, params.pass!!);
            if (errMsg != null) return errorResp("认证失败:${errMsg}")
        }
        clientDevice.user = user
        clientDevice.org = user.org
        clientDevice.loginProgramTime = Date();
        if (params.type == ClientDeviceClientConfig.AuthMode.Manual.ordinal + 1)
            clientDevice.loginType = ClientDevice.LoginType.Manual
        else if (params.type == ClientDeviceClientConfig.AuthMode.Auto.ordinal + 1)
            clientDevice.loginType = ClientDevice.LoginType.Auto
        else return errorResp("type参数错误: ${params.type}")

        clientDeviceService.saveDelay(clientDevice, false)
        respBody.username = user.username
        respBody.orgPath = user.org.path
        return respBody;
    }

    fun onPing(request: ClientActionMsgHandler.Companion.ClientActionRequest, sess: EdgeNodeService.WSSession): Any {
        var params = JSONUtils.toObject(request.body, PingParams::class.java)
        var respBody = PingRespBody()
        var canSendPendingAction = true;
        var errorResp = fun(err: String): PingRespBody {
            respBody.info = err
            respBody.code = 1
            return respBody
        }
        if (userService.isSyncing()) return errorResp("用户同步中")
        if (StringUtils.isEmpty(params.uuid))
            return errorResp("参数错误")
        if (StringUtils.isEmpty(params.username))
            params.username = null

        var clientDevice = clientDeviceService.findByDeviceIdInCache(params.uuid!!) ?: ClientDevice()
        if (clientDevice.id == null) {
            clientDevice.deviceId = params.uuid!!;
        }
        var fromIP = request.fromIP
        var nowDate = Date();

        if (clientDevice.pendingActions.isEmpty() && clientDevice.id != null && clientDevice.status != ClientDevice.Status.Offline && clientDevice.hostname.indexOf("robot_") == -1) {
            if (
                fromIP != clientDevice.fromIP &&
                params.host != clientDevice.hostname &&
                params.mac != clientDevice.localIPMAC
            ) {
                logger.warn("Repeat device id: ${params}, ${clientDevice.toFullString()}, reset it.")
                clientDeviceService.needResetDeviceIdMap[fromIP] = System.currentTimeMillis();
                clientDevice.pendingActions = arrayOf(ClientDevice.Action.ResetID.name)
            }
        }
        if (clientDevice.pendingActions.isEmpty() && clientDevice.id != null && clientDevice.status == ClientDevice.Status.Online) {
            var edgeNodeCluster: EdgeNodeCluster? = null;
            if (clientDevice.edgeNodeClusterId != null) {
                try {
                    edgeNodeCluster = edgeNodeClusterService.findById(clientDevice.edgeNodeClusterId!!);
                } catch (_: Exception) {
                }
            }
            if (edgeNodeCluster == null || !edgeNodeCluster.enabled) {
                clientDevice.pendingActions = arrayOf(ClientDevice.Action.ResetRoute.name);
                this.logger.info("${clientDevice} edge node miss, oldEdgeNodeCluster=${edgeNodeCluster}");
            } else {
                val route = edgeNodeRouteService.route(arrayOf(clientDevice.fromIP, clientDevice.localIPAddr))
                if (route != null && route.edgeNodeClusters.isNotEmpty() && route.edgeNodeClusters.find { it == edgeNodeCluster.id } == null) {
                    this.logger.info("${clientDevice} route change, oldEdgeNodeCluster=${edgeNodeCluster}");
                    clientDevice.pendingActions = arrayOf(ClientDevice.Action.ResetRoute.name);
                }
            }
        }

        clientDevice.hostname = params.host!!
        clientDevice.fromIP = fromIP

        if (StringUtils.isNotEmpty(params.ip))
            clientDevice.localIPAddr = params.ip
        if (StringUtils.isNotEmpty(params.mac))
            clientDevice.localIPMAC = params.mac

        if (clientDevice.status != ClientDevice.Status.Online)
            canSendPendingAction = false;
        if (clientDevice.status != ClientDevice.Status.Online)
            clientDevice.firstOnlineTime = nowDate;
        clientDevice.lastOnlineTime = nowDate;
        clientDevice.status = ClientDevice.Status.Online;

        clientDevice.systemCpuCount = params.cpuCount
        clientDevice.systemMemTotal = params.memTotal
        clientDevice.systemMemUsed = params.memUsed
        clientDevice.systemSwapTotal = params.swapTotal
        clientDevice.systemSwapUsed = params.swapUsed
        clientDevice.healthState = params.healthState
        clientDevice.fsList = params.fsList

        clientDeviceService.updateHealthScore(clientDevice, params.healthState?.toMutableMap())
        clientDeviceService.updateSystemVersion(clientDevice, params.os);

        if (params.uptime != null)
            clientDeviceService.updateUptime(clientDevice, params.uptime)
        else
            clientDevice.systemStartTime = null;

        if (params.appVersion != null)
            clientDevice.version = params.appVersion!!;

        clientDevice.versionNum = AppUtils.getVersionNumber(clientDevice.version);

        if (params.userSessions != null)
            clientDevice.userSessions = JSONUtils.toString(params.userSessions!!)
        else
            clientDevice.userSessions = null;
        if (params.userSessions != null && params.userSessions!!.isNotEmpty()) {
            clientDevice.username = params.userSessions!!.first()["user"] as String? ?: ""
        }
        clientDevice.edgeNodeClusterId = sess.edgeNode!!.cluster!!.id

        if (clientDevice.id == null || clientDevice.status == ClientDevice.Status.Expired) {
            if (!licenseService.requireClientDeviceLicense()) {
                if (clientDevice.id == null)
                    return errorResp("授权已满")
                clientDevice.status = ClientDevice.Status.Expired;
                canSendPendingAction = false;
            }
            if (clientDevice.id == null) {
                canSendPendingAction = false;
                clientDeviceService.runInTran {
                    clientDeviceService.beforeSave(clientDevice);
                    clientDeviceService.save(clientDevice);
                }
            }
        }

        var notifiedMsgIdx = max(params.notifiedMsgIdx, clientDevice.notifiedMsgIdx);
        if (notifiedMsgIdx > clientDevice.maxNotifyMsgIdx) {
            notifiedMsgIdx = clientDevice.notifiedMsgIdx;
        }
        respBody.notifyMsgIdx = notifiedMsgIdx;

        if (notifiedMsgIdx > clientDevice.notifiedMsgIdx && notifiedMsgIdx <= clientDevice.maxNotifyMsgIdx) {
            clientDevice.notifiedMsgIdx = notifiedMsgIdx
            clientNotifyMsgService.runInTran {
                clientNotifyMsgService.repo.setNotified(clientDevice, notifiedMsgIdx)
            }
        }
        if (notifiedMsgIdx < clientDevice.maxNotifyMsgIdx) {
            val queries = BooleanBuilder()
            val a = QClientNotifyMsg.a
            queries.and(a.device.eq(clientDevice))
            queries.and(a.notified.eq(false))
            queries.and(a.msgIdx.gt(notifiedMsgIdx))
            val msgs = clientNotifyMsgService.findAll(queries, PageAttr(PageAttr.FIRST_NUM, 10, null, false))
            if (msgs.isEmpty) {
                val beforeMaxNotifyMsgIdx = clientDevice.maxNotifyMsgIdx
                clientDevice.maxNotifyMsgIdx = clientNotifyMsgService.query().select(a.msgIdx.max()).where(a.device.eq(clientDevice)).fetchFirst() as Long;
                logger.warn("Notify max index error. reset ${beforeMaxNotifyMsgIdx} -> ${clientDevice.maxNotifyMsgIdx}. ${clientDevice}")
            } else {
                respBody.notifyMsgIdx = msgs.maxOf { it.msgIdx }
                val notifiedMsgs = mutableListOf<Map<String, Any>>()
                msgs.forEach {
                    notifiedMsgs.add(
                        mapOf(
                            "type" to it.type.toString(),
                            "title" to it.title,
                            "msg" to it.msg,
                            "target" to it.target,
                            "msgIdx" to it.msgIdx
                        )
                    )
                }
                respBody.notifyMsgs = notifiedMsgs
                if (msgs.size > 0)
                    canSendPendingAction = false;
            }
        }

        if (respBody.pendingAction == null && respBody.notifyMsgs == null)
            respBody.clientUpgrade = clientUpgradeFileService.getClientUpgrade(clientDevice)

        if (respBody.clientUpgrade != null) {
            canSendPendingAction = false;
        }

        if (canSendPendingAction) {
            if (clientDevice.id != null && clientDevice.user?.username != params.username && clientDevice.pendingActions.isEmpty()) {
                logger.info("username not equals, do reset login: ${clientDevice}, client=${params.username}, srv=${clientDevice.user?.username}")
                clientDevice.pendingActions = mutableListOf(ClientDevice.Action.ResetLogin.name).toTypedArray()
            }
            val firstPendingAct = clientDevice.pendingActions.firstOrNull()
            if (firstPendingAct != null) {
                if (firstPendingAct == ClientDevice.Action.ResetLogin.name) {
                    clientDevice.user = null;
                    clientDevice.org = orgService.getSysOrg(OrgService.SYS_ORG_KEY_UNKNOWN_CLIENT)
                }
                clientDevice.pendingActions = clientDevice.pendingActions.filter { it != firstPendingAct }.toTypedArray()
                respBody.pendingAction = firstPendingAct.toString()
                logger.info("${clientDevice} down pending action ${firstPendingAct}")
            }
        }

        if (clientDevice.status == ClientDevice.Status.Expired) {
            if (respBody.pendingAction != null)
                respBody.pendingAction = ClientDevice.Action.ResetLogin.toString();
        }

        if (respBody.pendingAction == ClientDevice.Action.Uninstall.toString()) {
            clientDevice.status = ClientDevice.Status.Expired;
            clientDeviceService.saveDelay(clientDevice, false);
        } else
            clientDeviceService.saveDelay(clientDevice, true);
        respBody.username = clientDevice.user?.username
        respBody.nickname = clientDevice.user?.nickname
        respBody.orgPath = clientDevice.org?.path
        return respBody
    }
}
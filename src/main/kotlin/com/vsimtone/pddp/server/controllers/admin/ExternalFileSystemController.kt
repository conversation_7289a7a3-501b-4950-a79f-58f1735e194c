package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.ExternalFileSystem
import com.vsimtone.pddp.server.entities.QExternalFileSystem
import com.vsimtone.pddp.server.services.ExternalFileSystemService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController("AdminExternalFileSystemController")
@RequestMapping("external_file_systems")
class ExternalFileSystemController : BaseHttpController() {

    @Autowired
    lateinit var service: ExternalFileSystemService

    @RequestMapping("")
    @Secured(Permissions.DATA_CLASSIFY_VIEW)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("name", required = false) name: String?,
        @RequestParam("type", required = false) type: ExternalFileSystem.Type?
    ): RestResp {
        val a = QExternalFileSystem.a
        val queries = BooleanBuilder()
        queries.and(currUserMgrOrgQuery(a.belongOrg))
        if (search != null) {
        }
        if (name != null) {
            queries.and(a.name.like("%${name}%"))
        }
        if (type != null) {
            queries.and(a.type.eq(type))
        }
        return jsonOut(service.findAll(queries, pageAttr));
    }

    @RequestMapping("{id:\\d+}")
    @Secured(Permissions.DATA_CLASSIFY_VIEW)
    fun view(@PathVariable("id") id: Long): RestResp {
        return jsonOut(service.findById(id));
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.DATA_CLASSIFY_EDIT)
    fun delete(@PathVariable("id") id: Long): RestResp {
        val data = service.findById(id);
        try {
            service.delete(data);
        } catch (_: Exception) {
            return this.jsonOut(1, "存在引用，无法删除");
        }
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.DATA_CLASSIFY_EDIT)
    @Transactional
    fun edit(@ModelAttribute("form") form: ExternalFileSystem): RestResp {
        var data: ExternalFileSystem;
        if (form.id != null) {
            data = service.findById(form.id!!);
        } else {
            data = ExternalFileSystem();
        }
        if (form.belongOrg == null)
            return jsonOut(1, "请选择管理机构")
        currUserMgrOrgRequire(form.belongOrg!!)
        try {
            if (data.id != null && StringUtils.isEmpty(form.password))
                form.password = data.password;
            this.service.checkConfig(form);
        } catch (e: Exception) {
            logger.error("${form} check error", e);
            var t: Throwable = e;
            if (t !is RestException)
                while (t.cause != null)
                    t = t.cause!!;
            return this.jsonOut(1, "错误：${t.message}")
        }
        if (!form.rootPath!!.startsWith("/"))
            form.rootPath = "/" + form.rootPath
        if (!form.rootPath!!.endsWith("/"))
            form.rootPath = form.rootPath + "/"
        form.rootPath = form.rootPath!!.replace(Regex("\\\\"), "/")
        data.belongOrg = form.belongOrg
        data.type = form.type
        data.name = form.name
        data.host = form.host
        data.port = form.port
        data.username = form.username
        if (!StringUtils.isEmpty(form.password))
            data.password = form.password
        data.rootPath = form.rootPath
        service.save(data);
        return jsonOut(data);
    }


}

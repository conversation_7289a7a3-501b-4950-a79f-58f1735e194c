package com.vsimtone.pddp.server.controllers.edge_node.msg_handlers

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.CryptUtils
import com.vsimtone.pddp.server.utils.Log
import okhttp3.internal.closeQuietly
import org.apache.commons.io.FilenameUtils
import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.io.Serializable
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import jakarta.annotation.PostConstruct
import kotlin.jvm.java

@Component
class EdgeNodeFileMsgHandler {

    protected open var logger = Log[this.javaClass];

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var fileService: FileService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService


    companion object {
        const val NAME_ACCESS_CHECK = "edge_node_file_opt"


        class EdgeNodeFileOptArgs : Serializable {
            lateinit var fileToken: String
            lateinit var authToken: String
        }

        class EdgeNodeFileOptResp : Serializable {

        }
    }

    @PostConstruct
    fun init() {
        edgeNodeService.addMsgHandler(NAME_ACCESS_CHECK, object : EdgeNodeService.Companion.WSMsgHandler<EdgeNodeFileOptArgs>() {
            init {
                argsType = EdgeNodeFileOptArgs::class.java
                handler = { a, b, c -> onFileAccessCheck(a, b, c) }
            }
        })
    }

    fun onFileAccessCheck(args: EdgeNodeFileOptArgs, msg: EdgeNodeService.Companion.WSMsg, sess: EdgeNodeService.WSSession): EdgeNodeFileOptResp {
        var f = fileService.findByToken(args.token) ?: return "token not exists."
        if (f.accessPermType == FileInfo.AccessPermType.Private) {
            if (f.secretKey != args.secretKey)
                return "secret key miss."
        }
        var resp = EdgeNodeFileOptResp()
        return resp;
    }
}

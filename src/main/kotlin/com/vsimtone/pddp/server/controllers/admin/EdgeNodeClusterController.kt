package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.RestUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.util.UUID
import kotlin.io.path.name

@RestController
@RequestMapping("edge_node_clusters")
class EdgeNodeClusterController : BaseController() {

    @Autowired
    lateinit var edgeNodeClusterService: EdgeNodeClusterService

    @Autowired
    lateinit var fileService: FileService

    @Autowired
    lateinit var certKeyPairService: CertKeyPairService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var edgeNodeRouteService: EdgeNodeRouteService

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("")
    @JsonView(JsonViews.SpecList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("name", required = false) name: String?,
    ): RestResp {
        val queries = BooleanBuilder();
        val a = QEdgeNodeCluster.a;
        if (name != null)
            queries.and(a.name.like("%${name}%"))
        val p = edgeNodeClusterService.findAll(queries, pageAttr)
        return jsonOut(p);
    }

    @Secured(Permissions.PROXY_NODE_EDIT)
    @PostMapping("")
    @Transactional
    fun edit(@ModelAttribute("form") form: EdgeNodeCluster): RestResp {
        var cluster: EdgeNodeCluster
        if (form.id != null) {
            cluster = edgeNodeClusterService.findById(form.id!!)
        } else {
            cluster = EdgeNodeCluster()
            cluster.configVersion = 0
        }
        cluster.configVersion += 1
        cluster.name = form.name
        cluster.useCustomConfig = form.useCustomConfig
        cluster.onlyAllowedNetworks = form.onlyAllowedNetworks
        cluster.enabled = form.enabled
        cluster.belongOrg = form.belongOrg

        if (form.useCustomConfig) {
            cluster.config = form.config
        }

        edgeNodeClusterService.save(cluster);
        return jsonOut(cluster.id)
    }

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.SpecView::class)
    fun view(@PathVariable("id") id: Long): RestResp {
        val cluster = edgeNodeClusterService.findById(id)
        return jsonOut(cluster)
    }

    @Secured(Permissions.PROXY_NODE_EDIT)
    @RequestMapping("{id:\\d+}/delete")
    @JsonView(JsonViews.SpecView::class)
    fun delete(@PathVariable("id") id: Long): RestResp {
        val cluster = edgeNodeClusterService.findById(id)
        edgeNodeClusterService.delete(cluster)
        return jsonOut(id)
    }

}
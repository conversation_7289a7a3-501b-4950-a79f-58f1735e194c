package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.EdgeNodeLog
import com.vsimtone.pddp.server.entities.QEdgeNodeLog
import com.vsimtone.pddp.server.services.EdgeNodeLogService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController("EdgeNodeLogController")
@RequestMapping("edge_node_logs")
class EdgeNodeLogController : BaseController() {

    @Autowired
    lateinit var service: EdgeNodeLogService

    @RequestMapping("")
    @Secured(Permissions.PROXY_NODE_VIEW)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?
    ): RestResp {
        val a = QEdgeNodeLog.a
        val queries = BooleanBuilder()
        if (search != null) {
        }
        return jsonOut(service.findAll(queries, pageAttr));
    }

    @RequestMapping("{id:\\d+}")
    @Secured(Permissions.PROXY_NODE_VIEW)
    fun view(@PathVariable("id") id: Long): RestResp {
        return jsonOut(service.findById(id));
    }

}

package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.repositories.UserFolderRepository
import com.vsimtone.pddp.server.services.FileService
import com.vsimtone.pddp.server.services.UserFileService
import com.vsimtone.pddp.server.services.UserFolderService
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RequestMapping("user_file")
@RestController
class UserFileController : BaseHttpController() {


    @Resource
    lateinit var userFileService: UserFileService

    @Resource
    lateinit var userFolderService: UserFolderService


    @Secured(Permissions.USER_FILE_VIEW)
    @RequestMapping("list")
    @JsonView(JsonViews.SpecList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("folderId", required = true) folderId: Long,
        @RequestParam("fileName", required = false) fileName: String?
    ): RestResp {
        val user:User = currUser()
        val queries = BooleanBuilder();
        val a = QUserFile.a;
        queries.and(a.owner.eq(user))
        if (StringUtils.isNotEmpty( fileName))
            queries.and(a.fileName.like("%${fileName}%"))
        val folder: UserFolder = userFolderService.findById(folderId)
        queries.and(a.folder.eq(folder))
        return jsonOut(userFileService.findAll(queries, pageAttr));
    }



    // 上传或修改文件
    @Secured(Permissions.USER_FILE_EDIT)
    @PostMapping("")
    @Transactional
    fun edit(@ModelAttribute("form") form: UserFile): RestResp {

        val user: User = currUser()

        if (StringUtils.isEmpty(form.fileName)) {
            return jsonOut(0, "请输入文件名称");
        }
        var userFile: UserFile? = null
        if (form.id != null) {
            userFile = userFileService.findById(form.id!!)
            if (userFile.owner.id != user.id) {
                return jsonOut(0, "没有权限")
            }

        } else {
            userFile  = UserFile()
            userFile.owner = user
            userFile.folder =form.folder
        }
        userFile.fileName = form.fileName
        // 根据文件名后缀推断文件类型，如果没有则为空
        userFile.fileType = userFileService.getFileType(form.fileName)
        userFile.fileSize  = form.fileSize
        userFile.fileB3Sum  = form.fileB3Sum
        userFile.fileToken  = form.fileToken
        userFileService.save(userFile)
        return jsonOut(userFile);
    }


    @Secured(Permissions.USER_FILE_EDIT)
    @DeleteMapping("")
    @Transactional
    fun delete(@RequestParam("id",  required = true) id: Long): RestResp {
        val userFile: UserFile = userFileService.findById(id)
        val user: User = currUser()
        if (userFile.owner.id != user.id) {
            return jsonOut(0, "没有权限")
        }
        userFileService.delete(userFile)
        return jsonOut(id)
    }



}
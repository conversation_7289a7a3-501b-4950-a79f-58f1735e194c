package com.vsimtone.pddp.server.controllers.admin

import com.vsimtone.pddp.server.services.FileService
import jakarta.servlet.http.HttpServletRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseBody
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@RequestMapping("file_infos")
@RestController
class FileController : BaseHttpController() {


    @Autowired
    lateinit var fileService: FileService

//    @Secured(Permissions.FILE_VIEW)
//    @RequestMapping("")
//    fun list(
//        @ModelAttribute("page") pageAttr: PageAttr,
//        @RequestParam("search", required = false) search: String?
//    ): RestResp {
//        val queries = BooleanBuilder();
//        if (!StringUtils.isEmpty(search))
//            queries.and(
//                QFileInfo.a.name.like("%$search%").or(QFileInfo.a.type.like("%$search%")).or(QFileInfo.a.sha256sum.like("%$search%"))
//            );
//        return jsonOut(fileService.findAll(queries, pageAttr));
//    }

    @RequestMapping("upload", method = [RequestMethod.POST])
    @ResponseBody
    @Transactional
    fun upload(
        req: HttpServletRequest,
        @RequestParam("file") file: MultipartFile,
        @RequestParam("folder", required = false) folder: Long?,
        @RequestParam("wangEditor", required = false) wangEditor: String?
    ): Any? {
        try {
            var f = fileService.upload(file)
//            if (wangEditor != null)
//                return mapOf<String, Any>("errno" to 0L, "data" to arrayListOf("/api/files/" + f.path))
//            addOptLog(OptLog.Type.Other, "文件管理——" + f.name, mapOf("msg" to ("上传文件，数据ID为：" + f.id)));
            return jsonOut(f)
        } catch (e: Exception) {
            e.printStackTrace()
            return null;
        }
    }


//    @RequestMapping("edit", method = [RequestMethod.POST])
//    @ResponseBody
//    @Transactional
//    @Secured(Permissions.FILE_VIEW)
//    fun edit(
//        @RequestParam("id") id: Long,
//        @RequestParam("name", required = false) name: String?,
//        @RequestParam("folder", required = false) folder: Long?,
//    ) {
//        var f = fileService.findById(id);
//        if (!StringUtils.isEmpty(name))
//            f.name = name!!;
//        if (folder != null) {
//            f.folder = FileFolder()
//            f.folder!!.id = folder
//        }
//        addOptLog(OptLog.Type.Modify,"文件管理——"+f.name, mapOf("msg" to ("修改数据，ID为："+f.id)));
//        fileService.save(f);
//    }


//    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
//    @Secured(Permissions.FILE_VIEW)
//    fun delete(@PathVariable("id") id: Long): RestResp {
//        val f = fileService.findById(id);
//        try {
//            fileService.delete(f);
//        } catch (e: java.lang.Exception) {
//            return jsonOut(1, "禁止删除该文件");
//        }
//        addOptLog(OptLog.Type.Delete,"文件管理——"+f.name, mapOf("msg" to ("已删除，ID为："+f.id)));
//        return jsonOut(true);
//    }

//    @RequestMapping("", method = [RequestMethod.POST])
//    @Transactional
//    fun modify(
//        @ModelAttribute("form") form: FileInfo
//    ): RestResp {
//        var f = fileService.findById(form.id!!);
//        fileService.save(f);
//        return jsonOut(f);
//    }


}
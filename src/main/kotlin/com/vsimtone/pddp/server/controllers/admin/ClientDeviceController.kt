package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.AppUtils
import com.vsimtone.pddp.server.utils.RestUtil
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController
@RequestMapping("client_devices")
class ClientDeviceController : BaseHttpController() {

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var fileService: FileService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var edgeNodeClusterService: EdgeNodeClusterService

    @Autowired
    lateinit var cacheInvokeService: CacheInvokeService

    @Autowired
    lateinit var clientUpgradeRecordService: ClientUpgradeRecordService

    @Autowired
    lateinit var clientUpgradeFileService: ClientUpgradeFileService

    @Autowired
    lateinit var clientNotifyMsgService: ClientNotifyMsgService;

    @Autowired
    lateinit var edgeNodeRouteService: EdgeNodeRouteService;


    @Secured(Permissions.CLIENT_VIEW)
    @RequestMapping("")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("hostname", required = false) hostname: String?,
        @RequestParam("username", required = false) username: String?,
        @RequestParam("org_path", required = false) orgPath: String?,
        @RequestParam("status", required = false) status: Array<ClientDevice.Status>?,
        @RequestParam("queryCache", required = false) queryCache: Boolean?,
        @RequestParam("fromIP", required = false) fromIP: String?,
        @RequestParam("healthScore", required = false) healthScore: Long?,
        @RequestParam("systemType", required = false) systemType: ClientDevice.SysType?,
        @RequestParam("systemVersion", required = false) systemVersion: String?,
        @RequestParam("localIPMAC", required = false) localIPMAC: String?
    ): RestResp {
        val queries = BooleanBuilder();
        queries.and(currUserMgrOrgQuery(QClientDevice.a.org))
        if (!StringUtils.isEmpty(hostname?.trim())) queries.and(
            QClientDevice.a.hostname.like("%$hostname%")
        );
        if (!StringUtils.isEmpty(username?.trim())) {
            queries.and(QClientDevice.a.user.username.like("%$username%"))
        }
        if (status != null && status.isNotEmpty()) queries.and(QClientDevice.a.status.`in`(status.toList()));
        else queries.and(QClientDevice.a.status.`ne`(ClientDevice.Status.Expired));
        if (!StringUtils.isEmpty(orgPath?.trim())) queries.and(QClientDevice.a.org.path.like("$orgPath%"));

        if (!StringUtils.isEmpty(fromIP?.trim())) queries.and(QClientDevice.a.fromIP.like("%$fromIP%"));

        if (healthScore != null) queries.and(QClientDevice.a.healthScore.lt(healthScore));

        if (systemType != null) queries.and(QClientDevice.a.systemType.eq(systemType))

        if (!StringUtils.isEmpty(systemVersion?.trim())) queries.and(QClientDevice.a.systemVersion.eq(systemVersion))

        if (!StringUtils.isEmpty(localIPMAC?.trim())) queries.and(QClientDevice.a.localIPMAC.eq(localIPMAC))

        var p: Page<ClientDevice> = clientDeviceService.findAll(queries, pageAttr);
        if (queryCache != null && queryCache == true) {
            p = p.map {
                val d = clientDeviceService.findByDeviceIdInCache(it.deviceId)!!
                d.org = orgService.findById(d.org!!.id!!)
                d
            }
        } else {
            p.content.forEach {
                RestUtil.unwrapBean(ClientDevice::user, it)
                RestUtil.unwrapBean(ClientDevice::org, it)
                if (it.edgeNodeClusterId != null) it.putBundle("edgeNodeCluster", edgeNodeClusterService.findById(it.edgeNodeClusterId!!))
            }
        }
        p.content.forEach {
            if (it.edgeNodeClusterId != null) it.putBundle("edgeNodeCluster", edgeNodeClusterService.findById(it.edgeNodeClusterId!!))
        }
        return jsonOut(p);
    }

    @RequestMapping("{id:\\d+}")
    @Secured(Permissions.CLIENT_VIEW)
    fun view(@PathVariable("id") id: Long): RestResp {
        val data = clientDeviceService.findById(id)
        currUserMgrOrgRequire(data.org!!)
        if (data.edgeNodeClusterId != null) data.putBundle("edgeNodeCluster", edgeNodeClusterService.findById(data.edgeNodeClusterId!!))
        data.putBundle("histories", clientDeviceService.clientDeviceHistoryRepository.findAll(QClientDeviceHistory.a.clientDevice.eq(data)).sortedByDescending { it.createdAt!!.time })
        if (data.fsList != null) data.putBundle("fsList", clientDeviceService.parseFSList(data.fsList!!))
        RestUtil.unwrapBean(ClientDevice::user, data)
        RestUtil.unwrapBean(ClientDevice::org, data)
        return jsonOut(data);
    }


    @RequestMapping("set_action", method = [RequestMethod.POST])
    @Secured(Permissions.CLIENT_EDIT)
    @Transactional
    fun setAction(@RequestParam("id") ids: Array<Long>, @RequestParam("action") action: String, @RequestParam("add") add: Boolean): RestResp {
        ClientDevice.Action.valueOf(action.split(":")[0])
        ids.forEach {
            val device = clientDeviceService.findById(it);
            currUserMgrOrgRequire(device.org!!)
            val pendingActions = device.pendingActions.toMutableList()
            if (!add) {
                pendingActions.remove(action)
            } else if (!pendingActions.contains(action)) {
                pendingActions.add(action)
            }
            device.pendingActions = pendingActions.toTypedArray()
            clientDeviceService.save(device);

            var handleInfo = clientDeviceService.actionToMsgInfo(action, add);
//        日志上传、取消上传日志去掉操作日志记录
            if (!action.equals(ClientDevice.Action.UploadLog.name)) addOptLog(handleInfo, "终端管理", device.hostname, device);
        }
        return jsonOut(true);
    }

    @RequestMapping("set_remark", method = [RequestMethod.POST])
    @Secured(Permissions.CLIENT_EDIT)
    @Transactional
    fun setRemark(@RequestParam("id") ids: Array<Long>, @RequestParam("remark") remark: String): RestResp {
        var devices = mutableListOf<ClientDevice>();
        ids.forEach {
            val device = clientDeviceService.findById(it);
            currUserMgrOrgRequire(device.org!!)
            if (device.remark != remark) {
                device.remark = remark;
                clientDeviceService.save(device);
                devices.add(device);
            }
        }
        addOptLog("批量备注", "终端管理", "${devices.size}条,备注:${remark}", devices);
        return jsonOut(true);
    }

    @RequestMapping("delete", method = [RequestMethod.POST])
    @Secured(Permissions.CLIENT_EDIT)
    @Transactional
    fun delete(@RequestParam("id") ids: Array<Long>): RestResp {
        var deletedDevices = mutableListOf<ClientDevice>();
        ids.forEach { id ->
            val device = clientDeviceService.findById(id);
            currUserMgrOrgRequire(device.org!!)
            clientNotifyMsgService.findAll(QClientNotifyMsg.a.device.eq(device)).forEach {
                clientNotifyMsgService.delete(it)
            }
            clientDeviceService.clientDeviceHistoryRepository.findAll(QClientDeviceHistory.a.clientDevice.eq(device)).forEach {
                clientDeviceService.clientDeviceHistoryRepository.delete(it);
            }
            if (device.status == ClientDevice.Status.Offline || device.status == ClientDevice.Status.Expired) {
                clientUpgradeRecordService.findAll(QClientUpgradeRecord.a.clientDevice.eq(device)).forEach {
                    clientUpgradeRecordService.delete(it)
                }
                clientDeviceService.delete(device);
            }
            deletedDevices.add(device)
        }
        addOptLog(OptLog.BATCH_DELETE, "终端管理", "${deletedDevices.size}条", deletedDevices);
        return jsonOut(true);
    }

//    @RequestMapping("{id:\\d+}/get_upload_logs", method = [RequestMethod.GET])
//    @Secured(Permissions.CLIENT_EDIT)
//    fun getUploadLogs(@PathVariable("id") id: Long): RestResp {
//        val device = clientDeviceService.findByIdInCache(id)!!;
//        currUserMgrOrgRequire(device.org!!)
//        val files = fileService.findAll(
//            QFileInfo.a.refs.any().type.eq(FileRef.Companion.Type.ClientUpLog).and(
//                QFileInfo.a.refs.any().clientId.eq(id)
//            )
//        ).map {
//            val flags = fileService.getFlags(it)
//            it.putBundle("expiredAt", flags.expiredAt)
//            it
//        }.filter {
//            val expiredAt: Date? = it.getBundle("expiredAt") as Date?
//            expiredAt == null || expiredAt!!.time > System.currentTimeMillis()
//        }
//        return jsonOut(files);
//    }

//    @RequestMapping("{id:\\d+}/log_file_crypt_down_data", method = [RequestMethod.GET])
//    @Secured(Permissions.CLIENT_EDIT)
//    fun logFileCryptDownData(@PathVariable("id") id: Long, @RequestParam("fid") fid: Long): RestResp {
//        val device = clientDeviceService.findByIdInCache(id)!!;
//        currUserMgrOrgRequire(device.org!!)
//        val f = fileService.findByIdIf(fid) ?: return jsonOut(1, "File id not found")
//        return jsonOut(edgeNodeService.getCryptDownData(f.edgeNode!!, f));
//    }

//    @RequestMapping("latest_client_packages")
//    fun latestClientPackages(@RequestParam("all", required = false) all: Boolean = false): RestResp {
//        val clients = mutableListOf<Map<String, Any>>()
//        ClientDevice.SysType.values().forEach { type ->
//            ClientDevice.SysArch.values().forEach { arch ->
//                val it = this.clientUpgradeFileService.findAll(
//                    QClientUpgradeFile.a.upgradeFileReady.isTrue.and(QClientUpgradeFile.a.sysType.eq(type)).and(QClientUpgradeFile.a.sysArch.eq(arch)).and(QClientUpgradeFile.a.anonymousDownloadable.isTrue), PageAttr(1, if (all) 100 else 1, arrayListOf("desc_createdAt"))
//                ).firstOrNull() ?: return@forEach
//                clients.add(
//                    mapOf(
//                        "id" to it.id!!.toString(), "clientVersion" to it.clientVersion, "sysArch" to it.sysArch, "sysType" to it.sysType, "name" to it.upgradeFile.name, "size" to it.upgradeFile.size, "sha256" to it.upgradeFile.sha256sum
//                    )
//                )
//            }
//        }
//        return jsonOut(clients);
//    }

//    @RequestMapping("client_package_crypt_down_data")
//    fun clientPackageCryptDownload(@RequestParam("id") id: Long): Any? {
//        val clients = mutableListOf<ClientUpgradeFile>()
//        ClientDevice.SysType.entries.forEach { type ->
//            ClientDevice.SysArch.entries.forEach { arch ->
//                val it = this.clientUpgradeFileService.findAll(
//                    QClientUpgradeFile.a.upgradeFileReady.isTrue.and(QClientUpgradeFile.a.sysType.eq(type)).and(QClientUpgradeFile.a.sysArch.eq(arch)).and(QClientUpgradeFile.a.anonymousDownloadable.isTrue), PageAttr(1, 100, arrayListOf("desc_createdAt"))
//                ).firstOrNull() ?: return@forEach
//                clients.add(it)
//            }
//        }
//        val upg = this.clientUpgradeFileService.findById(id)
//        if (!upg.upgradeFileReady || clients.find { it.id == id } == null) return this.jsonOut(1, "数据错误,请刷新重试！");
//        val file = upg.upgradeFile;
//        val fromIP = AppUtils.getClientIpAddress(this.getRequest());
//        val edgeNodeRouteHit = this.edgeNodeRouteService.route(arrayOf(fromIP)) ?: return this.jsonOut(1, "无匹配路由,来自IP:${fromIP}");
//        val edgeNode = edgeNodeRouteHit.edgeNodes.map { this.edgeNodeService.findByIdIf(it) }.filterNotNull().randomOrNull() ?: return this.jsonOut(1, "无匹配路由,来自IP:${fromIP}");
//        val f = fileService.findByToken(file.token) ?: return jsonOut(1, "File token not found")
//        return this.jsonOut(edgeNodeService.getCryptDownData(edgeNode, f))
//    }

    @RequestMapping("system_versions")
    @Secured(Permissions.CLIENT_VIEW)
    fun systemVersions(): RestResp {
        val data = cacheInvokeService.cacheInvoke("client_device_system_versions", true) {
            val a = QClientDevice.a
            return@cacheInvoke clientDeviceService.distinctQuery(a.systemVersion, a.status.ne(ClientDevice.Status.Expired));
        }
        return RestResp(data);
    }

    @RequestMapping("system_kernels")
    @Secured(Permissions.CLIENT_VIEW)
    fun systemKernels(): RestResp {
        val data = cacheInvokeService.cacheInvoke("client_device_system_kernels", true) {
            val a = QClientDevice.a
            return@cacheInvoke clientDeviceService.distinctQuery(a.systemKernel, a.status.ne(ClientDevice.Status.Expired));
        }
        return RestResp(data);
    }

    @RequestMapping("versions")
    @Secured(Permissions.CLIENT_VIEW)
    fun versions(): RestResp {
        val data = cacheInvokeService.cacheInvoke("client_device_versions", true) {
            val a = QClientDevice.a
            return@cacheInvoke clientDeviceService.distinctQuery(a.version, a.status.ne(ClientDevice.Status.Expired));
        }
        return RestResp(data);
    }

}

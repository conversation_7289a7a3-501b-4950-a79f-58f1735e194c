package com.vsimtone.pddp.server.controllers.client

import com.vsimtone.pddp.server.controllers.admin.BaseHttpController
import com.vsimtone.pddp.server.services.EdgeNodeClusterService
import com.vsimtone.pddp.server.services.EdgeNodeRouteService
import com.vsimtone.pddp.server.services.EdgeNodeService
import com.vsimtone.pddp.server.services.RedisService
import com.vsimtone.pddp.server.utils.AppUtils
import com.vsimtone.pddp.server.utils.CryptUtils
import org.apache.commons.codec.binary.Hex
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import kotlin.to

@RequestMapping(path = arrayOf("/client_route"))
@RestController
class ClientRouteController : BaseHttpController() {

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var edgeNodeClusterService: EdgeNodeClusterService

    @Autowired
    lateinit var edgeNodeRouteService: EdgeNodeRouteService

    @Autowired
    lateinit var redisService: RedisService

    companion object {}

    @RequestMapping(method = arrayOf(RequestMethod.GET))
    fun route(
        req: HttpServletRequest,
        resp: HttpServletResponse,
        @RequestParam("uuid", required = false) uuid: String?,
        @RequestParam("user", required = false) user: String?,
        @RequestParam("hostname", required = false) hostname: String?,
        @RequestParam("os", required = false) os: String?,
        @RequestParam("random", required = false) random: Boolean?
    ): Any {
        val ip = AppUtils.getClientIpAddress(req)
        var hit = edgeNodeRouteService.route(arrayOf(ip))
        if (hit == null || hit.edgeNodeClusters.isEmpty()) {
            this.edgeNodeRouteService.missingRoutes[ip] = EdgeNodeRouteService.Companion.MissingRouteItem(System.currentTimeMillis(), uuid, user, hostname)
            this.logger.warn("Client route miss match ${ip} ${user} ${hostname} ${os}")
            return mapOf("code" to 1, "msg" to "miss", "fromIP" to ip, "user" to user, "hostname" to hostname, "os" to os)
        }
        this.edgeNodeRouteService.missingRoutes.fastRemove(ip)
        if (random != null && random) {
            hit = edgeNodeRouteService.enabledRoutesCache.random().route
        }
        val cluster = edgeNodeClusterService.findById(hit.edgeNodeClusters.random())

        return mapOf(
            "code" to 0,
            "pubKey" to cluster.certKeyPair!!.publicKey,
            "pubKeyHash" to cluster.certKeyPair!!.publicKeyHash,
            "curlPinnedPubKey" to "sha256//" + CryptUtils.base64encode(Hex.decodeHex(cluster.certKeyPair!!.publicKeyHash)),
            "nodes" to edgeNodeService.findByCluster(cluster).map { it.httpsUrl }
        )
    }
}

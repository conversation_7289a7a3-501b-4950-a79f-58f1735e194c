package com.vsimtone.pddp.server.controllers.edge_node.msg_handlers

import com.vsimtone.pddp.server.beans.PerfMonStats
import com.vsimtone.pddp.server.configs.ServerConfig
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.DateUtil
import com.vsimtone.pddp.server.utils.Log
import jakarta.annotation.PostConstruct
import jakarta.websocket.CloseReason
import jakarta.websocket.CloseReason.CloseCodes
import org.apache.commons.compress.harmony.pack200.PackingUtils.config
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.io.Serializable
import java.util.*

@Component
class EdgeNodePingMsgHandler {

    @Autowired
    private lateinit var edgeNodeClusterService: EdgeNodeClusterService
    protected open var logger = Log[this.javaClass];

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var certKeyPairService: CertKeyPairService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var perfMonService: PerfMonService

    @Autowired
    lateinit var edgeNodeRouteService: EdgeNodeRouteService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var serverConfig: ServerConfig

    companion object {
        const val NAME = "edge_node_ping"

        class EdgeNodePingArgs : Serializable {
            var publicKey: String? = null
            var clusterPubKey: String? = null
            var clusterPubKeyHash: String? = null
            var nodeId: String? = null
            var nodeVersion: String? = null
            var nodeHost: String? = null
            var hostname: String? = null
            var ip: String? = null
            var configVersion: Long = 0
            var httpUrl: String? = null
            var httpsUrl: String? = null
            var perfMonStats: Map<String, PerfMonStats>? = null
            var perfMonDetails: List<PerfMonService.Companion.PerfMonDetail>? = null
            var perfMonInterIP: String? = null
            var memAlloc: Long = 0
            var sysMemTotal: Long = 0
            var sysCpuInfo: String? = null
            var nodeUptime: Long = 0
            var dataDiskUsed: Long = 0
            var dataDiskTotal: Long = 0
        }
    }

    @PostConstruct
    fun init() {
        edgeNodeService.addMsgHandler(NAME, object : EdgeNodeService.Companion.WSMsgHandler<EdgeNodePingArgs>() {
            init {
                argsType = EdgeNodePingArgs::class.java
                handler = { a, b, c -> onPing(a, b, c) }
            }
        })
    }

    fun onPing(args: EdgeNodePingArgs, msg: EdgeNodeService.Companion.WSMsg, sess: EdgeNodeService.WSSession): Map<String, Any> {
        try {
            if (args.nodeId == null || args.nodeId!!.isEmpty()) {
                throw RuntimeException("Node id is empty")
            }
            if (args.clusterPubKeyHash == null || args.clusterPubKeyHash!!.isEmpty()) {
                throw RuntimeException("Node clusterPublicKey is empty")
            }
            val result = mutableMapOf<String, Any>()
            var nodeHostChanged = false;
            var pingStarted = System.currentTimeMillis();
            val locked = redisService.runInLockAndTransaction("edge_node_cluster:${args.clusterPubKeyHash!!}", 10) {
                result["serverUrl"] = this.serverConfig.url!!;
                var edgeNode = edgeNodeService.findByNodeId(args.nodeId!!)
                var cluster = edgeNode?.cluster
                if (edgeNode == null) {
                    edgeNode = EdgeNode()
                    edgeNode.nodeId = args.nodeId!!
                    edgeNode.certKeyPair = certKeyPairService.newCertKey(args.publicKey!!, CertKeyPair.UsedBy.EdgeNode)
                    edgeNode.status = EdgeNode.Status.Online
                    certKeyPairService.repo.save(edgeNode.certKeyPair)
                }
                if (StringUtils.isNotEmpty(args.clusterPubKey)) {
                    if (edgeNode.cluster != null) {
                        if (edgeNode.cluster!!.certKeyPair!!.publicKey != args.clusterPubKey!!) {
                            throw RuntimeException("Cluster public key is incorrect")
                        }
                        if (args.nodeHost != edgeNode.nodeHost) {
                            nodeHostChanged = true;
                        }
                    } else {
                        cluster = edgeNodeClusterService.findOne(QEdgeNodeCluster.a.certKeyPair.publicKey.eq(args.clusterPubKey!!))
                        if (cluster != null) {
                            edgeNodeClusterService.nodeJoin(cluster, edgeNode, "auto_join")
                            nodeHostChanged = false
                        }
                    }
                    edgeNode.clusterPubkey = args.clusterPubKey!!
                    edgeNode.nodeVersion = args.nodeVersion!!
                    edgeNode.nodeHost = args.nodeHost!!
                    edgeNode.hostname = args.hostname!!
                    edgeNode.httpUrl = args.httpUrl!!
                    edgeNode.httpsUrl = args.httpsUrl!!
                }

                if (edgeNode.status == EdgeNode.Status.Offline) {
                    edgeNode.status = EdgeNode.Status.Online
                }

                edgeNode.ip = sess.fromIP
                edgeNode.pingTime = Date()
                edgeNode.perfMonInterIP = args.perfMonInterIP
                edgeNode.memAlloc = args.memAlloc
                edgeNode.sysMemTotal = args.sysMemTotal
                edgeNode.sysCpuInfo = args.sysCpuInfo
                edgeNode.nodeUptime = args.nodeUptime
                edgeNode.connectServerName = sysService.thisNodeInfo.hostName
                edgeNode.dataDiskUsed = args.dataDiskUsed
                edgeNode.dataDiskTotal = args.dataDiskTotal
                if (sess.edgeNode == null) {
                    edgeNode.connectTime = Date()
                }
                edgeNodeService.save(edgeNode)
                if (sess.edgeNode == null) {
                    sess.edgeNode = edgeNode
                    sess.checkSign(msg, msg.data!!)
                }
                edgeNode.certKeyPair._keyPair = certKeyPairService.toKeyPair(edgeNode.certKeyPair)
                sess.edgeNode = edgeNode;
                perfMonService.uploadPerfMon("edge_node_client:" + edgeNode.id!!.toString(), args.perfMonStats, args.perfMonDetails)
                if (msg.dataFormat!!.indexOf(EdgeNodeService.Companion.WSMsgDataFormat.CERT_CRYPT) == -1) {
                    result["serverPublicKey"] = certKeyPairService.serverCertKeyPair.publicKey
                }
                if (cluster == null || !cluster.enabled) {
                    return@runInLockAndTransaction
                }
                if (nodeHostChanged) {
                    edgeNodeClusterService.nodeUpdate(cluster, edgeNode, "node_host_change")
                }
                var globalConfig = edgeNodeService.getGlobalConfig()
                var config = cluster.config ?: globalConfig
                var configVersion = cluster.configVersion + globalConfig.version
                sess.rateLimitOutput.setSpeed(config.serverDownSpeedLimit)
                if (args.configVersion != configVersion) {
                    config.nodes = edgeNodeClusterService.getNodeUrls(cluster)
                    result["config"] = config
                    result["configVersion"] = configVersion
                    if (cluster.onlyAllowedNetworks)
                        result["allowedNetworks"] = edgeNodeRouteService.getNetworks(cluster.id!!)
                }
                if (cluster.onlyAllowedNetworks) {
                    result["allowedNetworks"] = edgeNodeRouteService.getNetworks(cluster.id!!)
                }
            }
            if (!locked)
                throw RuntimeException("${sess.edgeNode} ${args.hostname} ${args.ip} ping lock fail.")
            this.logger.info("${sess.edgeNode} ping succ. configVersion=${args.configVersion}, used=${DateUtil.useTimeToHum(System.currentTimeMillis() - pingStarted)}")
            return result
        } catch (e: Exception) {
            sess.writeError(msg, e.message ?: "ping error")
            sess.edgeNode = null
            sess.session.close(CloseReason(CloseCodes.UNEXPECTED_CONDITION, "Ping exception"))
            sess.closed = true;
            throw e;
        }
    }
}
package com.vsimtone.pddp.server.controllers.admin

import com.vsimtone.pddp.server.entities.FileInfo
import com.vsimtone.pddp.server.services.APPConfigService
import com.vsimtone.pddp.server.services.FileService
import com.vsimtone.pddp.server.services.EdgeNodeService
import io.micrometer.core.annotation.Timed
import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.web.server.MimeMappings
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.io.EOFException
import java.net.URLEncoder
import java.nio.file.attribute.FileTime
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import jakarta.servlet.http.HttpServletResponse

@RequestMapping("files")
@RestController
class FileDownloadController : BaseHttpController() {


    @Autowired
    lateinit var fileService: FileService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

//    @RequestMapping("zip")
//    @Timed("file_api_download_zip", longTask = true)
//    fun downloadZip(
//        @RequestParam("tokens") tokens: Array<String>,
//        resp: HttpServletResponse
//    ): Any? {
//        resp.setHeader("Cache-Control", "max-age=0")
//        resp.setHeader("Access-Control-Allow-Origin", "*")
//        resp.contentType = MimeMappings.DEFAULT.get("zip");
//        val encodeFilename =
//            URLEncoder.encode(tokens.size.toString() + "_" + System.currentTimeMillis() + ".zip", "UTF-8")
//        resp.setHeader(
//            "Content-Disposition",
//            "attachment; filename=\"\${$encodeFilename}\"; filename*=utf-8''$encodeFilename"
//        )
//        val bufLen = appConfigService.getLong(FileService.CNF_FILE_DOWNLOAD_SPEED)!!.toInt()
//        val entryList = mutableListOf<String>()
//        val z = ZipOutputStream(resp.outputStream)
//        val fileIds = mutableListOf<Long>()
//        z.use {
//            for (token in tokens)
//                try {
//                    val f = fileService.findByToken(token) ?: continue;
//                    fileIds.add(f.id!!)
//                    if (f.savedOn == FileInfo.SavedOn.EdgeNode) continue;
//                    val inputStream = fileService.getInputStream(f.path) ?: continue;
//                    inputStream.use {
//                        val time = FileTime.fromMillis(f.createdAt!!.time)
//                        val names = mutableListOf<String>()
//                        names.add(f.name)
//                        var name = names.joinToString("/")
//                        var i = 1;
//                        if (entryList.contains(name))
//                            while (true) {
//                                if (entryList.contains(name + ".${i}"))
//                                    i++;
//                                else {
//                                    name += ".${i}"
//                                    break
//                                }
//                            }
//                        entryList.add(name);
//                        z.putNextEntry(
//                            ZipEntry(name).setCreationTime(time).setLastAccessTime(time).setLastModifiedTime(time)
//                        )
//                        val buf = ByteArray(bufLen)
//                        while (true) {
//                            val t = System.currentTimeMillis()
//                            val len = IOUtils.read(inputStream, buf)
//                            if (len <= 0) break
//                            z.write(buf, 0, len)
//                            z.flush()
//                            val st = System.currentTimeMillis() - t
//                            if (st < 1000)
//                                Thread.sleep(1000 - st)
//                        }
//                        z.closeEntry();
//                    }
//
////                    addOptLog(OptLog.Type.Other, "文件管理*下载zip文件", mapOf("msg" to ("被下载的文件有：" + fileIds.joinToString("，"))));
//                } catch (e: Exception) {
//                    if (e !is EOFException)
//                        e.printStackTrace()
//                    resp.sendError(500, "Zip File error");
//                }
//        }
//        return null;
//    }
//
//
//    @RequestMapping("{token:^[\\w\\.]+$}")
//    @Timed("file_api_download", longTask = true)
//    fun download(
//        @PathVariable("token") token: String,
//        resp: HttpServletResponse,
//        @RequestParam("v", required = false) v: Boolean?
//    ): Any? {
//        resp.setHeader("Cache-Control", "max-age=864000")
//        resp.setHeader("Access-Control-Allow-Origin", "*")
//        try {
//            var f = fileService.findByToken(token) ?: return jsonOut(404, "file not found");
//            if (f.savedOn == FileInfo.SavedOn.EdgeNode) {
//                return jsonOut(403, "file saved on edge_node");
//            }
//            var inputStream = fileService.getInputStream(f.path) ?: return jsonOut(500, "file read fail");
//            inputStream.use {
//                resp.contentType = f.type;
//                if (v == null || v == false) {
//                    val encodeFilename = URLEncoder.encode(f.name, "UTF-8")
//                    resp.setHeader(
//                        "Content-Disposition",
//                        "attachment; filename=\"\${$encodeFilename}\"; filename*=utf-8''$encodeFilename"
//                    )
//                }
//                if (inputStream.available() != 0)
//                    resp.setContentLength(inputStream.available())
//                else if (f.size != 0L) {
//                    resp.setContentLength(f.size.toInt())
//                }
//                var ros = resp.outputStream
//                var bufLen = appConfigService.getLong(FileService.CNF_FILE_DOWNLOAD_SPEED)!!.toInt()
//                var buf = ByteArray(bufLen)
//                while (true) {
//                    var t = System.currentTimeMillis()
//                    var len = IOUtils.read(it, buf)
//                    if (len <= 0) break
//                    ros.write(buf, 0, len)
//                    ros.flush()
//                    var st = System.currentTimeMillis() - t
//                    if (st < 1000)
//                        Thread.sleep(1000 - st)
//                }
//            }
////            addOptLog(OptLog.Type.Other, "文件管理——" + f.name, mapOf("msg" to ("文件被下载,ID为：" + f.id)));
//        } catch (e: Exception) {
//            if (!(e is EOFException))
//                e.printStackTrace()
//            resp.sendError(500, "File error");
//        }
//        return null;
//    }

}
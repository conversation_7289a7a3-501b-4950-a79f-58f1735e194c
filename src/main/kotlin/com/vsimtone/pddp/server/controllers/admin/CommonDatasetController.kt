package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.CommonDataset
import com.vsimtone.pddp.server.entities.OptLog
import com.vsimtone.pddp.server.entities.QCommonDataset
import com.vsimtone.pddp.server.services.CommonDatasetService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController("adminCommonDatasetController")
@RequestMapping("common_datasets")
class CommonDatasetController : BaseHttpController() {

    @Autowired
    lateinit var service: CommonDatasetService

    @RequestMapping("")
    @Secured(Permissions.COMMON_DATASET_VIEW)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("type", required = false) type: CommonDataset.Type?,
        @RequestParam("name", required = false) name: String?
    ): RestResp {
        val a = QCommonDataset.a
        val queries = BooleanBuilder()
        if (search != null) {
        }
        if (type != null)
            queries.and(a.type.eq(type))
        if (name != null)
            queries.and(a.name.like("%${name}%"))
        queries.and(a.disabled.isFalse)
        return jsonOut(service.findAll(queries, pageAttr));
    }

    @RequestMapping("{id:\\d+}")
    @Secured(Permissions.COMMON_DATASET_VIEW)
    fun view(@PathVariable("id") id: Long): RestResp {
        val data = service.findById(id);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.COMMON_DATASET_EDIT)
    fun delete(@PathVariable("id") id: Long): RestResp {
        val data = service.findById(id)
        service.delete(data)
        addOptLog(OptLog.DELETE, "通用数据集", data.name, data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.COMMON_DATASET_EDIT)
    @Transactional
    fun edit(@ModelAttribute("form") form: CommonDataset): RestResp {
        var data: CommonDataset;
        if (form.id != null) {
            data = service.findById(form.id!!);
            if (data.code != null)
                return jsonOut(1, "内置数据集,不允许修改")
        } else {
            data = CommonDataset();
            data.type = form.type
        }
        if (form.name.length > 25) return jsonOut(1, "名称最多25个字符")
        if (form.desc.length > 200) return jsonOut(1, "描述最多200个字符")
        if (form.values.sumOf { it.length } >= 2000) return jsonOut(1, "值最多2000个字符")
        data.name = form.name
        data.desc = form.desc
        data.values = form.values
        service.save(data);
        if (form.id != null)
            addOptLog(OptLog.ADD, "通用数据集", data.name, data);
        else
            addOptLog(OptLog.EDIT, "通用数据集", data.name, data);
        return jsonOut(data);
    }


}

package com.vsimtone.pddp.server.controllers.edge_node.msg_handlers

import com.vsimtone.pddp.server.services.APPConfigService
import com.vsimtone.pddp.server.services.CertKeyPairService
import com.vsimtone.pddp.server.services.EdgeNodeService
import com.vsimtone.pddp.server.services.RedisService
import com.vsimtone.pddp.server.utils.DateUtil
import com.vsimtone.pddp.server.utils.JSONUtils
import com.vsimtone.pddp.server.utils.Log
import jakarta.annotation.PostConstruct
import org.apache.tomcat.util.threads.ThreadPoolExecutor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.io.Serializable
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

@Component
class ClientActionMsgHandler {

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var certKeyPairService: CertKeyPairService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var appConfigService: APPConfigService

    var logger = Log.get(this.javaClass)

    lateinit var threadPoolExecutor: ThreadPoolExecutor

    var actionHandlers = mutableMapOf<String, (request: ClientActionRequest, sess: EdgeNodeService.WSSession) -> Any>()

    companion object {
        const val NAME = "client_action"

        open class ClientActionBaseResp : Serializable {
            var code = 0
            var info = "ok"
        }

        class ClientActionRequest : Serializable {
            lateinit var action: String
            lateinit var fromIP: String
            lateinit var body: String
            var query = mutableMapOf<String, Array<String>>()
            var time: Long = 0
        }

        class ClientActionResponse : Serializable {
            var body: String? = null
            var error: String? = null
        }

        class ClientActionArgs : Serializable {
            var actions = mutableListOf<ClientActionRequest>()
        }

        class ClientActionResult : Serializable {
            var responses = mutableListOf<ClientActionResponse>()
        }
    }

    @PostConstruct
    fun init() {
        var poolSize = appConfigService.clientActionThreadSize;
        logger.info("Client action thread pool size ${poolSize}")
        threadPoolExecutor = ThreadPoolExecutor(
            poolSize / 10,
            poolSize,
            5,
            TimeUnit.MINUTES,
            ArrayBlockingQueue(poolSize * 10)
        )
        edgeNodeService.addMsgHandler(NAME, object : EdgeNodeService.Companion.WSMsgHandler<ClientActionArgs>() {
            init {
                argsType = ClientActionArgs::class.java
                handler = { a, b, c -> onClientAction(a, b, c) }
            }
        })
    }

    fun addActionHandler(method: String, f: (request: ClientActionRequest, sess: EdgeNodeService.WSSession) -> Any) {
        if (actionHandlers[method] != null)
            throw RuntimeException("Client action handler repeat : ${method}")
        actionHandlers[method] = f
    }

    fun maxStr(str: String?, len: Int): String? {
        if (str == null) return null;
        if (str.length < len) return str;
        return str.substring(0, len)
    }

    fun onClientAction(args: ClientActionArgs, msg: EdgeNodeService.Companion.WSMsg, sess: EdgeNodeService.WSSession): ClientActionResult {
        val result = ClientActionResult()
        args.actions.forEach {
            val r = ClientActionResponse()
            result.responses.add(r)
        }
        val queueRemainingCapacity = threadPoolExecutor.queue.remainingCapacity()
        if (queueRemainingCapacity < args.actions.size) {
            result.responses.forEach {
                it.error = "Client action pool queue full."
            }
            this.logger.error("Client action invoke queue full, action=${args.actions.firstOrNull()?.action}, actions_size=${args.actions.size}, queue_size=" + queueRemainingCapacity)
            return result;
        }
        val isFinish = AtomicBoolean();
        val doneCount = AtomicInteger();
        args.actions.forEachIndexed { idx, it ->
            threadPoolExecutor.submit {
                if (isFinish.get()) return@submit
                try {
                    val r = result.responses[idx]
                    val h = actionHandlers[it.action]
                    if (h != null) {
                        try {
                            var body = h(it, sess)
                            if (body !is String)
                                body = JSONUtils.toString(body)
                            r.body = body
                            if (isFinish.get()) {
                                r.error = "Invoke success, but response timeout."
                            }
                        } catch (e: Exception) {
                            logger.error("Action ${it.action} handler error", e)
                            var msg = e.message
                            var msgE: Throwable? = e
                            while (msgE != null && msgE != e.cause) {
                                if (e.message != null)
                                    msg = e.message
                                msgE = e.cause
                            }
                            r.error = msg ?: "Null exception"
                        }
                    } else {
                        r.error = "Action not support"
                    }
                    if (r.error != null) {
                        logger.warn("Action ${it.action} error, query=${JSONUtils.toString(it.query)}, body=${maxStr(JSONUtils.toString(it.body), 4096)} => ${r.error}")
                    } else if (logger.isDebugEnabled) {
                        logger.debug("Action ${it.action}, query=${JSONUtils.toString(it.query)}, body=${maxStr(JSONUtils.toString(it.body), 4096)} => ${maxStr(r.body, 4096)}")
                    }
                } finally {
                    doneCount.addAndGet(1)
                }
            }
        }
        val started = System.currentTimeMillis();
        var waitSleep = 1L;
        while (doneCount.get() != args.actions.size) {
            Thread.sleep(waitSleep);
            if (waitSleep <= 100)
                waitSleep++;
            if (System.currentTimeMillis() - started >= appConfigService.clientActionTimeoutSec * 1000) {
                break
            }
        }
        isFinish.set(true);
        val used = System.currentTimeMillis() - started
        if (used >= 3000) {
            this.logger.info("Client action invoke slow ${args.actions.firstOrNull()?.action} ${args.actions.size}, use " + DateUtil.useTimeToHum(used))
        }
        if (doneCount.get() != args.actions.size) {
            this.logger.error("Client action invoke timeout ${args.actions.firstOrNull()?.action} ${doneCount.get()}/${args.actions.size}, use " + DateUtil.useTimeToHum(used))
        }
        return result
    }
}
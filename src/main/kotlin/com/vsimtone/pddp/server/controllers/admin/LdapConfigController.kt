package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestException
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.CryptUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.util.*
import javax.naming.ldap.LdapContext

@RequestMapping("ldap_configs")
@RestController
class LdapConfigController : BaseHttpController() {

    @Autowired
    lateinit var ldapConfigService: LdapConfigService

    @Autowired
    lateinit var ldapService: LDAPService

    @Autowired
    lateinit var ldapSyncRecordService: LdapSyncRecordService

    @Autowired
    lateinit var licenseService: LicenseService

    @Autowired
    lateinit var externalFileSystemService: ExternalFileSystemService

    @Secured(Permissions.LDAP_VIEW)
    @RequestMapping("")
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?
    ): RestResp {
        var queries = BooleanBuilder()
        return jsonOut(ldapConfigService.findAll(queries, pageAttr));
    }

    @Secured(Permissions.LDAP_VIEW)
    @RequestMapping("{id:\\d+}")
    fun view(@PathVariable("id") id: Long): RestResp {
        val data = ldapConfigService.findById(id)
        return jsonOut(data);
    }

    @Secured(Permissions.LDAP_VIEW)
    @RequestMapping("{id:\\d+}/orgTree")
    fun orgTree(@PathVariable("id") id: Long, @RequestParam("parent", required = false) parent: String?): RestResp {
        var ldapCnf = ldapConfigService.findById(id)
        var ldapCtx = ldapService.getLdapCtx(ldapCnf)
        try {
            var data = ldapService.search(LDAPService.Companion.SearchOptions().apply {
                this.cnf = ldapCnf
                this.ctx = ldapCtx
                if (StringUtils.isEmpty(parent))
                    this.dn = ldapCnf.rootDN!!
                else
                    this.dn = ldapService.toDN(parent!!, ldapCnf.rootDN!!).dn
                this.treeResult = false
                this.includeChildren = false
                this.filters = arrayOf(LDAPService.basicTypes[ldapCnf.type!!]!!.getOrgFilter())
            }).filter { it.type == LDAPService.Companion.LdapItem.Type.Org }
            data.forEach {
                if (!StringUtils.isEmpty(parent))
                    it.dn = ldapService.toDN(it.dn.dn, parent!!)
            }
            return jsonOut(data);
        } finally {
            ldapCtx.close()
        }

    }

//    @Secured(Permissions.LDAP_VIEW)
//    @RequestMapping("{id:\\d+}/users")
//    fun users(@PathVariable("id") id: Long, @RequestParam("dn") dn: String): RestResp {
//        var ldapCnf = ldapConfigService.findById(id)
//        var ldapCtx = ldapService.getLdapCtx(ldapCnf)
//        try {
//            var tree = ldapService.search(LDAPService.Companion.SearchOptions().apply {
//                this.cnf = ldapCnf
//                this.ctx = ldapCtx
//                this.dn = ldapService.toDN(dn, ldapCnf.rootDN!!).dn
//                this.treeResult = false
//                this.includeChildren = false
//                this.limit = 100
//                this.filters = arrayOf(LDAPService.basicTypes[ldapCnf.type!!]!!.getUserFilter())
//            }).filter { it.type == LDAPService.Companion.LdapItem.Type.User }
//            addOptLog(OptLog.Type.Read,"域配置——"+ldapCnf.syncDN, mapOf("msg" to ("浏览用户列表信息，域配置数据ID为："+ id)));
//            return jsonOut(tree);
//        } finally {
//            ldapCtx.close()
//        }
//    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.LDAP_EDIT)
    @Transactional
    fun delete(@PathVariable("id") id: Long): RestResp {
        val data = ldapConfigService.findById(id)
        ldapSyncRecordService.findAll(QLdapSyncRecord.a.config.eq(data)).forEach {
            ldapSyncRecordService.delete(it)
        }
        ldapConfigService.delete(data);
        addOptLog(OptLog.DELETE, "域配置", data.host!!, data);
        return jsonOut(true);
    }

    @RequestMapping("{id:\\d+}/sync_now", method = [RequestMethod.POST])
    @Secured(Permissions.LDAP_EDIT)
    fun syncNow(@PathVariable("id") id: Long): RestResp {
        val data = ldapConfigService.findById(id)
        userService.sync(id);
        addOptLog("立即同步", "域配置", data.host!!, data);
        return jsonOut(true);
    }

    @Secured(Permissions.LDAP_VIEW)
    @RequestMapping("{id:\\d+}/records")
    fun view(@PathVariable("id") id: Long, @ModelAttribute("page") pageAttr: PageAttr): RestResp {
        var p = ldapSyncRecordService.findAll(BooleanBuilder().and(QLdapSyncRecord.a.config.id.eq(id)), pageAttr)
        return jsonOut(p);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.LDAP_EDIT)
    @Transactional
    fun edit(
        @ModelAttribute("form") form: LdapConfig,
        @RequestParam("authWithKrbKeytabFile", required = false) authWithKrbKeytabFile: MultipartFile?
    ): RestResp {
        if (form.type == LdapConfig.Type.BOC_P6 && !licenseService.hasComponent(LicenseService.Companion.Components.LdapTypeBOC_P6))
            return RestResp(3, "不支持的类型")

        val data: LdapConfig;
        if (form.id != null) {
            data = ldapConfigService.findById(form.id!!);
        } else {
            if (StringUtils.isEmpty(form.host) || StringUtils.isEmpty(form.username) || StringUtils.isEmpty(form.password))
                return RestResp(3, "请正确填写表单")
            if (form.type != LdapConfig.Type.BOC_P6 && StringUtils.isEmpty(form.rootDN))
                return RestResp(3, "请正确填写表单")
            data = LdapConfig();
        }
        data.type = form.type
        data.host = form.host!!.trim()
        data.port = form.port
        data.username = form.username!!.trim()
        if (!StringUtils.isEmpty(form.password))
            data.password = form.password
        if (form.type == LdapConfig.Type.BOC_P6) {
            form.bocP6RootPath = form.bocP6RootPath!!.trim()
            if (!form.bocP6RootPath!!.startsWith("/"))
                form.bocP6RootPath = "/" + form.bocP6RootPath
            if (!form.bocP6RootPath!!.endsWith("/"))
                form.bocP6RootPath = form.bocP6RootPath + "/"
            form.bocP6RootPath = form.bocP6RootPath!!.replace(Regex("\\\\"), "/")
            data.bocP6RootPath = form.bocP6RootPath!!.trim()
            data.bocP6FSType = form.bocP6FSType
        } else {
            data.rootDN = form.rootDN!!.trim()
        }
        data.syncTo = form.syncTo
        if (data.syncTo != null && data.syncTo!!.id != null) {
            if (form.type != LdapConfig.Type.BOC_P6) {
                if (form.syncDN == null || form.syncDN!!.trim().isEmpty())
                    return RestResp(3, "同步DN不能为空")
                data.syncDN = form.syncDN!!.trim()
            }

            data.syncTo = orgService.findById(data.syncTo!!.id!!)
            if (data.syncTo!!.type != Organization.Type.Local) return this.jsonOut(1, "不能选择该机构: 不是自建机构")
            val c = orgService.count(QOrganization.a.parent.id.eq(data.syncTo!!.id!!).and(QOrganization.a.type.eq(Organization.Type.Local)))
            if (c > 0) return this.jsonOut(1, "不能选择该机构: 有子机构")
            data.syncScheduleCron = form.syncScheduleCron
            data.syncScheduleLatestExecTime = Date()
            data.syncMaxDeleteSize = form.syncMaxDeleteSize;
            data.authWithAdHost = form.authWithAdHost
            data.authWithAdPort = form.authWithAdPort
            data.authWithKrbPrincipal = form.authWithKrbPrincipal
            if (authWithKrbKeytabFile != null) {
                val keytabData = authWithKrbKeytabFile.bytes
                data.authWithKrbKeytab = CryptUtils.base64encode(keytabData)
                data.authWithKrbKeytabHash = CryptUtils.sha256hex(keytabData);
            }
            if (StringUtils.isNotEmpty(form.syncScheduleCron))
                data.syncScheduleNextExecTime = userService.getNextExecTime(data.syncScheduleLatestExecTime, data.syncScheduleCron)
            else
                data.syncScheduleNextExecTime = null;
        } else {
            data.syncTo = null;
            data.syncDN = null;
            data.syncScheduleCron = null;
            data.syncScheduleLatestExecTime = null;
            data.syncScheduleNextExecTime = null;
            data.syncMaxDeleteSize = null;
            data.authWithAdHost = null;
            data.authWithAdPort = null;
            data.authWithKrbPrincipal = null;
            data.authWithKrbPrincipal = null;
        }
        if (data.type == LdapConfig.Type.ActiveDirectory || data.type == LdapConfig.Type.Ldap) {
            var ctx: LdapContext? = null
            try {
                ctx = ldapService.getLdapCtx(data)
                try {
                    ctx.lookup(data.rootDN)
                } catch (e: Exception) {
                    if (e.toString().indexOf("NO_SUCH_OBJECT") != -1 || e.toString().indexOf("NO_OBJECT") != -1)
                        throw RestException(1, "根DN错误：不存在")
                    if (e.toString().indexOf("BAD_NAME") != -1)
                        throw RestException(1, "根DN格式错误")
                    throw RestException(1, "根DN错误：" + (e.message ?: "不存在"))
                }
                if (data.syncTo != null)
                    try {
                        ctx.lookup(ldapService.toDN(data.syncDN!!, data.rootDN!!).dn)
                    } catch (e: Exception) {
                        if (e.toString().indexOf("NO_SUCH_OBJECT") != -1 || e.toString().indexOf("NO_OBJECT") != -1)
                            throw RestException(1, "同步DN错误：不存在")
                        if (e.toString().indexOf("BAD_NAME") != -1)
                            throw RestException(1, "同步DN格式错误")
                        this.logger.error("edit ldap exception", e);
                        throw RestException(1, "同步DN错误：" + (e.message ?: "未知错误"))
                    }
            } catch (e: Exception) {
                if (e is RestException)
                    throw e;
                throw RestException(1, "链接到LDA失败：" + e.message)
            } finally {
                ctx?.close()
            }
        }
        if (data.type == LdapConfig.Type.BOC_P6) {
            val testForm = ExternalFileSystem()
            testForm.type = form.bocP6FSType
            testForm.host = form.host
            testForm.port = form.port
            testForm.username = form.username
            testForm.password = data.password
            testForm.rootPath = form.bocP6RootPath
            try {
                this.externalFileSystemService.checkConfig(testForm);
            } catch (e: Exception) {
                logger.error("${form} check error", e);
                var t: Throwable = e;
                if (t !is RestException)
                    while (t.cause != null)
                        t = t.cause!!;
                return this.jsonOut(1, "错误：${t.message}")
            }
            data.domain = form.domain
        } else {
            data.domain = ldapService.getDomain(data)
        }
        if (StringUtils.isNotEmpty(data.authWithKrbPrincipal) && data.authWithKrbKeytab == null)
            throw RestException(1, "请上传KRB认证私钥");
        if (data.authWithKrbPrincipal != null && data.authWithKrbKeytab != null) {
            if (!data.authWithKrbPrincipal!!.endsWith("@" + data.domain.uppercase()))
                throw RestException(1, "KRB认证主体错误: 域不匹配");
            userService.getKrbValidator(data.authWithKrbPrincipal!!, data.authWithKrbKeytabHash!!, data.authWithKrbKeytab!!) ?: throw RestException(1, "KRB认证配置错误: 认证私钥解析失败或者认证主体和私钥不匹配")
        }
        ldapConfigService.save(data);
        if (data.authWithKrbKeytabHash != null && data.authWithKrbKeytab != null && data.authWithKrbPrincipal != null)
            userService.getKrbValidator(data.authWithKrbPrincipal!!, data.authWithKrbKeytabHash!!, data.authWithKrbKeytab!!);
        if (form.id == null)
            addOptLog(OptLog.ADD, "域配置", data.host!!, data);
        else
            addOptLog(OptLog.EDIT, "域配置", data.host!!, data);
        return jsonOut(data);
    }
}
package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.services.CacheInvokeService
import com.vsimtone.pddp.server.services.HomeStatsService
import com.vsimtone.pddp.server.utils.AppUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.text.SimpleDateFormat
import java.util.*

@RestController
@RequestMapping("home")
class HomeController : BaseHttpController() {

    @Autowired
    lateinit var homeStatsService: HomeStatsService

    @Autowired
    lateinit var cacheInvokeService: CacheInvokeService

    @RequestMapping("is_server")
    fun isServer(): String {
        return "true";
    }

    @PostMapping("excelToJson")
    fun excelToJson(@RequestParam("file") file: MultipartFile): RestResp {
        try {
            return this.jsonOut(AppUtils.readExcel(file.inputStream));
        } catch (e: Exception) {
            return this.jsonOut(1, e.message)
        }
    }


}
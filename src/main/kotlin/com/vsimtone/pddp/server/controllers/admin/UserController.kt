package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.AppUtils
import com.vsimtone.pddp.server.utils.RestUtil
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping("users")
class UserController : BaseHttpController() {

    @Autowired
    lateinit var sysRoleService: SysRoleService

    @Autowired
    lateinit var ldapService: LDAPService

    @Autowired
    lateinit var ldapConfigService: LdapConfigService

    @Autowired
    lateinit var passwordEncoder: PasswordEncoder

    @Autowired
    lateinit var licenseService: LicenseService

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Secured(Permissions.USER_VIEW)
    @RequestMapping("")
    @JsonView(JsonViews.SpecList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("org_path", required = false) orgPath: String?,
        @RequestParam("type", required = false) type: User.Type?,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("manager", required = false) manager: Boolean?,
        @RequestParam("nickname", required = false) nickname: String?,
        @RequestParam("username", required = false) username: String?,
        @RequestParam("enabled", required = false) enabled: Boolean?,
        @RequestParam("locked", required = false) locked: Boolean?,
        @RequestParam("manageRole", required = false) manageRole: Long?
    ): RestResp {
        var queries = BooleanBuilder();
        queries.and(currUserMgrOrgQuery(QUser.a.org))
        if (!StringUtils.isEmpty(orgPath))
            queries.and(QUser.a.org.path.like("$orgPath%"));
        if (type != null)
            queries.and(QUser.a.type.eq(type));
        if (!StringUtils.isEmpty(search)) {
            queries.and(QUser.a.nickname.like("%$search%").or(QUser.a.username.like("%$search%")))
        }
        if (manager != null) {
            queries.and(QUser.a.manager.eq(manager))
        }
        if (nickname != null)
            queries.and(QUser.a.nickname.like("%$nickname%"))
        if (username != null)
            queries.and(QUser.a.username.like("%$username%"))
        if (enabled != null)
            queries.and(QUser.a.enabled.eq(enabled))
        if (locked != null)
            queries.and(QUser.a.locked.eq(locked))
        if (manageRole != null)
            queries.and(QUser.a.manageRoles.any().id.eq(manageRole))

        queries.and(
            QUser.a.id.notIn(userService.findManager(orgService.getSysOrg(OrgService.SYS_ORG_KEY_ROOT).id!!, SysRole.Code.SuperManager, 10).map { it.id })
        )

        var p = userService.findAll(queries, pageAttr);
        if (manager != null && manager) {
            p.content.forEach {
                if (it.manager) {
                    RestUtil.unwrapBean(User::manageOrgs, it)
                    RestUtil.unwrapBean(User::manageRoles, it)
                }
            }
        }
        return jsonOut(p);
    }

    @RequestMapping("domains")
    fun domains(): RestResp {
        val names = mutableListOf<String>()
        ldapConfigService.findAll().forEach {
            if (it.syncTo != null) {
                names.add(it.domain)
            }
        }
        return jsonOut(names)
    }

    @RequestMapping("curr")
    @JsonView(JsonViews.SpecView::class)
    fun curr(): RestResp {
        var user = userService.currUser() ?: return jsonOut(mapOf("serverVer" to sysService.thisNodeInfo.version))
        var licenseStatus = licenseService.getLicenseStatus()
        if (licenseStatus != LicenseService.Companion.LicenseStatus.Active) {
            user.permissions = user.permissions.filter { it == Permissions.APP_LICENSE_EDIT || it == Permissions.APP_LICENSE_VIEW }.toMutableList()
            if (user.permissions.isEmpty())
                return jsonOut(mapOf("serverVer" to sysService.thisNodeInfo.version));
        }
        var info = userService.findById(user.id);
        RestUtil.unwrapBean(User::manageOrgs, info);
        RestUtil.unwrapBean(User::manageRoles, info);
        return jsonOut(
            mapOf(
                "serverVer" to sysService.thisNodeInfo.version,
                "user" to info,
                "user_privileges" to user.permissions
            )
        )
    }


    @Secured(Permissions.USER_VIEW)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.SpecView::class)
    fun view(@PathVariable("id") id: Long): RestResp {
        val u = userService.findById(id);
        RestUtil.unwrapBean(User::manageOrgs, u)
        RestUtil.unwrapBean(User::manageRoles, u)
        return jsonOut(u);
    }


    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.USER_EDIT)
    @Transactional
    @JsonView(JsonViews.View::class)
    fun delete(@PathVariable("id") id: Long): RestResp {
        val user = userService.findById(id);
        if (user.manageRoles.find { it.code == SysRole.Code.SuperManager } != null)
            return jsonOut(1, "不允许删除")
        val ok = userService.delUser(user);
        if (!ok)
            return this.jsonOut(1, "删除失败,该用户正被使用中，请先确认未使用该用户。")
        addOptLog(OptLog.DELETE, "用户", user.username, user);
        return jsonOut(true);
    }

    @RequestMapping("{id:\\d+}/unlock", method = [RequestMethod.POST])
    @Transactional
    @Secured(Permissions.USER_EDIT)
    @JsonView(JsonViews.View::class)
    fun unlock(@PathVariable("id") id: Long): RestResp {
        var user = userService.findById(id);
        user.locked = false;
        user.loginFailCount = 0;
        userService.save(user);
        addOptLog("解锁", "用户", user.username, user);
        return jsonOut(true);
    }

    @RequestMapping("{id:\\d+}/set_manager", method = [RequestMethod.POST])
    @Transactional
    @Secured(Permissions.USER_SET_MANAGER)
    @JsonView(JsonViews.View::class)
    fun manager(@PathVariable("id") id: Long, @ModelAttribute() form: User): RestResp {
        var user = userService.findById(id);
        if (user.manageRoles.find { it.code == SysRole.Code.SuperManager } != null)
            return jsonOut(1, "不允许修改")
        user.manageRoles = form.manageRoles
        user.manageOrgs = form.manageOrgs
        user.manager = user.manageRoles.isNotEmpty() && user.manageOrgs.isNotEmpty();
        userService.save(user);
        if (user.manager)
            addOptLog("设定管理员", "管理员", user.username, user);
        else
            addOptLog("取消管理员", "管理员", user.username, user);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.USER_EDIT)
    @Transactional
    @JsonView(JsonViews.View::class)
    fun edit(@ModelAttribute("form") form: User): RestResp {
        var data: User;
        if (form.id != null) {
            data = userService.findById(form.id!!);
            if (data.manageRoles.find { it.code == SysRole.Code.SuperManager } != null) error("不允许修改该用户")
            RestUtil.unwrapBean(User::org, data);
        } else {
            data = User();
            data.username = form.username;
            data.type = User.Type.Local;
            currUserMgrOrgRequire(form.org);
            data.org = form.org;
            if (StringUtils.isEmpty(form.username) || form.username.length < 4) {
                return jsonOut(1, "用户账户最少4位")
            }
            if (userService.findByUsername(form.username) != null) {
                return jsonOut(1, "用户账户已存在：${form.username}")
            }
        }
        if (data.type == User.Type.Local) {
            data.nickname = form.nickname;
            data.email = form.email;
            data.enabled = form.enabled;
            currUserMgrOrgRequire(form.org);
            val org = orgService.findById(form.org.id!!)
            if (orgService.isSyncTo(org.id!!) || org.type == Organization.Type.Ldap) {
                return jsonOut(1, "不能在同步目录修改用户")
            }
            data.org = form.org;
            if (!StringUtils.isEmpty(form.password)) {
                var pwdRule = "密码不符合规则：长度8-32位，必须含一个大写字母，一个小写字母，一个数字";
                if (form.password!!.length < 8)
                    return jsonOut(1, pwdRule)
                if (!form.password!!.matches(Regex(".*[A-Z]+.*")))
                    return jsonOut(1, pwdRule)
                if (!form.password!!.matches(Regex(".*[a-z]+.*")))
                    return jsonOut(1, pwdRule)
                if (!form.password!!.matches(Regex(".*[0-9]+.*")))
                    return jsonOut(1, pwdRule)
                data.password = userService.passwordEncoder.encode(form.password);
            }
        }
//        如果没锁定，一种解锁另一种则还未被锁定
        if (!form.locked) {
            if (form.loginFailCount >= 10)
                data.loginFailCount = 0;
            else
                data.loginFailCount = form.loginFailCount;
        }
        data.locked = form.locked;

        userService.save(data);
        clientDeviceService.findAll(QClientDevice.a.user.eq(data)).forEach {
            val cacheDevice = clientDeviceService.findByIdInCache(it.id!!)!!
            cacheDevice.user = data;
            cacheDevice.org = orgService.findById(data.org.id!!);
            clientDeviceService.saveDelay(cacheDevice, false);
        }
        if (form.id == null)
            addOptLog(OptLog.ADD, "用户", data.username, data);
        else
            addOptLog(OptLog.EDIT, "用户", data.username, data);
        return jsonOut(data.id);
    }

    @RequestMapping(value = ["import"], method = [RequestMethod.POST])
    @Transactional
    @Secured(Permissions.USER_EDIT)
    @JsonView(JsonViews.View::class)
    fun import(@RequestParam("upfile") upfile: MultipartFile): RestResp {
        val xlsData = AppUtils.readExcel(upfile.inputStream)
        val columns = xlsData[0]
        val usernameColIdx = columns.indexOf("用户账户")
        val roleNameColIdx = columns.indexOf("角色")
        val mgrOrgColIdx = columns.indexOf("管理机构")
        if (usernameColIdx == -1) return this.jsonOut(1, "未发现用户账户列")
        if (roleNameColIdx == -1) return this.jsonOut(1, "未发现角色列")
        if (mgrOrgColIdx == -1) return this.jsonOut(1, "未发现管理机构列")

        var nonString = fun(row: Int, column: Int): String {
            var row = xlsData[row]
            if (StringUtils.isEmpty(row[column].toString().trim()))
                error("第${row}行，${columns[column]} 不能为空")
            return row[column].toString().trim()
        }
        val errMsg = mutableListOf<String>()
        var errorCount = 0;
        var successCount = 0;
        val users = mutableListOf<User>()
        val allRoles = sysRoleService.findAll().filter { it.code != SysRole.Code.SuperManager }
        val orgCaches = mutableMapOf<String, Organization>()
        val getOrgByName = fun(name: String): Organization? {
            val names = name.split(Regex("(->|/|\n)")).filter { it.isNotBlank() }
            var namePath = ""
            var org: Organization? = null
            names.forEach {
                val _namePath = namePath + it.trim()
                if (orgCaches[_namePath] == null) {
                    val _org = orgService.findByParentAndName(org, it.trim()) ?: return null
                    orgCaches[_namePath] = _org;
                }
                org = orgCaches[_namePath]
                namePath = _namePath
            }
            return org
        }
        xlsData.forEachIndexed { i, row ->
            if (i == 0) return@forEachIndexed
            try {
                var username = nonString(i, usernameColIdx)
                var roleNames = nonString(i, roleNameColIdx).split(Regex("\\s*[,，\\\\|]\\s*")).filter { it.isNotBlank() }
                var orgNames = nonString(i, mgrOrgColIdx).split(Regex("\\s*[,，\\\\|]\\s*")).filter { it.isNotBlank() }

                var manageRoleList = roleNames.map { name -> allRoles.find { it.name == name } ?: error("角色'$name'不存在") }

                var managerOrgList = orgNames.map { name ->
                    val org = getOrgByName(name) ?: error("管理机构'$name'不存在")
                    if (!getCurrUserCanMgrOrg(org)) error("您没有'$name'的管理权限")
                    org
                }

                var user = userService.repo.findOne(QUser.a.username.eq(username)).orElseGet { null } ?: error("账户'$username'不存在!")
                user.manageRoles = manageRoleList.toMutableList()
                user.manageOrgs = managerOrgList.toMutableList()
                user.manager = managerOrgList.isNotEmpty() && manageRoleList.isNotEmpty()
                userService.repo.save(user)
                successCount++
                users.add(user)

            } catch (e: Exception) {
                errorCount++
                errMsg.add(e.message ?: "空指针")
                logger.error("导入失败", e)
            }
        }
        addOptLog("导入管理员", "管理员", "${users.size}条", users);
        return this.jsonOut(mutableMapOf("count" to mutableMapOf("success" to successCount, "error" to errorCount), "msg" to errMsg))
    }

    @RequestMapping(value = ["edit_pwd"], method = [RequestMethod.POST])
    @Transactional
    @JsonView(JsonViews.View::class)
    fun editPwd(
        @RequestParam("oldPassword") oldPassword: String,
        @RequestParam("password") password: String
    ): RestResp {
        var data = currUser();
        if (!data.type.equals(User.Type.Local))
            return jsonOut(1, "非本地账户，不能进行密码的修改")
        if (userService.checkPassword(data, oldPassword) != null)
            return jsonOut(1, "旧密码错误，不能进行修改")
        if (!StringUtils.isEmpty(password)) {
            var pwdRule = "密码不符合规则：长度8-32位，必须含一个大写字母，一个小写字母，一个数字";
            if (password.length < 8)
                return jsonOut(1, pwdRule)
            if (!password.matches(Regex(".*[A-Z]+.*")))
                return jsonOut(1, pwdRule)
            if (!password.matches(Regex(".*[a-z]+.*")))
                return jsonOut(1, pwdRule)
            if (!password.matches(Regex(".*[0-9]+.*")))
                return jsonOut(1, pwdRule)
            data.password = userService.passwordEncoder.encode(password);
        }
        userService.save(data);
        addOptLog("修改密码", "用户", data.username, data);
        return jsonOut(true);
    }

}
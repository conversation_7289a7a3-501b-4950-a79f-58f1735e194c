package com.vsimtone.pddp.server.controllers.admin

import com.vsimtone.pddp.server.beans.CurrAuthedUser
import com.vsimtone.pddp.server.configs.AdminSecurityConfig
import com.vsimtone.pddp.server.services.BaseService.Companion.appCtx
import com.vsimtone.pddp.server.utils.JSONUtils
import jakarta.websocket.*
import okhttp3.internal.closeQuietly
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContextHolder

abstract class BaseWSController : BaseController() {

    inner class BaseSessContext {
        var session: Session
        var closed = false
        var fromIP: String
        var authentication: Authentication? = null
        var user: CurrAuthedUser? = null
        var dataContext: Any? = null

        constructor(_session: Session) {
            this.session = _session
            this.fromIP = (_session.userProperties.get("ClientIP")!!).toString()
            this.authentication = _session.userProperties.get("Authentication") as Authentication?
        }

        override fun toString(): String {
            return "BaseSessContext(session=${session.id}, path=${session.requestURI.path}, fromIP=${fromIP}, user=${user?.username}, data=${dataContext}"
        }
    }

    var sessions = mutableMapOf<String, BaseSessContext>()

    open fun requirePermissions(): Array<String>? {
        return arrayOf();
    }

    open fun dataContext(baseSessContext: BaseSessContext): Any? {
        return null;
    }

    abstract fun onMsg(ctx: BaseSessContext, msg: String, type: String? = null);

    abstract fun onMsg(ctx: BaseSessContext, msg: ByteArray);

    fun sendMessage(baseCtx: BaseSessContext, type: String, _data: Any?, close: Boolean = false) {
        var data = _data;
        if (close && data != null && data !is String) {
            data = JSONUtils.toString(_data)
        }
        baseCtx.session.basicRemote.sendText(JSONUtils.toString(mapOf("type" to type, "data" to data)))
        baseCtx.session.basicRemote.flushBatch()
        if (close) {
            baseCtx.closed = true;
            if (data != null && type == "error") baseCtx.session.close(CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "${data}"));
            else baseCtx.session.closeQuietly();
        }
    }

    fun sendError(baseCtx: BaseSessContext, data: Any?, close: Boolean = true) {
        if (close) this.logger.error("on error: ${baseCtx}", RuntimeException("${data}"))
        return sendMessage(baseCtx, "error", data, close)
    }

    fun onMessageInit(baseCtx: BaseSessContext) {
        appCtx.autowireCapableBeanFactory.autowireBean(this)
        SecurityContextHolder.getContext().authentication = baseCtx.authentication
    }

    @OnOpen
    fun onOpen(session: Session) {
        appCtx.autowireCapableBeanFactory.autowireBean(this)
        val wsSess = BaseSessContext(session)
        try {
            wsSess.dataContext = dataContext(wsSess)
            val permList = requirePermissions();
            if (wsSess.authentication != null && wsSess.authentication is AdminSecurityConfig.AdminLoginAuthToken) {
                wsSess.user = (wsSess.authentication as AdminSecurityConfig.AdminLoginAuthToken).details!! as CurrAuthedUser;
            }
            if (permList != null) {
                if (wsSess.user == null) return sendError(wsSess, "need login", true);
                permList.forEach {
                    if (!wsSess.user!!.permissions.contains(it)) {
                        return sendError(wsSess, "no permission", true)
                    }
                }
            }
            sessions[session.id] = wsSess
            logger.info("on open : ${wsSess}")
        } catch (e: Exception) {
            this.logger.error("on open error: ${wsSess}", e);
            sendError(wsSess, "init fail", true);
        }
    }

    @OnClose
    fun onClose(session: Session, reason: CloseReason) {
        val weSess = sessions[session.id] ?: return session.close(CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "onClose: session not found"))
        weSess.closed = true;
        sessions.remove(session.id)
        logger.info("on close: ${weSess} ${reason}")
    }

    @OnError
    fun onError(session: Session, e: Throwable) {
        val wsSess = sessions[session.id] ?: return session.close(CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "onError: session not found"))
        wsSess.closed = true;
        sessions.remove(session.id)
        logger.error("on error: ${wsSess}", e)
    }

    @OnMessage
    fun onBinMessage(session: Session, binaryMessage: ByteArray) {
        val ctx = sessions[session.id] ?: return session.close(CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "onBinMessage: session not found"))
        try {
            onMessageInit(ctx)
            onMsg(ctx, binaryMessage)
        } catch (e: Exception) {
            this.logger.error("on binary message error: ${ctx}", e)
            return sendError(ctx, "500", true)
        }
    }

    @OnMessage
    fun onTxtMessage(session: Session, textMessage: String) {
        val ctx = sessions[session.id] ?: return session.close(CloseReason(CloseReason.CloseCodes.UNEXPECTED_CONDITION, "onTxtMessage: session not found"))
        try {
            onMessageInit(ctx)
            var msg: String = textMessage
            var type: String? = null
            if (textMessage.startsWith("type:")) {
                val t = textMessage.split(Regex(":"), 3)
                if (t.size >= 2) {
                    type = t[1]
                    msg = if (t.size == 3) t[2] else ""
                }
            }
            onMsg(ctx, textMessage, type)
        } catch (e: Exception) {
            this.logger.error("on text message error: ${ctx}", e)
            return sendError(ctx, "500", true)
        }
    }

}

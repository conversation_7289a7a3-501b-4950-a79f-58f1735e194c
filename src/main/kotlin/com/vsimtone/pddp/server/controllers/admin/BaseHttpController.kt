package com.vsimtone.pddp.server.controllers.admin

import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jakarta.servlet.http.HttpSession
import org.springframework.beans.propertyeditors.CustomDateEditor
import org.springframework.web.bind.ServletRequestDataBinder
import org.springframework.web.bind.annotation.InitBinder
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*

open class BaseHttpController : BaseController() {

    @InitBinder
    @Throws(Exception::class)
    protected open fun initBinder(request: HttpServletRequest?, binder: ServletRequestDataBinder) {
        val format: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val dateEditor = CustomDateEditor(format, true)
        binder.registerCustomEditor(Date::class.java, dateEditor)
    }

    open fun getAttribute(attributeName: String?): Any? {
        return getRequest().getAttribute(attributeName)
    }

    open fun setAttribute(attributeName: String?, `object`: Any?) {
        getRequest().setAttribute(attributeName, `object`)
    }

    open fun getSession(attributeName: String?): Any? {
        return getRequest().getSession(true).getAttribute(attributeName)
    }

    open fun setSession(attributeName: String?, `object`: Any?) {
        getRequest().getSession(true).setAttribute(attributeName, `object`)
    }

    open fun getRequest(): HttpServletRequest {
        val ra = RequestContextHolder.getRequestAttributes()
        return (ra as ServletRequestAttributes).request
    }

    open fun getResponse(): HttpServletResponse {
        val ra = RequestContextHolder.getRequestAttributes()
        return (ra as ServletRequestAttributes).response
    }

    open fun getSession(): HttpSession? {
        return getRequest().getSession(true)
    }


    open fun getHeader(headerName: String?): String? {
        return getRequest().getHeader(headerName)
    }

    open fun allowCrossDomainAccess() {
        val servletResponse = getResponse()
        servletResponse.setHeader("Access-Control-Allow-Origin", "*")
        servletResponse.setHeader("Access-Control-Allow-Methods", "POST,GET")
    }
}
package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.RestUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import kotlin.io.path.name

@RestController
@RequestMapping("edge_nodes")
class EdgeNodeController : BaseController() {

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService

    @Autowired
    lateinit var fileService: FileService

    @Autowired
    lateinit var certKeyPairService: CertKeyPairService

    @Autowired
    lateinit var clientDeviceService: ClientDeviceService

    @Autowired
    lateinit var edgeNodeRouteService: EdgeNodeRouteService

    @Autowired
    lateinit var edgeNodeClusterService: EdgeNodeClusterService

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("")
    @JsonView(JsonViews.SpecList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("name", required = false) name: String?,
        @RequestParam("status", required = false) status: EdgeNode.Status?,
        @RequestParam("url", required = false) url: String?
    ): RestResp {
        val queries = BooleanBuilder();
        val a = QEdgeNode.a;
        if (name != null)
            queries.and(a.hostname.like("%${name}%"))
        if (url != null) {
            queries.and(a.httpsUrl.like("%${url}%"))
        }
        if (status != null) {
            queries.and(a.status.eq(status))
        }
        val p = edgeNodeService.findAll(queries, pageAttr)
        p.content.forEach {
            RestUtil.unwrapBean(EdgeNode::certKeyPair, it)
        }
        return jsonOut(p);
    }

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.SpecView::class)
    fun view(@PathVariable("id") id: Long): RestResp {
        val node = edgeNodeService.findById(id)
        return jsonOut(node)
    }


    @Secured(Permissions.PROXY_NODE_EDIT)
    @PostMapping("")
    @Transactional
    fun edit(@ModelAttribute("form") form: EdgeNode): RestResp {
        val node = edgeNodeService.findById(form.id!!)
        if (node.cluster != null) {
            return this.jsonOut(1, "不能重复加入")
        }
        val cluster = edgeNodeClusterService.findById(form.cluster!!.id!!)
        edgeNodeClusterService.nodeJoin(cluster, node, "user_manual");
        return jsonOut(cluster.id)
    }

}
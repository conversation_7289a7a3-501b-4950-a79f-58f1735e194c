package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.services.LicenseService
import com.vsimtone.pddp.server.services.SysService
import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@RestController("LicenseController")
@RequestMapping("license")
class LicenseController : BaseHttpController() {

    @Autowired
    lateinit var service: LicenseService

    @Autowired
    lateinit var sysService: SysService

    @RequestMapping("components")
    @Secured()
    @JsonView(JsonViews.View::class)
    fun components(): RestResp {
        return jsonOut(mapOf("components" to service.getLicenseContent()?.components));
    }

    @RequestMapping("")
    @Secured(Permissions.APP_LICENSE_VIEW)
    @JsonView(JsonViews.View::class)
    fun getLicenseInfo(): RestResp {
        return jsonOut(
            mapOf(
                "status" to service.getLicenseStatus(),
                "content" to service.getLicenseContent(),
                "freeClientDevice" to service.freeClientDeviceLicense.get(),
                "server_nodes" to sysService.serverNodes.readAllValues().toMutableList()
            )
        );
    }


    @PostMapping("upload")
    @Secured(Permissions.APP_LICENSE_EDIT)
    fun uploadLicense(@RequestParam("file", required = false) file: MultipartFile?, @RequestParam("data", required = false) data: String?): RestResp {
        var content = "";
        if (file != null)
            content = IOUtils.toString(file.inputStream, Charsets.UTF_8);
        else
            content = data!!;
        if (content.trim().isEmpty())
            return jsonOut(1, "授权内容错误");
        try {
            service.applyLicenseContent(content);
        } catch (e: Exception) {
            this.logger.error("Invalidate license", e);
            return jsonOut(1, "授权校验失败");
        }
        return jsonOut(true);
    }


    @PostMapping("generate_request")
    @Secured(Permissions.APP_LICENSE_EDIT)
    fun generateLicenseRequest(): RestResp {
        return jsonOut(service.generateLicenseRequest());
    }


}

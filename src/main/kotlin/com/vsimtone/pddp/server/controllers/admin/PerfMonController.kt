package com.vsimtone.pddp.server.controllers.admin

import com.vsimtone.pddp.server.beans.PerfMonStats
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.services.PerfMonService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("perf_mon")
class PerfMonController : BaseHttpController() {

    @Autowired
    lateinit var perfMonService: PerfMonService

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("")
    fun list(
        @RequestParam("begin", required = false) begin: Long?,
        @RequestParam("end", required = false) end: Long?,
        @RequestParam("target") target: String,
        @RequestParam("name") name: String,
        @RequestParam("size", required = false) size: Long?
    ): RestResp {
        val limit = size ?: 1440;
        val content = mutableListOf<PerfMonStats>()
        perfMonService.findAll(target, name, true) {
            if (content.size >= limit)
                return@findAll false;
            if (begin != null && it.time < begin) {
                return@findAll true;
            }
            if (end != null && it.time > end) {
                return@findAll false;
            }
            content.add(it)
            return@findAll true;
        }
        return jsonOut(content)
    }

}
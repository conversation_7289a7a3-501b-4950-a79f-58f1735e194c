package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.OptLog
import com.vsimtone.pddp.server.entities.Organization
import com.vsimtone.pddp.server.entities.QOrganization
import com.vsimtone.pddp.server.services.EdgeNodeService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RequestMapping("organizations")
@RestController
class OrganizationController : BaseHttpController() {

    @Autowired
    lateinit var edgeNodeService: EdgeNodeService


    @Secured(Permissions.ORG_VIEW)
    @RequestMapping("")
    fun list(
        @RequestParam("ids", required = false) ids: Array<Long>?,
        @RequestParam("parent", required = false) parent: Long?,
        @RequestParam("search", required = false) search: String?,
    ): RestResp {
        val queries = BooleanBuilder();
        val a = QOrganization.a;

        val currUser = userService.currUser()!!
        if (!currUser.manager) throwErrorAccess()

        var mgrPaths = mutableListOf<String>()
        currUser.manageOrgs.sortedBy { it.path }.forEach {
            if (mgrPaths.indexOf(it.path) == -1) {
                mgrPaths.add(it.path)
            }
        }
        val pathQueries = BooleanBuilder()
        mgrPaths.forEach {
            pathQueries.or(a.path.startsWith(it))
            var paths = it.split("|").filter { it.isNotBlank() }
            paths.forEachIndexed { idx, id ->
                var ep = paths.subList(0, idx + 1).joinToString("|") + "|"
                if (mgrPaths.indexOf(ep) == -1) {
                    pathQueries.or(a.path.eq(ep))
                }
            }
        }
        queries.andAnyOf(pathQueries)

        if (ids != null)
            queries.and(a.id.`in`(*ids))
        else if (parent != null)
            queries.and(a.parent.id.eq(parent))
        else if (StringUtils.isEmpty(search))
            queries.and(a.parent.isNull)
        else {
            queries.and(a.name.contains(search))
            return jsonOut(orgService.findAll(queries, PageAttr(PageAttr.FIRST_NUM, 15, null, false)).content.map {
                mapOf(
                    "names" to orgService.getNames(it),
                    "path" to it.path,
                    "id" to it.id.toString()
                )
            })
        }
        val page = orgService.findAll(queries)
        page.forEach {
            it.putBundle("canMgr", getCurrUserCanMgrOrg(it))
        }
        return jsonOut(page);
    }

    @Secured()
    @RequestMapping("getNamesByPath")
    fun getNamesByPath(@RequestParam("path") path: String): RestResp {
        var names = mutableListOf<String>()
        path.split("|").forEach {
            if (!it.isEmpty())
                names.add(orgService.findById(it.toLong()).name)
        }
        return jsonOut(names);
    }

    @Secured(Permissions.ORG_VIEW)
    @RequestMapping("{id:\\d+}")
    fun view(@PathVariable("id") id: Long): RestResp {
        val data = orgService.findById(id);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.ORG_EDIT)
    fun delete(@PathVariable("id") id: Long): RestResp {
        val org = orgService.findById(id);
        if (!getCurrUserCanMgrOrg(org)) return jsonOut(1, "无此机构删除权限")
        if (org.type == Organization.Type.Ldap) {
            return jsonOut(1, "该机构为同步过来的机构，不允许删除")
        }
        if (orgService.isSyncTo(org.id!!))
            return jsonOut(1, "请先取消同步")
        try {
            var ok = false;
            orgService.runInTran {
                ok = orgService.delOrg(org);
            }
            if (!ok)
                return jsonOut(1, "删除失败,该机构正被使用中，请先确认未使用该机构。");
        } catch (e: Exception) {
            return jsonOut(1, "删除失败,该机构正被使用中，请先确认未使用该机构。");
        }
        addOptLog(OptLog.DELETE, "组织机构", org.name, org);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.ORG_EDIT)
    @Transactional
    fun edit(@ModelAttribute("form") form: Organization): RestResp {
        var data: Organization;
        if (form.id != null) {
            data = orgService.findById(form.id!!);
            if (data.type != Organization.Type.Local)
                return jsonOut(1, "该机构为同步过来的机构，不允许手动修改")
            if (!getCurrUserCanMgrOrg(data)) return jsonOut(1, "无修改权限")
        } else {
            data = Organization();
            if (form.parent == null) return jsonOut(2, "请选择上级机构")
            form.parent = orgService.findById(form.parent!!.id!!)
            if (form.parent!!.type == Organization.Type.Ldap) return jsonOut(1, "不能新建子机构")
            if (!getCurrUserCanMgrOrg(form.parent!!)) return jsonOut(1, "无新建权限")
            data.parent = form.parent
        }
        if (data.parent != null && orgService.isSyncTo(data.parent!!.id!!))
            return jsonOut(2, "该机构已设置为同步机构，不允许手动添加子机构")
        val e = orgService.findByParentAndName(data.parent, form.name)
        if (e != null && e.id != data.id) return jsonOut(2, "机构名称重复")
        data.type = Organization.Type.Local
        data.name = form.name
        orgService.save(data);

        if (form.id == null)
            addOptLog(OptLog.ADD, "组织机构", data.name, data);
        else
            addOptLog(OptLog.EDIT, "组织机构", data.name, data);
        return jsonOut(data);
    }
}
package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.services.EdgeNodeClusterService
import com.vsimtone.pddp.server.services.EdgeNodeRouteService
import com.vsimtone.pddp.server.utils.AppUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.net.util.SubnetUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RequestMapping("edge_node_routes")
@RestController
class EdgeNodeRouteController : BaseHttpController() {

    @Autowired
    lateinit var edgeNodeRouteService: EdgeNodeRouteService

    @Autowired
    lateinit var edgeNodeClusterService: EdgeNodeClusterService

    fun fetchEdgeNodes(r: EdgeNodeRoute) {
        val nodes = mutableListOf<EdgeNodeCluster>()
        r.edgeNodeClusters.forEach {
            try {
                nodes.add(edgeNodeClusterService.findById(it))
            } catch (e: Exception) {
                this.logger.error("Load edge node ${it} error", e);
            }
        }
        r.putBundle("edgeNodeClusters", nodes);
    }

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("")
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr, @RequestParam("search", required = false) search: String?
    ): RestResp {
        val queries = BooleanBuilder();
        val a = QEdgeNodeRoute.a;
        queries.and(currUserMgrOrgQuery(a.belongOrg))
        val p = edgeNodeRouteService.findAll(queries, pageAttr)
        val routes = edgeNodeRouteService.findAll().toMutableList()
        p.content.forEach { r ->
            fetchEdgeNodes(r)
            r.putBundle("repeats", edgeNodeRouteService.checkRepeat(routes, r).map { it.network })
        }
        return jsonOut(p);
    }

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("{id:\\d+}")
    fun view(@PathVariable("id") id: Long): RestResp {
        val data = edgeNodeRouteService.findById(id)
        currUserMgrOrgRequire(data.belongOrg!!)
        fetchEdgeNodes(data)
        return jsonOut(data);
    }

    @Secured(Permissions.PROXY_NODE_VIEW)
    @RequestMapping("match_test", method = [RequestMethod.POST, RequestMethod.GET])
    fun match(@RequestParam("ip") ip: String): RestResp {
        val hit = edgeNodeRouteService.route(ip.split(Regex("[\\s,]+")).toTypedArray())
        if (hit != null) fetchEdgeNodes(hit)
        return jsonOut(hit);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.PROXY_NODE_EDIT)
    fun delete(@PathVariable("id") id: Long): RestResp {
        val data = edgeNodeRouteService.findById(id)
        currUserMgrOrgRequire(data.belongOrg!!)
        edgeNodeRouteService.delete(data);
        edgeNodeRouteService.runInAfterTranCommitted {
            edgeNodeRouteService.reloadRoutesCache();
        }
        addOptLog(OptLog.DELETE, "代理节点路由", data.network, data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.PROXY_NODE_EDIT)
    @Transactional
    fun edit(@ModelAttribute("form") form: EdgeNodeRoute): RestResp {
        if (form.belongOrg == null) return jsonOut(1, "请选择管理机构")
        currUserMgrOrgRequire(form.belongOrg!!)
        val data: EdgeNodeRoute;
        if (form.id != null) {
            data = edgeNodeRouteService.findById(form.id!!);
        } else {
            if (form.edgeNodeClusters.isEmpty()) return RestResp(3, "代理节点集群必须选择")
            data = EdgeNodeRoute();
            data.status = EdgeNodeRoute.Status.Disabled;
        }
        if (!form.network.matches(Regex("^\\d+\\.\\d+\\.\\d+\\.\\d+/\\d+$"))) {
            return RestResp(1, "网络格式错误")
        }

        val networkSameData = edgeNodeRouteService.findOne(QEdgeNodeRoute.a.network.eq(form.network))
        if (networkSameData != null && networkSameData.id != data.id) return this.jsonOut(1, "网络重复");
        val changedEdgeNodes = mutableSetOf<Long>()
        changedEdgeNodes.addAll(form.edgeNodeClusters)
        changedEdgeNodes.addAll(data.edgeNodeClusters)
        data.edgeNodeClusters = form.edgeNodeClusters.map { edgeNodeClusterService.findById(it) }.map { currUserMgrOrgRequire(it.belongOrg!!); it.id!! }.toMutableList()

        if (!form.network.endsWith("/32")) {
            val netInfo = SubnetUtils(form.network)
            data.network = netInfo.info.cidrSignature
            data.highAddress = netInfo.info.highAddress
            data.lowAddress = netInfo.info.lowAddress
            data.addressCount = netInfo.info.addressCountLong
        } else {
            data.network = form.network
            data.highAddress = form.network.replace("/32", "")
            data.lowAddress = form.network.replace("/32", "")
            data.addressCount = 1
        }

        data.sortIndex = form.sortIndex
        data.belongOrg = form.belongOrg
        if (form.status != null) data.status = form.status
        edgeNodeRouteService.save(data);
        changedEdgeNodes.forEach {
            val cluster = edgeNodeClusterService.findById(it);
            cluster.configVersion++;
            edgeNodeClusterService.save(cluster);
            logger.info("Add edge_node config version. ${cluster} ${cluster.configVersion}")
        }
        edgeNodeRouteService.runInAfterTranCommitted {
            edgeNodeRouteService.reloadRoutesCache();
        }
        if (form.id == null) addOptLog(OptLog.ADD, "代理节点路由", data.network, data);
        else addOptLog(OptLog.EDIT, "代理节点路由", data.network, data);
        return jsonOut(data.id);
    }

    @RequestMapping(value = ["import"], method = [RequestMethod.POST])
    @Transactional
    @Secured(Permissions.PROXY_NODE_EDIT)
    @JsonView(JsonViews.View::class)
    fun import(@RequestParam("upfile") upfile: MultipartFile): RestResp {
        val xlsData = AppUtils.readExcel(upfile.inputStream)
        val columns = xlsData[0]
        val columnDefines = mutableListOf<String>("代理节点", "状态", "优先级", "网络")
        columnDefines.forEachIndexed { i, s ->
            if (columns[i] != s) return this.jsonOut(1, "第${i + 1}列不正确,应该是 ${s}")
        }
        var nonString = fun(row: Int, column: Int): String {
            var row = xlsData[row]
            if (StringUtils.isEmpty(row[column].toString().trim())) error("第${row}行，${columnDefines[column]} 不能为空")
            return row[column].toString().trim()
        }
        var errMsg = mutableListOf<String>()
        var errorCount = 0;
        var successCount = 0;
        val edgeNodeRoutes = mutableListOf<EdgeNodeRoute>()
        xlsData.forEachIndexed { i, row ->
            if (i == 0) return@forEachIndexed
            try {
                var ri = 0;
                var edgeNodeNames = nonString(i, ri++).split(Regex("[,\\|，]")).map { it.trim() }.filter { it.isNotEmpty() }
                if (edgeNodeNames.isEmpty()) {
                    error("代理节点为空")
                }
                var status = nonString(i, ri++)
                if (!(status.equals("启用") || status.equals("禁用"))) error("状态的数据不准确，请填写'启用'与'禁用'")
                var sortIndex = nonString(i, ri++)
                var network = nonString(i, ri++)
                if (!network.matches(Regex("^\\d+\\.\\d+\\.\\d+\\.\\d+/\\d+$"))) {
                    error("网络格式错误")
                }
                var clusters = edgeNodeClusterService.repo.findAll(QEdgeNodeCluster.a.name.`in`(edgeNodeNames))
                edgeNodeNames.forEach { name ->
                    var node = clusters.find { it.name == name }
                    if (node == null) error("代理节点集群${name}不存在")
                    else if (!getCurrUserCanMgrOrg(node.belongOrg!!)) error("代理节点集群${name}无管理权限")
                }
                var route = edgeNodeRouteService.findOne(QEdgeNodeRoute.a.network.eq(network))
                if (route != null) error("网络重复${network}");
                route = EdgeNodeRoute();
                route.edgeNodeClusters = clusters.map { it.id!! }.toMutableList();
                if (status.equals("启用")) route.status = EdgeNodeRoute.Status.Enabled;
                else route.status = EdgeNodeRoute.Status.Disabled;
                route.sortIndex = sortIndex.toDouble().toInt();
                val netInfo = SubnetUtils(network)
                route.network = netInfo.info.cidrSignature
                route.highAddress = netInfo.info.highAddress
                route.lowAddress = netInfo.info.lowAddress
                route.addressCount = netInfo.info.addressCountLong
                edgeNodeRouteService.repo.save(route)
                successCount++
                edgeNodeRoutes.add(route)
            } catch (e: Exception) {
                errorCount++
                errMsg.add(e.message ?: "空指针")
                logger.error("导入失败", e)
            }
        }
        addOptLog(OptLog.IMPORT, "代理节点路由", "${edgeNodeRoutes.size}条", edgeNodeRoutes);
        return this.jsonOut(mutableMapOf("count" to mutableMapOf("success" to successCount, "error" to errorCount), "msg" to errMsg))
    }
}
package com.vsimtone.pddp.server.controllers.admin

import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.QOptLog
import org.apache.commons.lang3.StringUtils
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController()
@RequestMapping("opt_logs")
class OptLogController : BaseHttpController() {

    @Secured(Permissions.OPT_LOG_VIEW)
    @GetMapping("")
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("type", required = false) type: String?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("moduleName", required = false) moduleName: String?,
        @RequestParam("objName", required = false) objName: String?,
        @RequestParam("username", required = false) username: String?
    ): RestResp {
        val q = QOptLog.a
        val queries = BooleanBuilder()
        if (type != null)
            queries.andAnyOf(BooleanBuilder(q.type.eq(type)))
        if (startTime != null)
            queries.andAnyOf(BooleanBuilder(q.createdAt.gt(startTime)))
        if (endTime != null)
            queries.andAnyOf(BooleanBuilder(q.createdAt.lt(endTime)))
        if (!StringUtils.isEmpty(moduleName?.trim()))
            queries.andAnyOf(BooleanBuilder(q.moduleName.like("%$moduleName%")))
        if (!StringUtils.isEmpty(objName?.trim()))
            queries.andAnyOf(BooleanBuilder(q.objName.like("%$objName%")))
        if (!StringUtils.isEmpty(username?.trim()))
            queries.andAnyOf(BooleanBuilder(q.username.like("%$username%")))
        pageAttr.sort = arrayListOf("desc_id");
        return jsonOut(optLogService.findAll(queries, pageAttr))
    }
}
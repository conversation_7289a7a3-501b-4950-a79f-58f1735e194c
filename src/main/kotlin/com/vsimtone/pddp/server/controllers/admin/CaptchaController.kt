package com.vsimtone.pddp.server.controllers.admin

import com.github.cage.Cage
import com.github.cage.image.RgbColorGenerator
import com.vsimtone.pddp.server.services.CaptchaService
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import jakarta.servlet.http.HttpSession
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.util.*

@RequestMapping(path = arrayOf("/captcha"))
@RestController
class CaptchaController {

    @Autowired
    lateinit var captchaService: CaptchaService

    companion object

    val logger = LoggerFactory.getLogger("captchaController")

    @RequestMapping(method = arrayOf(RequestMethod.POST, RequestMethod.GET))
    @ResponseBody
    fun output(
        session: HttpSession,
        req: HttpServletRequest,
        resp: HttpServletResponse,
        @RequestParam("type", required = false) type: CaptchaService.Companion.Type?
    ) {
        if (type == null || type == CaptchaService.Companion.Type.Image) {
            val cage = Cage(null, null, RgbColorGenerator(Random()), null, 0.5F, null, Random())
            val os = resp.outputStream
            resp.contentType = "image/jpeg"
            cage.draw(captchaService.generateImage(session), os)
            return
        }
    }
}
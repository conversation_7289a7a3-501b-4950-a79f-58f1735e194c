package com.vsimtone.pddp.server.controllers.admin

import com.vsimtone.pddp.server.beans.ClientDeviceClientConfig
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.OptLog
import com.vsimtone.pddp.server.entities.EdgeNode
import com.vsimtone.pddp.server.entities.EdgeNodeCluster
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.JSONUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestWrapper
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import kotlin.jvm.java

/**
 */
@RestController("appConfigController")
@RequestMapping("appconfigs")
class APPConfigController : BaseHttpController() {

    @Autowired
    private lateinit var appConfigService: APPConfigService

    @Autowired
    private lateinit var edgeNodeService: EdgeNodeService

    fun checkKey(vararg key: String) {
        var keys = mutableListOf<String>(
            "config.save.info",
            FileService.CNF_FILE_DOWNLOAD_SPEED,
            ClientDeviceService.CNF_CLIENT_CONFIG,
            ClientDeviceService.CNF_EXPIRE_TIME,
            ClientDeviceService.CNF_HISTORY_SIZE_MAX,
            MailService.CNF_NAME_MAIL_HOST,
            MailService.CNF_NAME_MAIL_PORT,
            MailService.CNF_NAME_MAIL_USERNAME,
            MailService.CNF_NAME_MAIL_PASSWORD,
            MailService.CNF_NAME_MAIL_PROTOCOL,
            MailService.CNF_NAME_MAIL_CRYPT_PROTOCOL,
            MailService.CNF_NAME_MAIL_SEND_INTERVAL,
            ClientUpgradeFileService.CNF_KEY_ALL_MGR_ENABLE,
            EdgeNodeService.CNF_KEY_GLOBAL_EDGE_NODE_CLUSTER_CONFIG,
            "core.main_server.runtime_config",
            BOCP6TableService.CNF_KEY_DELETE_USER_STATES,
            BOCP6TableService.CNF_KEY_DELETE_ORG_STATES,
        )
        key.forEach {
            if (!keys.contains(it))
                throw RuntimeException("key $it reject.")
        }
    }

    @RequestMapping("")
    @Secured(Permissions.APP_CONFIG_EDIT)
    fun list(@RequestParam("key") key: Array<String>): RestResp {
        checkKey(*key)
        return jsonOut(appConfigService.search(*key))
    }

    @RequestMapping("/{key}")
    @Secured(Permissions.APP_CONFIG_EDIT)
    fun get(@PathVariable("key") key: String): RestResp {
        checkKey(key)
        return jsonOut(mapOf(key to appConfigService.getVal(appConfigService.findByKey(key))))
    }

    @RequestMapping(method = [RequestMethod.POST])
    @Transactional
    @Secured(Permissions.APP_CONFIG_EDIT)
    fun set(
        request: SecurityContextHolderAwareRequestWrapper,
        @RequestParam("key") key: Array<String>,
        @RequestParam("val") `val`: Array<String>,
        @RequestParam("type") type: Array<String>
    ): RestResp {
        checkKey(*key)
        key.forEachIndexed { index, s ->
            var v = `val`[index];
            val k = key[index];
            if (k == ClientDeviceService.CNF_CLIENT_CONFIG) {
                val cnf = JSONUtils.toObject(v, ClientDeviceClientConfig::class.java)
                cnf.lastUpdatedUser = currUser().id
                if (cnf.logMaxSize <= 1024 * 100) cnf.logMaxSize = 1024 * 100;
                v = JSONUtils.toString(cnf)
            }
            if (k == EdgeNodeService.CNF_KEY_GLOBAL_EDGE_NODE_CLUSTER_CONFIG) {
                val cnf = JSONUtils.toObject(v, EdgeNodeCluster.Companion.EdgeNodeClusterConfig::class.java)
                edgeNodeService.checkConfig(cnf)
                v = JSONUtils.toString(cnf)
            }
            appConfigService.setConfig(k, v, type[index])
            logger.info("Set ${k} = ${v}")
        }
        addOptLog(OptLog.EDIT, "系统设置", "系统设置", mapOf("keys" to key, "values" to `val`));
        return jsonOut(null);
    }


}

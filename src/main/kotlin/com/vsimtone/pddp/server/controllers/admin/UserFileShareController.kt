package com.vsimtone.pddp.server.controllers.admin

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.pddp.server.beans.PageAttr
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.*
import com.vsimtone.pddp.server.entities.QUserShare
import com.vsimtone.pddp.server.json.JsonViews
import com.vsimtone.pddp.server.repositories.UserFolderRepository
import com.vsimtone.pddp.server.services.FileService
import com.vsimtone.pddp.server.services.UserFileService
import com.vsimtone.pddp.server.services.UserFileShareService
import com.vsimtone.pddp.server.services.UserFolderService
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RequestMapping("user_file_share")
@RestController
class UserFileShareController : BaseHttpController() {


    @Resource
    lateinit var userFileShareService: UserFileShareService

    @Resource
    lateinit var userFolderService: UserFolderService

    @Resource
    lateinit var userFileService: UserFileService

    @Resource
    lateinit var userServiceInstance: com.vsimtone.pddp.server.services.UserService


    @Secured(Permissions.USER_FILE_VIEW)
    @RequestMapping("list")
    @JsonView(JsonViews.SpecList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("folderId", required = false) folderId: Long?,
        @RequestParam("fileName", required = false) fileName: String?
    ): RestResp {
        try {
            val user:User = currUser()
            val queries = BooleanBuilder();
            val a = QUserShare.a;
            queries.and(a.fromUser.eq(user))
            if (StringUtils.isNotEmpty( fileName))
                queries.and(a.userFile.fileName.like("%${fileName}%"))
            if (folderId != null) {
                val folder = userFolderService.findById(folderId)
                queries.and(a.userFile.folder.eq(folder))
            }

            return jsonOut(userFileShareService.findAll(queries, pageAttr));
        } catch (e: Exception) {
            e.printStackTrace()
            return jsonOut(0, "获取分享列表失败: ${e.message}")
        }
    }



    // 创建分享
    @Secured(Permissions.USER_FILE_EDIT)
    @PostMapping("")
    @Transactional
    fun edit(@ModelAttribute("form") form: UserShare): RestResp {
        try {
            val user: User = currUser()
            
            if (form.userFile == null) {
                return jsonOut(0, "请选择要分享的文件")
            }
            
            if (form.toUser == null) {
                return jsonOut(0, "请选择分享给的用户")
            }
            
            // 验证文件所有权
            val userFileId = form.userFile!!.id!!
            val userFile = userFileService.findById(userFileId)
            if (userFile.owner.id != user.id) {
                return jsonOut(0, "没有权限分享此文件")
            }
            
            // 验证目标用户
            val toUserId = form.toUser!!.id!!
            val toUser = userServiceInstance.findById(toUserId)
            if (toUser == null) {
                return jsonOut(0, "目标用户不存在")
            }
            
            // 检查是否已经分享给该用户
            val existingShare = userFileShareService.findOne(
                QUserShare.a.fromUser.eq(user)
                    .and(QUserShare.a.userFile.eq(userFile))
                    .and(QUserShare.a.toUser.eq(toUser))
            )
            
            if (existingShare != null) {
                return jsonOut(0, "已经分享给该用户")
            }
            
            // 创建分享
            val share = UserShare()
            share.fromUser = user
            share.toUser = toUser
            share.userFile = userFile
            share.shareToType = UserShare.Companion.ShareToType.User
            share.shareTargetType = UserShare.Companion.ShareTargetType.File
            
            userFileShareService.save(share)
            
            return jsonOut(share)
        } catch (e: Exception) {
            e.printStackTrace()
            return jsonOut(0, "创建分享失败: ${e.message}")
        }
    }


    @Secured(Permissions.USER_FILE_EDIT)
    @DeleteMapping("")
    @Transactional
    fun delete(@RequestParam("id",  required = true) id: Long): RestResp {
        val userShare: UserShare = userFileShareService.findById(id)
        userFileShareService.delete(userShare)
        return jsonOut(id)
    }



}
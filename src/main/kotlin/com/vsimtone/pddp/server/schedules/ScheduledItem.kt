package com.vsimtone.pddp.server.schedules

import com.vsimtone.pddp.server.services.RedisService
import com.vsimtone.pddp.server.services.SysService
import com.vsimtone.pddp.server.utils.DateUtil
import com.vsimtone.pddp.server.utils.Log
import org.redisson.api.RBucket
import org.redisson.client.codec.LongCodec
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Lazy
import org.springframework.scheduling.support.CronExpression
import java.io.IOException
import java.time.Instant
import java.util.*

/**
 * Created by zhangkun on 16-4-6.
 */
abstract class ScheduledItem : Runnable {


    private var cronSeq: CronExpression? = null
    private var _cron: String? = null
    private lateinit var id: String
    protected var logger = Log[this.javaClass]

    @Autowired
    protected lateinit var redisService: RedisService

    @Autowired
    @Lazy
    protected lateinit var sysService: SysService

    lateinit var ctx: ApplicationContext;

    private var isAutowired: Boolean = false

    private var nextExec = 0L;


    var fixDelay: Long = 0L
    var runInTran = false
    var runInReadOnlyAndNoTran = false
    var lastExecBucket: RBucket<Long>? = null

    fun setId(id: String) {
        this.id = "schedule:${id}"
    }


    private fun updateNextExecTime(execTime: Long? = null) {
        if (this.lastExecBucket == null) this.lastExecBucket = redisService.redisson.getBucket<Long>("$id:exec_time", LongCodec())
        var lastExec = 0L;
        if (execTime != null)
            lastExec = execTime
        else
            lastExec = lastExecBucket!!.get() ?: 0;
        var _nextExec: Instant? = null
        if (cronSeq != null) {
            _nextExec = cronSeq!!.next(Date(lastExec).toInstant())
        } else if (fixDelay != 0L) {
            _nextExec = Instant.ofEpochMilli(lastExec + fixDelay)
        }
        if (_nextExec == null) {
            nextExec = 0L;
            logger.warn("$this : next exec date is null, never scheduled.")
            return
        }
        nextExec = _nextExec.toEpochMilli();
    }

    fun onAddToScheduleService() {
        updateNextExecTime()
    }

    fun canSchedule(): Boolean {
        if (System.currentTimeMillis() > nextExec && nextExec != 0L) {
            updateNextExecTime()
        }
        if (System.currentTimeMillis() > nextExec && nextExec != 0L) {
            return true;
        }
        return false;
    }

    private fun doScheduled() {
        if (!isAutowired) {
            ctx.autowireCapableBeanFactory.autowireBean(this)
            isAutowired = true;
        }
        try {
            val locked = redisService.runInLock(id) {
                updateNextExecTime();
                if (!(System.currentTimeMillis() > nextExec && nextExec != 0L))
                    return@runInLock

                logger.debug("$this : run start.")
                val started = System.currentTimeMillis()
                lastExecBucket!!.set(System.currentTimeMillis())
                if (runInReadOnlyAndNoTran) {
                    sysService.runInReadOnlyAndNoTran { scheduled() }
                } else if (runInTran) {
                    sysService.runInTran { scheduled() }
                } else {
                    scheduled()
                }
                lastExecBucket!!.set(System.currentTimeMillis())
                logger.debug("$this : run stop. use " + DateUtil.useTimeToHum(System.currentTimeMillis() - started))

            }
            if (!locked) {
                updateNextExecTime(System.currentTimeMillis())
            }
        } catch (e: IOException) {
            logger.error("$this : scheduled error.", e)
        }
    }

    override fun run() {
        this.doScheduled();
    }

    abstract fun scheduled()

    fun setCron(cron: String?) {
        this._cron = cron
        if (cron != null)
            cronSeq = CronExpression.parse(cron)
        else
            cronSeq = null
    }

    override fun toString(): String {
        return id
    }
}

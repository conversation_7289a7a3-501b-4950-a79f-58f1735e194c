package com.vsimtone.pddp.server.schedules

import com.vsimtone.pddp.server.services.BaseService
import com.vsimtone.pddp.server.services.RedisService
import com.vsimtone.pddp.server.services.SysService
import com.vsimtone.pddp.server.services.UserService
import com.vsimtone.pddp.server.utils.Log
import org.springframework.beans.BeansException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationContextAware
import org.springframework.core.task.SimpleAsyncTaskExecutor
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

/**
 * Created by z<PERSON><PERSON><PERSON> on 16-3-29.
 */
@Service
class ScheduledService : ApplicationContextAware {

    companion object {
        const val FIXED_DELAY = (1 * 1000).toLong()
    }

    protected var logger = Log[this.javaClass]

    val simpleAsyncTaskExecutor = SimpleAsyncTaskExecutor()

    val items = ArrayList<ScheduledItem>()

    private lateinit var appCtx: ApplicationContext

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var userService: UserService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var redisService: RedisService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var sysService: SysService

    fun addItem(item: ScheduledItem) {
        appCtx.autowireCapableBeanFactory.autowireBean(item);
        item.onAddToScheduleService()
        items.add(item)
        logger.info("add $item")
    }

    @Scheduled(fixedDelay = FIXED_DELAY, initialDelay = FIXED_DELAY * 10)
    @Throws(Exception::class)
    fun scheduled() {
        if (!sysService.thisNodeInfo.runtimeConfig.enableScheduled) return;
        if (!BaseService.AppReady) return
        if (userService.isSyncing()) return
        for (item in items) {
            item.ctx = this.appCtx
            if (item.canSchedule())
                simpleAsyncTaskExecutor.execute(item)
        }
    }

    @Throws(BeansException::class)
    override fun setApplicationContext(applicationContext: ApplicationContext) {
        this.appCtx = applicationContext
    }

}

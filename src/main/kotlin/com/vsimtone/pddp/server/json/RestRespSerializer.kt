package com.vsimtone.pddp.server.json

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import com.vsimtone.pddp.server.beans.RestResp
import java.io.IOException

class RestRespSerializer : StdSerializer<RestResp>(RestResp::class.java) {

    @Throws(IOException::class)
    override fun serialize(value: RestResp, gen: JsonGenerator, provider: SerializerProvider) {
        gen.writeStartObject()
        gen.writeStringField("errmsg", value.errmsg)
        gen.writeNumberField("errcode", value.errcode)
        gen.writeFieldName("data")
        provider.defaultSerializeValue(value.data, gen)
        gen.writeEndObject()
    }

}
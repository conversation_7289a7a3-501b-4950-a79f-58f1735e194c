package com.vsimtone.pddp.server.json

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import org.springframework.data.domain.PageImpl

import java.io.IOException

class PageSerializer : StdSerializer<PageImpl<*>>(PageImpl::class.java) {

    @Throws(IOException::class)
    override fun serialize(value: PageImpl<*>, gen: JsonGenerator, provider: SerializerProvider) {
        gen.writeStartObject()
        gen.writeNumberField("number", value.number)
        gen.writeNumberField("numberOfElements", value.numberOfElements)
        gen.writeNumberField("totalElements", value.totalElements)
        gen.writeNumberField("totalPages", value.totalPages)
        gen.writeNumberField("size", value.size)
        gen.writeFieldName("content")
        provider.defaultSerializeValue(value.content, gen)
        gen.writeEndObject()
    }

}
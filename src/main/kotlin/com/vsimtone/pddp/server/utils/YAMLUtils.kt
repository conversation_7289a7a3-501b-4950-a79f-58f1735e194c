package com.vsimtone.pddp.server.utils


import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import java.text.SimpleDateFormat


object YAMLUtils {

    val om: ObjectMapper

    init {
        val build = Jackson2ObjectMapperBuilder.yaml()
        build.dateFormat(SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
        om = build.build()
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        om.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    fun <T> toObject(data: String, c: Class<T>): T {
        return om.readValue(data, c)
    }

    fun toString(data: Any): String {
        return om.writeValueAsString(data)
    }

}

package com.vsimtone.pddp.server.utils

import java.io.StringWriter

class UCLUtils {
    companion object {
        private const val UCL_TAB_SIZE = 4;
        private val UCL_TAB_SPACES = mutableMapOf<Int, String>();

        init {
            for (i in 0..100) {
                UCL_TAB_SPACES[i] = tabSpace(i);
            }
        }

        fun tabSpace(size: Int): String {
            if (UCL_TAB_SPACES[size] != null) return UCL_TAB_SPACES[size]!!;
            val sb = StringBuffer()
            for (i in 1..size * UCL_TAB_SIZE) {
                sb.append(" ")
            }
            return sb.toString();
        }

        fun uclWrite(fromMap: Boolean, data: Any, os: StringWriter, level: Int) {
            if (data is List<*> || data is Array<*>) {
                os.write(" [\n");
                var list: List<Any?>;
                if (data is Array<*>)
                    list = data.toList() as List<Any?>;
                else
                    list = data as List<Any?>;
                list.forEachIndexed { idx, it ->
                    if (it == null) return@forEachIndexed
                    os.write(tabSpace(level + 1))
                    uclWrite(false, it, os, level + 1);
                    if (idx != list.size - 1)
                        os.write(",\n");
                    else
                        os.write("\n");
                }
                os.write(tabSpace(level) + "]");
            } else if (data is Map<*, *>) {
                os.write(" {\n");
                data.forEach {
                    os.write(tabSpace(level + 1) + it.key)
                    if (it.value == null)
                        throw RuntimeException("Value is null ${it.key}")
                    uclWrite(true, it.value!!, os, level + 1);
                    os.write("\n");
                }
                os.write(tabSpace(level) + "}");
            } else {
                if (fromMap)
                    os.write(" = ")
                if (data is Int || data is Long || data is Short) {
                    os.write(data.toString());
                } else if (data is Double || data is Float) {
                    os.write(data.toString());
                } else if (data is String || data is Boolean) {
                    val _data = data.toString().replace("\\", "\\\\").replace("\"", "\\\"")
                    os.write("\"${_data}\"");
                } else {
                    throw RuntimeException("Unsupported type ${data.javaClass.simpleName}")
                }
                if (fromMap)
                    os.write(";")
            }
        }

        fun toString(data: Any): String {
            var writer = StringWriter()
            var mapData: Map<String, Any>
            if (data !is Map<*, *>) {
                mapData = JSONUtils.toObject(JSONUtils.toString(data), Map::class.java) as Map<String, Any>
            } else {
                mapData = data as Map<String, Any>
            }
            mapData.forEach { key, valData ->
                writer.write(key);
                uclWrite(true, valData, writer, 0);
                writer.write("\n");
            }
            return writer.toString()
        }
    }
}
package com.vsimtone.pddp.server.utils

import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.floor

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16-4-7.
 */
object DateUtil {
    val TIME_SCALE_EN_UNITS = arrayListOf<Array<Any>>(arrayOf(1, "ms"), arrayOf(1000, "s"), arrayOf(60, "m"), arrayOf(60, "h"), arrayOf(24, "d"));

    init {
        fun timeUnitProcess(units: ArrayList<Array<Any>>) {
            var s = 1L;
            units.forEach {
                var us = 0L;
                if (it[0] is Long)
                    us = it[0] as Long
                if (it[0] is Int)
                    us = (it[0] as Int).toLong()
                s *= us;
                it[0] = s;
            }
        }
        timeUnitProcess(TIME_SCALE_EN_UNITS)
    }

    fun now(offset: Long): Long {
        return System.currentTimeMillis() + offset
    }

    fun nowDate(offset: Long): Date {
        return Date(now(offset))
    }

    fun second(second: Long): Long {
        return second * 1000
    }

    fun minute(minute: Long): Long {
        return second(minute * 60)
    }

    fun hour(hour: Long): Long {
        return minute(hour * 60)
    }

    fun day(day: Long): Long {
        return hour(day * 24)
    }

    fun week(day: Long): Long {
        return day(day * 7)
    }

    fun useTimeToHum(t: Long): String {
        if (t <= 0) return t.toString() + TIME_SCALE_EN_UNITS[0][1];
        val data = mutableListOf<String>()
        var v = t;
        TIME_SCALE_EN_UNITS.reversed().forEach {
            val us = it[0] as Long
            val un = it[1] as String
            if (v < us) return@forEach
            data.add(floor(v / us.toDouble()).toLong().toString() + un)
            v = v.rem(us)
        }
        return data.joinToString("")
    }

    fun dayBegin(t: Long = System.currentTimeMillis(), zone: TimeZone = TimeZone.getDefault()): Long {
        return t - t % (24 * 3600 * 1000) - zone.getOffset(t)
    }

    fun dayEnd(_t: Long = System.currentTimeMillis(), zone: TimeZone = TimeZone.getDefault()): Long {
        return dayBegin(_t, zone) + 24 * 3600 * 1000 - 1000
    }

    fun format(putToCacheTime: Long): String {
        return SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(putToCacheTime)
    }
}

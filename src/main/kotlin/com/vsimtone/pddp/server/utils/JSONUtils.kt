package com.vsimtone.pddp.server.utils


import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.core.util.DefaultIndenter
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import java.text.SimpleDateFormat

/**
 * Created by zhangkun on 15-7-27.
 */
class CustomPrettyPrinter : DefaultPrettyPrinter() {
    init {
        this._arrayIndenter = DefaultIndenter.SYSTEM_LINEFEED_INSTANCE
    }

    override fun createInstance(): DefaultPrettyPrinter? {
        return CustomPrettyPrinter()
    }
}

object JSONUtils {

    val om: ObjectMapper

    init {
        val build = Jackson2ObjectMapperBuilder.json()
        build.dateFormat(SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
        om = build.build()
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        om.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        val customPrettyPrinter = CustomPrettyPrinter()
        om.setDefaultPrettyPrinter(customPrettyPrinter)
    }

    fun <T> toObject(data: String, c: Class<T>): T {
        return om.readValue(data, c)
    }

    fun toString(data: Any, pretty: Boolean = false): String {
        if (pretty) {
            return om.writerWithDefaultPrettyPrinter().writeValueAsString(data)
        }
        return om.writeValueAsString(data)
    }

}

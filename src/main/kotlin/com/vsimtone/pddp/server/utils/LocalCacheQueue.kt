package com.vsimtone.pddp.server.utils

import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.thread

/**
 * 本地缓存队列，用于提供对缓存的批量处理，到达指定数量或者周期进行处理, 缓存满时阻塞
 */
class LocalCacheQueue<T>(
    val name: String,
    val cap: Int,
    val batchProcessSize: Int,
    val batchProcessInterval: Long,
    val handler: (data: List<T>) -> Unit
) {
    companion object {
    }

    private var logger = Log[this.javaClass];
    private var cache: ArrayBlockingQueue<T> = ArrayBlockingQueue(cap, true)
    private var lock = ReentrantLock()

    init {
        thread {
            this.logger.info("${this.name} started, cap=${cap}, size=${batchProcessSize}, interval=${batchProcessInterval}");
            while (true) {
                this.process(true);
                try {
                    Thread.sleep(batchProcessInterval);
                } catch (e: Exception) {
                    break;
                }
            }
            this.logger.info("${this.name} stopped");
        }
    }

    private fun process(force: Boolean = false) {
        try {
            if (this.lock.tryLock()) {
                if (!force && cache.size < batchProcessSize) return;
                var started = System.currentTimeMillis();
                val tmp = mutableListOf<T>()
                do {
                    tmp.add(cache.poll() ?: break)
                } while (true);
                if (tmp.isEmpty()) return;
                var pollEnd = System.currentTimeMillis();
                tmp.chunked(batchProcessSize).forEach {
                    handler(it);
                }
                var totalUse = System.currentTimeMillis() - pollEnd
                if (totalUse >= 100) // 大于100毫秒
                    this.logger.warn("${this.name} process slow, items=${tmp.size}, force=${force}, pull=${DateUtil.useTimeToHum(pollEnd - started)}, handle=${DateUtil.useTimeToHum(totalUse)}");
            }
        } catch (e: Exception) {
            this.logger.error("Process error", e);
        }
    }

    fun add(e: T) {
        try {
            cache.add(e)
            this.process();
        } catch (e: Exception) {
            this.logger.error("Add error", e);
        }
    }

    fun offer(e: T): Boolean {
        val ok = cache.offer(e)
        if (ok) this.process();
        return ok;
    }

    fun contains(e: T): Boolean {
        return cache.contains(e)
    }

    fun cachedSize(): Int {
        return cache.size
    }

}

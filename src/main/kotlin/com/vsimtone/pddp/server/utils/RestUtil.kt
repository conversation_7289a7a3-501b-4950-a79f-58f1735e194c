package com.vsimtone.pddp.server.utils

import org.hibernate.Hibernate
import org.hibernate.collection.spi.PersistentBag
import org.hibernate.proxy.HibernateProxy
import org.hibernate.proxy.LazyInitializer
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.lang.reflect.Field
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method
import java.util.*
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream
import kotlin.Throws
import kotlin.reflect.KMutableProperty1

/**
 * Created by zhangkun on 2015/7/12.
 */
object RestUtil {


    internal var logger = Log.get(RestUtil::class.java)


    var excludeBeanCopy = Arrays.asList(*arrayOf("id", "createdAt", "updatedAt", "updateUser", "bundle"))

    @Throws(NoSuchMethodException::class, InvocationTargetException::class, IllegalAccessException::class)
    fun getBeanValue(o: Any, f: Field): Any? {
        val c = f.declaringClass
        val name = f.name
        val getMethodNames =
            arrayOf("get" + name.substring(0, 1).uppercase() + name.substring(1), "is" + name.substring(0, 1).uppercase() + name.substring(1), name)

        var m: Method? = null
        for (mn in getMethodNames)
            try {
                m = c.getMethod(mn)
                break
            } catch (nsm: NoSuchMethodException) {
            }

        if (m == null)
            throw RuntimeException("getBeanValue error, not found get method " + c.simpleName + "." + name)
        return m.invoke(o)
    }

    fun <T> unwrapBean(data: T): T {
        if (data is HibernateProxy) {
            return Hibernate.unproxy(data) as T
        } else if (data is LazyInitializer) {
            return data.implementation as T
        } else if (data is PersistentBag<*>) {
            return data.toArray().toMutableList() as T
        } else if (data is MutableList<*>) {
            return data.map { unwrapBean(it!!) }.toMutableList() as T
        } else if (data is Collection<*>) {
            return data.map { unwrapBean(it!!) } as T
        } else return data
    }

    fun <T, V> unwrapBean(field: KMutableProperty1<T, V>, obj: T) {
        val data = field.get(obj) ?: return
        field.set(obj, unwrapBean(data))
    }

    @Throws(Exception::class)
    fun copyBean(source: Any, dest: Any, allowNames: List<String>, excludeNames: List<String>) {
        val fs = source.javaClass.declaredFields
        val ns = ArrayList<String>()
        for (name in allowNames) {
            if (name.indexOf("[") != -1)
                ns.add(name.substring(0, name.indexOf("[")))
            else if (name.indexOf(".") != -1)
                ns.add(name.substring(0, name.indexOf(".")))
            else
                ns.add(name)
        }
        for (f in fs) {
            if (ns.contains(f.name)) {
                if (excludeNames.contains(f.name)) continue
                if (excludeBeanCopy.contains(f.name)) continue
                try {
                    AppUtils.setValue(dest, f.name, AppUtils.getValue(source, f.name))
                } catch (nsfe: NoSuchFieldException) {
                    logger.warn("Src:$source,Dest:$dest,Field:$f", nsfe)
                }

            }
        }
    }

    fun gzipEncode(data: ByteArray): ByteArray {
        var baos = ByteArrayOutputStream()
        GZIPOutputStream(baos).use {
            it.write(data)
        }
        return baos.toByteArray()
    }

    fun gzipDecode(data: ByteArray): ByteArray {
        var bais = ByteArrayInputStream(data)
        GZIPInputStream(bais).use {
            return it.readBytes()
        }
    }
}

package com.vsimtone.pddp.server.utils

import org.apache.logging.log4j.Level
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger

class Log(l: Logger) {

    var logger: Logger = l

    val level: Level
        get() = logger.level

    val name: String
        get() = logger.name

    val isDebugEnabled: Boolean
        get() = logger.isDebugEnabled

    val isInfoEnabled: Boolean
        get() = logger.isInfoEnabled

    val isWarnEnabled: Boolean
        get() = logger.isWarnEnabled


    fun debug(message: String) {
        logger.debug(message)
    }

    fun debug(message: String, vararg params: Any) {
        logger.debug(message, *params)
    }

    fun debug(message: String, t: Throwable) {
        logger.debug(message, t)
    }

    fun error(message: String) {
        logger.error(message)
    }

    fun error(message: String, vararg params: Any) {
        logger.error(message, *params)
    }

    fun error(message: String, t: Throwable) {
        logger.error(message, t)
    }

    fun fatal(message: String) {
        logger.fatal(message)
    }

    fun fatal(message: String, vararg params: Any) {
        logger.fatal(message, *params)
    }

    fun fatal(message: String, t: Throwable) {
        logger.fatal(message, t)
    }

    fun info(message: String) {
        logger.info(message)
    }

    fun info(message: String, vararg params: Any) {
        logger.info(message, *params)
    }

    fun info(message: String, t: Throwable) {
        logger.info(message, t)
    }

    fun warn(message: String) {
        logger.warn(message)
    }

    fun warn(message: String, vararg params: Any) {
        logger.warn(message, *params)
    }

    fun warn(message: String, t: Throwable) {
        logger.warn(message, t)
    }

    companion object {

        operator fun get(c: Class<*>): Log {
            return Log(LogManager.getLogger(c))
        }

        operator fun get(name: String): Log {
            return Log(LogManager.getLogger(name))
        }
    }
}
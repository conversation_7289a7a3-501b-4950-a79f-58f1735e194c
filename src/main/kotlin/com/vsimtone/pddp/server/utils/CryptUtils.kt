package com.vsimtone.pddp.server.utils

import org.apache.commons.codec.binary.Base64
import org.apache.commons.codec.binary.Hex
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.codec.digest.MessageDigestAlgorithms
import java.nio.ByteBuffer
import java.nio.charset.Charset
import java.security.MessageDigest
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.SecretKeySpec
import kotlin.random.Random


object CryptUtils {

    val UTF8 = Charset.forName("UTF-8")
    val AES = "AES"
    val AES_GCM_NOPADDING = "AES/GCM/NoPadding"
    val AES_GCM_KEY_SIZE = 32;
    val AES_GCM_IV_SIZE = 12;
    val AES_GCM_TAG_SIZE = 16;

    init {
    }

    fun randomBytes(size: Int): ByteArray {
        val data = ByteArray(size)
        Random.nextBytes(data)
        return data
    }

    fun base64encode(d: ByteArray): String {
        return Base64.encodeBase64String(d)
    }

    fun base64decode(s: String): ByteArray {
        return Base64.decodeBase64(s)
    }

    @Throws(java.lang.Exception::class)
    fun aesGCMEncrypt(plaintext: ByteArray, key: ByteArray): ByteArray { // Get Cipher Instance
        val cipher = Cipher.getInstance(AES_GCM_NOPADDING)
        // Create SecretKeySpec
        val keySpec = SecretKeySpec(key, AES)
        val iv = randomBytes(AES_GCM_IV_SIZE)
        val ivParameterSpec = GCMParameterSpec(AES_GCM_TAG_SIZE * 8, iv)
        // Initialize Cipher for ENCRYPT_MODE
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivParameterSpec)
        // Perform Encryption
        var data = cipher.doFinal(plaintext)
        val buf = ByteBuffer.allocate(iv.size + data.size)
        buf.put(iv)
        buf.put(data)
        data = buf.array()
        return data
    }

    @Throws(java.lang.Exception::class)
    fun aesGCMDecrypt(cipherText: ByteArray, key: ByteArray): ByteArray { // Get Cipher Instance
        val cipher = Cipher.getInstance(AES_GCM_NOPADDING)
        // Create SecretKeySpec
        val keySpec = SecretKeySpec(key, AES)
        // Create GCMParameterSpec
        val ivParameterSpec = GCMParameterSpec(AES_GCM_TAG_SIZE * 8, cipherText, 0, AES_GCM_IV_SIZE)
        // Initialize Cipher for DECRYPT_MODE
        cipher.init(Cipher.DECRYPT_MODE, keySpec, ivParameterSpec)
        // Perform Decryption
        return cipher.doFinal(cipherText, AES_GCM_IV_SIZE, cipherText.size - AES_GCM_IV_SIZE)
    }

    @Throws(java.lang.Exception::class)
    fun aesECBEncrypt(_plaintext: ByteArray, key: ByteArray): ByteArray {// Get Cipher Instance
        val cipher = Cipher.getInstance("AES/ECB/NoPadding")
        // Create SecretKeySpec
        val keySpec = SecretKeySpec(key, AES)
        // Initialize Cipher for DECRYPT_MODE
        cipher.init(Cipher.ENCRYPT_MODE, keySpec)
        // Perform Decryption
        val paddingLen = key.size - _plaintext.size % key.size
        val plaintext = if (paddingLen < key.size) ByteArray(_plaintext.size + paddingLen) else _plaintext
        if (plaintext != _plaintext)
            _plaintext.copyInto(plaintext)
        return cipher.doFinal(plaintext)
    }

    @Throws(java.lang.Exception::class)
    fun aesECBDecrypt(_cipherText: ByteArray, key: ByteArray): ByteArray { // Get Cipher Instance
        val cipher = Cipher.getInstance("AES/ECB/NoPadding")
        // Create SecretKeySpec
        val keySpec = SecretKeySpec(key, AES)
        // Initialize Cipher for DECRYPT_MODE
        cipher.init(Cipher.DECRYPT_MODE, keySpec)
        // Perform Decryption
        val paddingLen = key.size - _cipherText.size % key.size
        val cipherText = if (paddingLen < key.size) ByteArray(_cipherText.size + paddingLen) else _cipherText
        if (cipherText != _cipherText)
            _cipherText.copyInto(cipherText)
        return cipher.doFinal(cipherText)
    }

    open fun md5hex(source: String): String {
        return Hex.encodeHexString(
            DigestUtils.digest(
                MessageDigest.getInstance(MessageDigestAlgorithms.MD5),
                source.toByteArray()
            )
        )
    }

    open fun md5hex(source: ByteArray): String {
        return Hex.encodeHexString(
            DigestUtils.digest(
                MessageDigest.getInstance(MessageDigestAlgorithms.MD5),
                source
            )
        )
    }

    open fun sha256hex(source: String): String {
        return Hex.encodeHexString(
            DigestUtils.digest(
                MessageDigest.getInstance(MessageDigestAlgorithms.SHA_256),
                source.toByteArray()
            )
        )
    }

    open fun sha256hex(source: ByteArray): String {
        return Hex.encodeHexString(
            DigestUtils.digest(
                MessageDigest.getInstance(MessageDigestAlgorithms.SHA_256),
                source
            )
        )
    }

    open fun sha512hex(source: String): String {
        return Hex.encodeHexString(
            DigestUtils.digest(
                MessageDigest.getInstance(MessageDigestAlgorithms.SHA_512),
                source.toByteArray()
            )
        )
    }

    fun genKey(): ByteArray {
        val keyGenerator = KeyGenerator.getInstance(AES)
        val key = keyGenerator.generateKey()
        return key.encoded
    }
}
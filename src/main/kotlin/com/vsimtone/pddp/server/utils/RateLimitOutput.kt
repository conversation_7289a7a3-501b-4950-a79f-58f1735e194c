package com.vsimtone.pddp.server.utils

import com.google.common.util.concurrent.RateLimiter
import java.io.OutputStream
import java.util.concurrent.locks.ReentrantLock

class RateLimitOutput(speedPerSecond: Long) {

    companion object {
        enum class Priority { Low, Normal, High }
    }

    var writeCount = 0L;
    var maxWriteCount = 0L
    var rateLimit: RateLimiter
    var lock: ReentrantLock

    init {
        this.rateLimit = RateLimiter.create(1000.0)
        this.lock = ReentrantLock()
        setSpeed(speedPerSecond)
    }

    fun setSpeed(speed: Long) {
        this.maxWriteCount = speed / 1000
    }

    private fun write(os: OutputStream, b: Int) {
        if (maxWriteCount == 0L)
            return os.write(b)
        writeCount++;
        if (writeCount >= maxWriteCount) {
            os.flush();
            this.rateLimit.acquire(1);
            writeCount = 0;
        }
        return os.write(b)
    }

    fun writeByPriority(getOut: () -> OutputStream, priority: Priority, b: ByteArray) {
        var sleepTime = 200L;
        if (priority == Priority.Low)
            sleepTime = 400L;
        else if (priority == Priority.High)
            sleepTime = 1L;
        var waitCount = 0;
        while (true) {
            if (this.lock.tryLock()) {
                var os: OutputStream? = null
                try {
                    os = getOut()
                    b.forEach {
                        this.write(os, it.toInt())
                    }
                    return;
                } finally {
                    try {
                        os?.close()
                    } finally {
                        this.lock.unlock();
                    }
                }
            }
            Thread.sleep(sleepTime);
            waitCount++;
            if (waitCount >= sleepTime)
                sleepTime--;
            if (sleepTime <= 0L)
                sleepTime = 1L;
        }
    }


}
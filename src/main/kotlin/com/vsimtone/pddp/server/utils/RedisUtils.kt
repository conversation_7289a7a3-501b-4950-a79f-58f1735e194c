package com.vsimtone.pddp.server.utils

import org.redisson.client.codec.*
import org.redisson.codec.TypedJsonJacksonCodec

class RedisUtils {
    companion object {
        var logger = Log.get(RedisUtils::class.java)
        private val codecMap = mutableMapOf<Class<*>, Codec>(
            String::class.java to StringCodec(),
            Long::class.java to LongCodec(),
            Integer::class.java to IntegerCodec(),
            Double::class.java to DoubleCodec(),
            ByteArray::class.java to ByteArrayCodec()
        )

        private fun getCodec(c: Class<*>?): Codec? {
            if (c == null) return null;
            var codec = codecMap[c];
            if (codec != null) return codec;
            if (c.isInstance(Collection::class.java)) throw RuntimeException("List not support codec, use array.")
            if (c.isInstance(Map::class.java)) throw RuntimeException("Map not support codec.")
            synchronized(codecMap) {
                logger.info("Create redis codec for ${c}")
                codec = TypedJsonJacksonCodec(c, JSONUtils.om)
                codecMap[c] = codec!!
            }
            return codec!!
        }

        fun autoCodec(valCls: Class<*>, keyCls: Class<*>? = null): Codec {
            if (keyCls == null) {
                return getCodec(valCls)!!
            }
            return TypedJsonJacksonCodec(keyCls, valCls, JSONUtils.om)
        }
    }
}
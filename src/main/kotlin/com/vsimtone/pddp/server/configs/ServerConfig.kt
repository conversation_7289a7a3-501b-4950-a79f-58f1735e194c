package com.vsimtone.pddp.server.configs

import jakarta.annotation.PostConstruct
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.info.BuildProperties
import org.springframework.context.annotation.Configuration

@Configuration
@ConfigurationProperties(prefix = "server")
class ServerConfig {

    @Autowired(required = false)
    var buildProperties: BuildProperties? = null


    lateinit var version: String
    var url: String? = null

    @PostConstruct
    fun init() {
        if (url == null || !url!!.endsWith("/")) {
            throw RuntimeException("application config error: server.url")
        }
        if (buildProperties == null)
            version = "DEV"
        else
            version = buildProperties!!.version
    }

}
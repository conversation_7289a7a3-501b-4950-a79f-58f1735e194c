package com.vsimtone.pddp.server.configs


import com.vsimtone.pddp.server.beans.CurrAuthedUser
import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.consts.Permissions
import com.vsimtone.pddp.server.entities.QOrganization
import com.vsimtone.pddp.server.entities.SysRole
import com.vsimtone.pddp.server.entities.User
import com.vsimtone.pddp.server.services.*
import com.vsimtone.pddp.server.utils.JSONUtils
import com.vsimtone.pddp.server.utils.Log
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.annotation.Order
import org.springframework.security.authentication.*
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.security.web.context.HttpSessionSecurityContextRepository
import org.springframework.security.web.util.matcher.AntPathRequestMatcher


@Configuration
@Order(2)
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, prePostEnabled = true, jsr250Enabled = true)
class AdminSecurityConfig : SecurityConfig() {

    private val logger = Log["admin_security"]

    @Autowired
    lateinit var userService: UserService

    @Autowired
    lateinit var sysRoleService: SysRoleService

    @Autowired
    lateinit var orgService: OrgService

    @Autowired
    lateinit var captchaService: CaptchaService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var ldapConfigService: LdapConfigService

    @Autowired
    lateinit var eventPublisher: AuthenticationEventPublisher

    @Autowired
    lateinit var optLogService: OptLogService

    @Autowired
    lateinit var licenseService: LicenseService

    @Autowired
    lateinit var appConfigService: APPConfigService

    var adminLoginAuthenticationFilter = AdminLoginAuthenticationFilter();

    class AdminLoginAuthToken(a: MutableCollection<GrantedAuthority>? = null) : AbstractAuthenticationToken(a) {
        lateinit var username: String;
        lateinit var password: String;

        override fun getCredentials(): Any {
            return ""
        }

        override fun getPrincipal(): Any {
            return username
        }
    }

    inner class AdminLoginAuthenticationFilter() : AbstractAuthenticationProcessingFilter(AntPathRequestMatcher("/users/login", "POST")) {
        init {
            setAuthenticationSuccessHandler() { _: HttpServletRequest, resp: HttpServletResponse, _: Authentication ->
                OutputResp(resp, RestResp(null))
            }
            setAuthenticationFailureHandler() { _: HttpServletRequest, resp: HttpServletResponse, a: AuthenticationException ->
                OutputResp(resp, RestResp(1, a.message))
            }
            setSecurityContextRepository(HttpSessionSecurityContextRepository())
        }

        override fun attemptAuthentication(request: HttpServletRequest, response: HttpServletResponse): Authentication? {
            var username = request.getParameter("username");
            var password = request.getParameter("password");
            var domain = request.getParameter("domain");
            var captcha = request.getParameter("captcha");
            val session = request.getSession(false)
            if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
                OutputResp(response, RestResp(1, "用户账户或密码错误"))
                return null
            }
            if (!captchaService.validateImage(session, captcha)) {
                OutputResp(response, RestResp(1, "验证码错误"))
                return null
            }

            if (StringUtils.isNotEmpty(domain) && domain != "local") username = "$username@$domain"
            var token = AdminLoginAuthToken()
            token.username = username
            token.password = password
            return this.authenticationManager.authenticate(token)
        }

    }

    inner class AdminAuthProvider : AuthenticationProvider {

        override fun authenticate(authentication: Authentication): Authentication? {
            var token = authentication as AdminLoginAuthToken
            var user: User? = null
            val username = token.username
            val password = token.password
            token.password = "";
            token.details = null
            redisService.runInLock("login:${username}") {
                val _user = userService.findByUsername(username) ?: throw UsernameNotFoundException("用户账户或密码错误");
                try {
                    val errMsg = userService.checkPassword(_user, password)
                    if (errMsg != null) {
                        throw BadCredentialsException("认证失败: ${errMsg}")
                    }
                } catch (e: Exception) {
                    if (e is BadCredentialsException) throw e;
                    throw InternalAuthenticationServiceException("密码验证失败,请联系管理员!", e)
                }
                if (!_user.manager || _user.manageOrgs.isEmpty() || _user.manageRoles.isEmpty()) throw InsufficientAuthenticationException("用户无管理员权限")
                if (!_user.enabled) throw LockedException("用户已被禁用")
                if (_user.locked) throw DisabledException("用户已被锁定")
                user = _user
            }
            val licenseStatus = licenseService.getLicenseStatus()
            val details = CurrAuthedUser()
            if (user != null) {
                details.id = user!!.id!!
                details.nickname = user!!.nickname
                details.username = user!!.username
                details.manager = user!!.manager
                details.manageOrgs = user!!.manageOrgs.toMutableList()
                details.manageRoles = user!!.manageRoles
                details.permissions = sysRoleService.getPermissions(details.manageRoles).toMutableList()
                if (details.manageRoles.find { it.code == SysRole.Code.SuperManager } != null) {
                    orgService.findAll(QOrganization.a.parent.isNull).forEach {
                        if (!details.manageOrgs.contains(it)) details.manageOrgs.add(it)
                    }
                    Permissions.getAll().forEach {
                        if (!details.permissions.contains(it)) details.permissions.add(it)
                    }
                }
                if (appConfigService.getBoolean(ClientUpgradeFileService.CNF_KEY_ALL_MGR_ENABLE) != true) {
                    details.permissions = details.permissions.filter { it != Permissions.CLIENT_UPGRADE_VIEW && it != Permissions.CLIENT_UPGRADE_EDIT }.toMutableList()
                }
                if (licenseStatus != LicenseService.Companion.LicenseStatus.Active) details.permissions = details.permissions.filter { it == Permissions.APP_LICENSE_VIEW || it == Permissions.APP_LICENSE_EDIT }.toMutableList()
                if (details.permissions.isNotEmpty()) {
                    token = AdminLoginAuthToken(details.permissions.map { SimpleGrantedAuthority(it) }.toMutableSet());
                    token.username = username;
                    token.details = details;
                    token.isAuthenticated = true;
                    if (licenseStatus != LicenseService.Companion.LicenseStatus.Active && details.permissions.isEmpty()) {
                        throw DisabledException("当前系统授权已过期，请联系管理员处理")
                    }
                    return token;
                }
            }
            return null;
        }

        override fun supports(authentication: Class<*>?): Boolean {
            if (authentication == AdminLoginAuthToken::class.java) return true
            return false
        }

    }

    @Bean
    fun authenticationManagerBean(): AuthenticationManager {
        val m = ProviderManager(AdminAuthProvider())
        m.setAuthenticationEventPublisher(eventPublisher)
        adminLoginAuthenticationFilter.setAuthenticationManager(m)
        return m;
    }

    @Bean
    fun filterChain(adminHttpSecurity: HttpSecurity): SecurityFilterChain {
        adminHttpSecurity.authenticationManager(authenticationManagerBean());
        adminHttpSecurity.securityMatcher("/**")
        adminHttpSecurity.addFilterBefore(adminLoginAuthenticationFilter, UsernamePasswordAuthenticationFilter::class.java)
        commonConfig(
            adminHttpSecurity, arrayOf(
                "/edge_node_endpoint", "/client_route", "/home/<USER>", "/users/curr", "/users/domains", "/users/login", "/captcha", "/client_devices/latest_client_packages", "/client_devices/client_package_crypt_down_data"
            )
        )
        adminHttpSecurity.logout() {
            it.logoutUrl("/users/logout").clearAuthentication(true)
            it.invalidateHttpSession(true)
            it.addLogoutHandler { request, response, authentication ->
                val user = userService.currUser()!!
                optLogService.addOptLog(user.username, "退出登录", "用户登录", user.nickname, user);
            }
            it.logoutSuccessHandler { _, response, _ ->
                response.status = 200;
                response.writer.print(JSONUtils.toString(RestResp()))
            }
        }
        return adminHttpSecurity.build();
    }

}

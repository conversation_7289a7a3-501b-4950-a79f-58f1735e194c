package com.vsimtone.pddp.server.configs

import org.springframework.context.annotation.Configuration


@Configuration
class ClickHouseConfig {

//    @Bean("clickHouseDataSourceConfig")
//    @ConfigurationProperties("clickhouse")
//    @Qualifier("clickHouseDataSourceConfig")
//    fun clickHouseDataSourceConfig(): DataSourceProperties {
//        return DataSourceProperties()
//    }
//
//    @Bean("clickHouseJdbcTemplate")
//    fun clickHouseJdbcTemplate(
//        @Autowired
//        @Qualifier("clickHouseDataSourceConfig")
//        clickHouseDataSourceConfig: DataSourceProperties
//    ): JdbcTemplate {
//        val t = JdbcTemplate(clickHouseDataSourceConfig.initializeDataSourceBuilder().build());
//        return t;
//    }

//    @Bean
//    fun clickHouseEntityManagerFactory(@Autowired clickHouseDataSource: DataSource): LocalContainerEntityManagerFactoryBean {
//        val properties: MutableMap<String, Any> = HashMap()
//        properties["hibernate.hbm2ddl.auto"] = "update"
//        properties["hibernate.dialect"] = MySQLDialect::class.java.name
//        properties["hibernate.order_inserts"] = "true"
//        properties["hibernate.order_updates"] = "true"
//        properties["hibernate.jdbc.fetch_size"] = "500"
//        properties["hibernate.jdbc.batch_size"] = "500"
//        return entityManagerFactoryBuilder
//            .dataSource(clickHouseDataSource)
//            .packages(PerfMonStats::class.java)
//            .persistenceUnit("clickhouse")
//            .properties(properties)
//            .build()
//    }
//
//    @Bean
//    fun clickHouseEntityManager(@Autowired clickHouseEntityManagerFactory: LocalContainerEntityManagerFactoryBean): EntityManager {
//        return clickHouseEntityManagerFactory.getObject().createEntityManager();
//    }
}
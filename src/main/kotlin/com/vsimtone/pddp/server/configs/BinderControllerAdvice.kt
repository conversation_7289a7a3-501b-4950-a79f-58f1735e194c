package com.vsimtone.pddp.server.configs

import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.web.bind.WebDataBinder
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.InitBinder

/**
 * fix CVE-2022-22965
 */
@ControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
class BinderControllerAdvice {
    @InitBinder
    fun setAllowedFields(dataBinder: WebDataBinder) {
        val denyList = arrayOf("class.*", "Class.*", "*.class.*", "*.Class.*")
        dataBinder.setDisallowedFields(*denyList)
    }
}
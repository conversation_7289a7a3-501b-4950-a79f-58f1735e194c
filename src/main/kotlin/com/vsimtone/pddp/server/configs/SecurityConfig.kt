package com.vsimtone.pddp.server.configs

import com.vsimtone.pddp.server.beans.RestResp
import com.vsimtone.pddp.server.utils.JSONUtils
import jakarta.servlet.http.HttpServletResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException
import org.springframework.security.authentication.InsufficientAuthenticationException
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.core.session.SessionRegistry

open class SecurityConfig {
    @Autowired
    lateinit var sessionRegistry: SessionRegistry

    companion object {
        fun OutputResp(resp: HttpServletResponse, obj: Any) {
            resp.characterEncoding = "UTF-8";
            resp.setHeader("content-type", "application/json;charset=utf-8")
            resp.outputStream.write(JSONUtils.toString(obj).toByteArray(Charsets.UTF_8))
        }
    }

    fun commonConfig(http: HttpSecurity, apiWhitelist: Array<String>) {
        http.exceptionHandling() {
            it.authenticationEntryPoint { request, response, authException ->
                if (authException is InsufficientAuthenticationException) {
                    response.status = 401;
                    OutputResp(response, RestResp(-1, "need_login"))
                }
                if (authException is AuthenticationCredentialsNotFoundException) {
                    response.status = 401;
                    OutputResp(response, RestResp(-1, "need_login"))
                }
            }
        }
        http.sessionManagement() { it.sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED).maximumSessions(1).sessionRegistry(sessionRegistry) }
        http.anonymous() { it.disable() }
        http.rememberMe() { it.disable() }
        http.httpBasic() { it.disable() }
        http.headers() { it.frameOptions() { it.sameOrigin() } }
        http.csrf() { it.disable() }
        http.authorizeHttpRequests() {
            it.requestMatchers(*apiWhitelist).permitAll()
            it.anyRequest().authenticated()
        }
    }

}
package com.vsimtone.pddp.server.configs

import jakarta.servlet.http.HttpSession
import jakarta.websocket.HandshakeResponse
import jakarta.websocket.server.HandshakeRequest
import jakarta.websocket.server.ServerEndpointConfig
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.socket.config.annotation.EnableWebSocket
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean


@Configuration
@EnableWebSocket
class WebsocketConfig {
    @Bean
    fun createWebSocketContainer(): ServletServerContainerFactoryBean {
        val container = ServletServerContainerFactoryBean()
        container.maxBinaryMessageBufferSize = 1024 * 1024 * 10
        return container
    }

    companion object {
        class Configurator : ServerEndpointConfig.Configurator() {
            override fun modifyHandshake(sec: ServerEndpointConfig, request: HandshakeRequest, response: HandshakeResponse) {
                val httpSession = request.httpSession as HttpSession
                sec.userProperties["ClientIP"] = httpSession.getAttribute("ClientIP");
                val auth = SecurityContextHolder.getContext()?.authentication
                if (auth != null) sec.userProperties["Authentication"] = auth
            }
        }
    }

}
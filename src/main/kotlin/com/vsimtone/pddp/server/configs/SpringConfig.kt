package com.vsimtone.pddp.server.configs

import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.json.JsonMapper
import com.vsimtone.pddp.server.json.JsonViews
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.convert.converter.Converter
import org.springframework.format.FormatterRegistry
import org.springframework.http.converter.HttpMessageConverter
import org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import org.springframework.web.socket.server.standard.ServerEndpointExporter
import java.util.concurrent.Executor
import java.util.concurrent.ThreadPoolExecutor


@Configuration
@EnableCaching
class SpringConfig : WebMvcConfigurer {

    @Autowired
    lateinit var jsonMapperBuilder: JsonMapper.Builder;

    @Bean
    fun serverClientExporter(): ServerEndpointExporter {
        return ServerEndpointExporter()
    }

    @Bean
    fun taskScheduler(): TaskScheduler {
        val taskScheduler = ThreadPoolTaskScheduler()
        taskScheduler.poolSize = 100
        return taskScheduler
    }

    @Bean
    fun taskExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 10
        executor.maxPoolSize = 600
        executor.queueCapacity = 6000
        executor.keepAliveSeconds = 60
        executor.threadNamePrefix = "sys-executor-"
        executor.setRejectedExecutionHandler(ThreadPoolExecutor.CallerRunsPolicy())
        return executor
    }

    override fun configureMessageConverters(converters: MutableList<HttpMessageConverter<*>>) {
        val mapper = jsonMapperBuilder.enable(MapperFeature.DEFAULT_VIEW_INCLUSION).build()
        mapper.setConfig(mapper.serializationConfig.withView(JsonViews.Default::class.java))
        converters.removeIf { it is AbstractJackson2HttpMessageConverter }
        converters.add(MappingJackson2HttpMessageConverter(mapper));
    }

    override fun addFormatters(registry: FormatterRegistry) {
        registry.removeConvertible(String::class.java, Array<String>::class.java)
        registry.addConverter(String::class.java, Array<String>::class.java, noCommaSplitStringToArrayConverter())
        registry.removeConvertible(String::class.java, Collection::class.java)
        registry.addConverter(String::class.java, Collection::class.java, noCommaSplitStringToCollectionConverter())
    }

    @Bean
    fun noCommaSplitStringToArrayConverter(): Converter<String, Array<String>> {
        return object : Converter<String, Array<String>> {
            override fun convert(source: String): Array<String> {
                return arrayOf(source)
            }
        }
    }

    @Bean
    fun noCommaSplitStringToCollectionConverter(): Converter<String, Collection<String>> {
        return object : Converter<String, Collection<String>> {
            override fun convert(source: String): Collection<String> {
                val arrayWithOneElement = listOf(source)
                return arrayWithOneElement
            }
        }
    }
}

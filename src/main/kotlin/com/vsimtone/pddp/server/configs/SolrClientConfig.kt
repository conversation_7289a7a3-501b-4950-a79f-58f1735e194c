package com.vsimtone.pddp.server.configs

import org.apache.solr.client.solrj.SolrClient
import org.apache.solr.client.solrj.embedded.SSLConfig
import org.apache.solr.client.solrj.impl.Http2SolrClient
import org.eclipse.jetty.util.ssl.SslContextFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.io.Serializable
import java.security.SecureRandom
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager


/**
 * Created by zhangkun on 17-3-7.
 */

@Configuration
class SolrClientConfig : Serializable {

    companion object {
        @Configuration
        @ConfigurationProperties(prefix = "solr")
        class SolrConfig {
            var url: String? = null
            var user: String? = null
            var pass: String? = null
            var core: String? = null
            var trustAllCert: Boolean = false
            var maxConnections = 10
            var requestTimeoutSec = 60L
            var connectTimeoutSec = 10L
        }

        var trustAllCerts: Array<TrustManager> = arrayOf(object : X509TrustManager {
            override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
            }

            override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
            }

            override fun getAcceptedIssuers(): Array<X509Certificate> {
                return arrayOf()
            }
        })
    }

    @Autowired
    lateinit var config: SolrConfig

    @Bean
    fun solrClient(): SolrClient {
        val builder = Http2SolrClient.Builder(config.url + "${config.core}");
        builder.withBasicAuthCredentials(config.user, config.pass);
        builder.withMaxConnectionsPerHost(config.maxConnections)
        builder.withConnectionTimeout(config.connectTimeoutSec, TimeUnit.SECONDS)
        builder.withRequestTimeout(config.requestTimeoutSec, TimeUnit.SECONDS)
        if (config.trustAllCert)
            builder.withSSLConfig(object : SSLConfig(false, false, "", "", "", "") {
                override fun createClientContextFactory(): SslContextFactory.Client {
                    // Install the all-trusting trust manager
                    val sslContext = SSLContext.getInstance("TLS")
                    sslContext.init(null, trustAllCerts, SecureRandom())
                    // Configure SSL context factory
                    val sslContextFactory = SslContextFactory.Client()
                    sslContextFactory.sslContext = sslContext
                    return sslContextFactory;
                }
            })
        val client = builder.build();
        return client;
    }

}

package com.vsimtone.pddp.server.clickhouse.ddl.annotation

import java.lang.annotation.Retention
import java.lang.annotation.RetentionPolicy
import kotlin.reflect.KClass

@Retention(RetentionPolicy.RUNTIME)
annotation class Column( // 主键
    val primaryKey: Boolean = false,  // 类型
    val type: Type = Type.Auto,  // 长度
    val length: Int = 0,  // 精度
    val decimal: Int = -1,  // 空
    val nullable: <PERSON>olean = true,  // 唯一
    val unique: Boolean = false,  // 乐观锁
    val version: Boolean = false, val comment: String = "",  // 自定义定义
    val extend: String = "",  // 引用位置
    val refsIdName: String = "",  // 引用对象
    val refsTarget: KClass<*> = Any::class, val refsWhere: String = ""
) {
    enum class Type {
        Auto, VARCHAR, TEXT, INT, BIGINT, NUMERIC, DATETIME, BOOLEAN, CUSTOM
    }
}
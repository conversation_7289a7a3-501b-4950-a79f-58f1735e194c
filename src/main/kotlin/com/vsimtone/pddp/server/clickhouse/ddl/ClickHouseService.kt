package com.vsimtone.pddp.server.clickhouse.ddl

import com.google.common.base.CaseFormat
import com.vsimtone.pddp.server.clickhouse.ddl.annotation.Column
import com.vsimtone.pddp.server.clickhouse.ddl.annotation.Index
import com.vsimtone.pddp.server.clickhouse.ddl.annotation.Table
import com.vsimtone.pddp.server.entities.BaseEntity
import com.vsimtone.pddp.server.services.BaseService
import org.apache.commons.lang3.StringUtils
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider
import org.springframework.core.type.filter.AnnotationTypeFilter
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.TransactionStatus
import org.springframework.transaction.support.TransactionTemplate
import java.lang.reflect.Field
import java.sql.SQLException
import java.util.*

@Service
class ClickHouseService : BaseService() {
    class ColumnDefine {
        var name: String? = null
        var length = 0
        var decimal = 0
        var extra: String? = null
        var comment: String? = null
        var type: Column.Type? = null
        var field: Field? = null
        var defineSQL: String? = null
        var isPrimaryKey = false
        var isRefs = false
        var isVersion = false
        var refsIdName: String? = null
        var refsWhere: String? = null
        val refsCls: Class<*>? = null
    }

    class TableDefine(val name: String, val cls: Class<*>) {
        var comment: String? = null
        var columns: MutableList<ColumnDefine> = ArrayList()
        var idColumnIndex = -1

    }

//    @Autowired
//    @Qualifier("clickHouseJdbcTemplate")
    lateinit var jdbcTemplate: JdbcTemplate

    private val entityDefines: MutableMap<String, TableDefine> = HashMap()

    private inner class ColumnSchema {
        var name: String? = null
        var type: String? = null
    }

    private fun createComponentScanner(): ClassPathScanningCandidateComponentProvider {
        val provider = ClassPathScanningCandidateComponentProvider(false)
        provider.addIncludeFilter(AnnotationTypeFilter(Table::class.java))
        return provider
    }

    private fun _sn(name: String): String {
        return CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name)
    }

    @Throws(SQLException::class)
    private fun getColumnSchemas(tableName: String): ArrayList<ColumnSchema> {
        val catalog: String = jdbcTemplate.dataSource.connection.getCatalog()
        val t = jdbcTemplate.queryForList("select * from information_schema.columns where table_catalog = ?0 and table_name = ?1", catalog, tableName);
        val columns = ArrayList<ColumnSchema>()
        for (it in t) {
            if (it == null) continue
            val c = ColumnSchema()
            c.name = it["columnName"]!!.toString()
            c.type = it["columnType"]!!.toString()
            columns.add(c)
        }
        return columns
    }

    private fun getColumnDefine(field: Field): ColumnDefine {
        val c = field.getAnnotation(Column::class.java)
        val define = ColumnDefine()
        define.name = _sn(field.name)
        var len: Int = c.length
        var decimal: Int = c.decimal
        var type: Column.Type = c.type
        val vcls = field.type
        if (type == Column.Type.Auto) {
            if (vcls.isEnum) {
                type = Column.Type.VARCHAR
                len = 255
            } else if (vcls == Long::class.java || vcls == Long::class.javaPrimitiveType) {
                type = Column.Type.BIGINT
                len = 20
            } else if (vcls == Int::class.java || vcls == Short::class.java || vcls == Short::class.javaPrimitiveType || vcls == Int::class.javaPrimitiveType) {
                type = Column.Type.INT
                len = 0
            } else if (vcls == Double::class.java || vcls == Float::class.java || vcls == Double::class.javaPrimitiveType || vcls == Float::class.javaPrimitiveType) {
                type = Column.Type.NUMERIC
                len = 20
                if (decimal == -1) decimal = 6
            } else if (vcls == Date::class.java) {
                type = Column.Type.DATETIME
                len = 0
            } else if (vcls == String::class.java) {
                type = Column.Type.VARCHAR
                len = 255
            } else if (vcls == Boolean::class.java || vcls == Boolean::class.javaPrimitiveType) {
                type = Column.Type.BOOLEAN
            } else {
                throw RuntimeException("Unsupport field type : $field : $vcls")
            }
        }
        if (c.length != 0) len = c.length
        define.length = len
        define.decimal = decimal
        define.extra = c.extend
        define.field = field
        define.type = type
        define.isVersion = c.version
        if (!StringUtils.isEmpty(c.comment)) define.comment = c.comment
        if (define.isVersion && vcls != Long::class.javaPrimitiveType) throw RuntimeException("Version field type must be equals `long`")
        val sql = StringBuffer()
        sql.append("`" + define.name + "` ")
        if (type != Column.Type.CUSTOM) {
            sql.append(type.name)
            if (len > 0 && decimal >= 0) {
                sql.append("($len,$decimal)")
            } else if (len > 0) {
                sql.append("($len)")
            }
            if (!c.nullable) {
                sql.append(" NOT NUll")
            }
            if (c.unique) {
                sql.append(" UNIQUE")
            }
        }
        if (c.primaryKey) {
            define.isPrimaryKey = true
            sql.append(" NOT NULL PRIMARY KEY")
        }
        sql.append(" " + c.extend)
        define.defineSQL = sql.toString()
        return define
    }

    @Throws(SQLException::class)
    private fun updateSQL(sql: String) {
        logger.info("Mysql ddl : $sql")
        jdbcTemplate.update(sql)
    }

    @Throws(SQLException::class)
    fun doGenerate(cls: Class<*>) {
        val tabDef = TableDefine(_sn(cls.simpleName), cls)
        entityDefines[cls.name] = tabDef
        val tabDefine = cls.getAnnotation(Table::class.java) as Table
        if (!StringUtils.isEmpty(tabDefine.comment)) tabDef.comment = tabDefine.comment
        val indexes: Array<Index>? = cls.getAnnotationsByType(Index::class.java) as Array<Index>?
        var _c = cls
        while (_c != Any::class.java) {
            val fs = _c.declaredFields
            val defines: MutableList<ColumnDefine> = ArrayList()
            for (f in fs) {
                if (f.isAnnotationPresent(Column::class.java)) {
                    f.isAccessible = true
                    val colDef = getColumnDefine(f)
                    defines.add(colDef)
                }
            }
            tabDef.columns.addAll(0, defines)
            _c = _c.superclass
        }
        for (i in tabDef.columns.indices) {
            if (tabDef.columns[i].isPrimaryKey) {
                tabDef.idColumnIndex = i
                break
            }
        }
        val columnSchemas = getColumnSchemas(tabDef.name)
        if (columnSchemas.size == 0) {
            // create table
            val colDefSql: MutableList<String?> = ArrayList()
            for (i in tabDef.columns.indices) {
                val colDef = tabDef.columns[i]
                if (colDef.defineSQL != null) {
                    colDefSql.add(colDef.defineSQL)
                }
            }
            val createSQL = StringBuffer()
            createSQL.append("CREATE TABLE `" + tabDef.name + "`( " + StringUtils.join(colDefSql, ", ") + " ) " + TABLE_CREATE_DEFINE)
            updateSQL(createSQL.toString())
        } else {
            for (i in tabDef.columns.indices) {
                val colDef = tabDef.columns[i]
                if (colDef.defineSQL == null) continue
                var exists = false
                for (j in columnSchemas.indices) {
                    if (columnSchemas[j].name.equals(colDef.name, ignoreCase = true)) {
                        exists = true
                        break
                    }
                }
                if (exists) continue
                updateSQL("alter table `" + tabDef.name + "` add column " + colDef.defineSQL)
            }
        }
//        val eidx: MutableList<String> = dbSchemaMapper.findIndexBySchemaAndTable(sqlSession.getConnection().getCatalog(), tabDef.name)
        if (indexes != null) {
            for (idx in indexes) {
//                if (!eidx.contains(idx.name)) {
                updateSQL("alter table `" + tabDef.name + "` add index `" + idx.name + "`(" + StringUtils.join(idx.columns, ",") + ")")
//                    eidx.add(idx.name)
//                }
            }
        }
    }

    @Throws(SQLException::class)
    private fun afterAllGenerated() {
    }

    fun doGenerate() {
        val transactionTemplate = TransactionTemplate(platformTransactionManager)
        transactionTemplate.execute<Any?> { txStatus: TransactionStatus? ->
            val provider = createComponentScanner()
            try {
                provider.addIncludeFilter(AnnotationTypeFilter(Table::class.java))
                for (beanDef in provider.findCandidateComponents(BaseEntity::class.java.getPackage().name)) {
                    doGenerate(Class.forName(beanDef.beanClassName))
                }
                afterAllGenerated()
            } catch (e: Exception) {
                throw RuntimeException(e)
            }
            null
        }
        logger.info("Generate done")
    }

    fun getTableDefine(cls: Class<*>?): TableDefine? {
        if (!entityDefines.containsKey(cls!!.name)) throw RuntimeException("Mysql table define : " + cls.name + " not exists.")
        return entityDefines[cls.name]
    }

//    @Throws(BeansException::class)
//    override fun setApplicationContext(applicationContext: ApplicationContext) {
//        super.setApplicationContext(applicationContext)
//        doGenerate()
//    }

    companion object {
        private const val TABLE_CREATE_DEFINE = ""
    }
}
package com.vsimtone.pddp.server.beans


class ClientDeviceClientConfig {


    enum class AuthMode { Auto, Manual }

    var updatedAt = System.currentTimeMillis();

    var daemonLoopInterval = 15 * 60 * 1000L; // 默认15分钟

    var scanFileMaxsize = 30L; // 最大扫描文件
    var scanTimeout = 30L;// 扫描超时
    var decompressDepth = 5L;// 解压最大层数

    var evtCacheDay = 90L;// 事件缓存存储天数
    var evtCacheSize = 100L; // 离线事件存储空间

    var logMaxSize = 10; // 日志最大大小
    var logMaxCount = 3; // 日志最大计数

    var trayEnable = true; // 开启托盘
    var uninstallNeedPW = false; // 卸载需要密码
    var uninstallPassWD = ""; // 卸载密码
    var authMode = AuthMode.Auto; // 认证模式

    var autoShowLogin = false; // 自动显示登录界面

    var policyHaltEnable = false;//启动策略熔断
    var policyHaltInterval = 24L; //统计时间范围(H)
    var policyHaltNum = 1000L;//策略事件数量超过

    var pathWhitelist = listOf<String>();//可信任路径列表

    var processWhitelist = listOf<String>();// 进程白名单

    var netTargetWhitelist = listOf<String>();// 网络白名单

    var lastUpdatedUser: Long? = null;

}
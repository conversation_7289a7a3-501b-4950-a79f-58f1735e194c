package com.vsimtone.pddp.server.beans

import com.vsimtone.pddp.server.entities.Organization
import com.vsimtone.pddp.server.entities.SysRole

class CurrAuthedUser : java.io.Serializable {
    var id: Long = -1L
    var manager: Boolean = false
    lateinit var username: String
    lateinit var nickname: String
    lateinit var permissions: MutableList<String>
    lateinit var manageOrgs: MutableList<Organization>
    lateinit var manageRoles: MutableList<SysRole>
    override fun toString(): String {
        return "CurrAuthedUser(id=$id, username='$username')"
    }
}
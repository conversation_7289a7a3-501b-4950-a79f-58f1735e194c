package com.vsimtone.pddp.server.beans

class RestResp {

    companion object {
    }

    var errcode: Int = 0
    var errmsg: String? = null
    var data: Any? = null

    constructor() {}
    constructor(errcode: Int, data: Any?) {
        this.errcode = errcode
        this.errmsg = errmsg
        this.data = data
    }

    constructor(data: Any?) {
        this.data = data
    }

    constructor(errcode: Int, errmsg: String?) {
        this.errcode = errcode
        this.errmsg = errmsg
    }
}
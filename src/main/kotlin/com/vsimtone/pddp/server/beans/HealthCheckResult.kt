package com.vsimtone.pddp.server.beans

import java.io.Serializable

class HealthCheckResult : Serializable {

    enum class CheckStatus { Success, Error }

    class Component : Serializable {
        var name = ""
        var processId = ""
        var processName = ""
        var statusCode = 0
        var statusMsg = ""
    }

    var taskId = ""
    var checkStatus = CheckStatus.Success
    var checkErrMsg = ""

    var version = ""
    var agentId = ""
    var startupTime = 0L
    var successPingTime = 0L
    var components = mutableListOf<Component>()

}
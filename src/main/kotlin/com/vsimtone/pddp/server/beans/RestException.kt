package com.vsimtone.pddp.server.beans

// 一定要继承 RuntimeException, 否则异常时不会回滚事务
class RestException : RuntimeException {

    var errcode: Int = 0
    var errmsg: String? = null

    constructor() {
    }

    constructor(errcode: Int, errmsg: String?) : super("$errcode: $errmsg") {
        this.errcode = errcode
        this.errmsg = errmsg
    }

    constructor(errcode: Int, errmsg: String?, cause: Throwable) : super("$errcode: $errmsg", cause) {
        this.errcode = errcode
        this.errmsg = errmsg
    }
}
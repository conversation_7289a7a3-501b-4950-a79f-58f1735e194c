spring:
    flyway:
        enabled: false
        locations: db_migrations
        baseline-on-migrate: true
        table: db_migrations
        baseline-version: 1.0.0_001
    data:
        redis:
            database: 12
            poolSize: 200
            minIdleSize: 100
            repositories:
                enabled: false
    application:
        name: "PDDP Server"
    jpa:
        properties:
            hibernate:
                enable_lazy_load_no_trans: true
                order_inserts: true
                order_updates: true
                jdbc:
                    fetch_size: 500
                    batch_size: 500
        open-in-view: false
    servlet:
        multipart:
            max-request-size: 1073741824
            max-file-size: 1073741824
    cache:
        type: simple
    
    jackson:
        serialization:
            fail-on-empty-beans: false
    output:
        ansi:
            enabled: detect
    datasource:
        hikari:
            maximum-pool-size: 1000 # 最大连接数
            minimum-idle: 10 # 最小连接数
            idle-timeout: 600000 # 空闲超时，10分钟
            max-lifetime: 7200000 # 连接最大使用时间，2个小时
            leakDetectionThresholdMs: 3600000 # 连接超过1个小时未释放检测为连接泄露
management:
    client:
        metrics:
            enabled: true
        health:
            show-details: when_authorized
    clients:
        web:
            exposure:
                include: health,info,metrics
server:
    port: 8920
    servlet:
        context-path: /
        session:
            cookie:
                http-only: true
                name: "VDSID"
            timeout: 30m
        encoding:
            charset: UTF-8
            force: true
            enabled: true
    tomcat:
        max-swallow-size: 1GB
        max-http-form-post-size: 1GB
        threads:
            max: 1000
debug: false

# apache vfs file system url
# file://[ absolute-path]
# ftp://[ username[: password]@] hostname[: port][ relative-path]
# ftps://[ username[: password]@] hostname[: port][ absolute-path]
# sftp://[ username[: password]@] hostname[: port][ relative-path]
# smb://[ username[: password]@] hostname[: port][ absolute-path]
# tmp://[ absolute-path]
# ram://[ path]
# s3://[[access-key:secret-key]:sign-region]@endpoint-url/folder-or-bucket/
fs.vfs_base_url: ram:///pddp-server

solr:
    url: ""
    user: ""
    pass: ""
    core: ""
    trustAllCert: false
    connectTimeoutSec: 10
    requestTimeoutSec: 60
    maxConnections: 10

app:
    config:
        robotClientAuthKey:
        delaySaveThreadSize: 200
        clientActionThreadSize: 1000
        clientActionTimeoutSec: 30
        machineLearnWorkDir: machine-learn-work
        keytabConfigDir: keytab-config
        license_req_include_all_machine_id: false
    log:
        redirectConsoleToFile: false
        console:
            enable: true
            pattern: ""
            level: "info"
        file:
            enable: true
            path: "logs/app.log"
            max-file-count: 10
            max-file-size: 100MB
            pattern: ""
            level: "info"
        level:
            ROOT: info
    schema_update_file: ""
    
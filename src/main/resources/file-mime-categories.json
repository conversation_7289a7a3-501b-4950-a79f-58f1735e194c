{"text": {"label": "文本类", "desc": "各种类型文本文件", "mimes": ["text/x-asm", "text/x-bcpl", "text/x-c", "text/x-c++", "text/x-java", "text/x-objective-c", "text/x-awk", "text/x-execline", "text/x-gawk", "text/x-luatex", "text/x-nawk", "text/x-php", "text/x-shellscript", "text/x-systemtap", "text/x-tcl", "text/x-dmtf-mif", "text/xml", "text/x-gimp-curve", "text/x-gimp-ggr", "text/x-gimp-gpl", "text/x-po", "text/x-java", "text/x-xmcd", "text/texmacs", "text/x-lisp", "text/x-lua", "text/x-m4", "text/x-makefile", "text/calendar", "text/vcard", "text/x-modulefile", "text/x-msdos-batch", "text/x-pascal", "text/x-perl", "text/pgp", "text/x-script.python", "text/rtf", "text/x-ruby", "text/html", "text/xml", "text/vnd.sosi", "text/x-tcl", "text/x-info", "text/x-tex", "text/x-texinfo", "text/troff", "text/plain", "text/plain", "text/x-installshield-lid", "text/x-ms-adm", "text/x-ms-cpx", "text/x-ms-regedit", "text/x-ms-tag", "text/x-wine-extension-reg"]}, "font": {"label": "字体类", "desc": "各种字体文件", "mimes": ["font/otf", "font/sfnt", "font/ttf", "font/woff", "font/woff2", "font/x-dos-cpi", "font/x-drdos-cpi", "font/x-postscript-pfb"]}, "video": {"label": "视频类", "desc": "各种视频文件", "mimes": ["video/3gpp", "video/3gpp2", "video/mj2", "video/mp2t", "video/mp4", "video/mpeg", "video/mpeg4-generic", "video/quicktime", "video/vnd.dvb.file", "video/x-dv", "video/x-flc", "video/x-fli", "video/x-ifo", "video/x-jng", "video/x-m4v", "video/x-mng", "video/x-sgi-movie", "video/x-ms-asf", "video/x-flv", "video/mj2", "video/webm", "video/x-matroska", "video/x-4xmv", "video/x-amv", "video/x-cdxa", "video/x-mmm", "video/x-msvideo", "video/x-vdr", "video/ogg"]}, "image": {"label": "图像类", "desc": "各种图像文件", "mimes": ["image/avif", "image/heic", "image/heic-sequence", "image/heif", "image/heif-sequence", "image/jp2", "image/jpm", "image/jpx", "image/x-quicktime", "image/vnd.dwg", "image/x-3ds", "image/x-intergraph", "image/x-intergraph-cit", "image/x-intergraph-rgb", "image/x-intergraph-rle", "image/x-gimp-gbr", "image/x-gimp-gih", "image/x-gimp-pat", "image/x-xcf", "image/bmp", "image/bpg", "image/fits", "image/gif", "image/png", "image/tiff", "image/vnd.adobe.photoshop", "image/vnd.djvu", "image/x-atari-degas", "image/x-award-bioslogo", "image/x-award-bioslogo2", "image/x-canon-cr2", "image/x-canon-crw", "image/x-commodore-vbm", "image/x-corel-bmf", "image/x-corel-cpt", "image/x-cpi", "image/x-dcx", "image/x-deskmate-fig", "image/x-dpx", "image/x-exr", "image/x-fuji-raf", "image/x-gem", "image/x-godot-stex", "image/x-ibm-pointer", "image/x-icns", "image/x-ilab", "image/x-miff", "image/x-ms-bmp", "image/x-mvg", "image/x-niff", "image/x-olympus-orf", "image/x-os2-graphics", "image/x-os2-ico", "image/x-paintnet", "image/x-pcx", "image/x-polar-monitor-bitmap", "image/x-portable-arbitrarymap", "image/x-portable-bitmap", "image/x-portable-graymap", "image/x-portable-greymap", "image/x-portable-pixmap", "image/x-qoi", "image/x-sony-tim", "image/x-tga", "image/x-unknown", "image/x-x3f", "image/x-xfig", "image/x-xpixmap", "image/x-xv-thumbnail", "image/x-xwindowdump", "image/jls", "image/jp2", "image/jpeg", "image/jpm", "image/jpx", "image/jxr", "image/x-hsi", "image/x-jp2-codestream", "image/x-jxl", "image/x-lss16", "image/g3fax", "image/vnd.microsoft.icon", "image/wmf", "image/x-eps", "image/x-win-bitmap", "image/x.nifti", "image/vnd.fpx", "image/x-ms-awd", "image/x-wpg", "image/x-pgf", "image/webp", "image/x-corel-des", "image/svg+xml", "image/x-xcursor", "model/3mf", "model/gltf-binary", "model/vnd.collada+xml", "model/vrml", "model/x3d+vrml", "model/x3d+xml", "model/e57", "model/x-autodesk-max"]}, "audio": {"label": "音频类", "desc": "各种音频文件", "mimes": ["audio/mp4", "audio/mpeg", "audio/x-hx-aac-adif", "audio/x-hx-aac-adts", "audio/x-m4a", "audio/x-mp4a-latm", "audio/amr", "audio/basic", "audio/flac", "audio/midi", "audio/x-adpcm", "audio/x-adx", "audio/x-ape", "audio/x-bcstm", "audio/x-bcwav", "audio/x-bfstm", "audio/x-brstm", "audio/x-dec-basic", "audio/x-mod", "audio/x-musepack", "audio/x-pn-realaudio", "audio/x-psf", "audio/x-s3m", "audio/x-sap", "audio/x-unknown", "audio/x-vgm", "audio/x-vpm-garmin", "audio/x-vpm-wav-garmin", "audio/vnd.dolby.dd-raw", "audio/x-aiff", "audio/mid", "audio/x-mids", "audio/x-sfbk", "audio/x-w64", "audio/x-wav", "audio/x-syx", "audio/ogg", "application/ogg"]}, "biosig": {"label": "生物医学信号", "desc": "医疗仪器数据", "mimes": ["biosig/abf2", "biosig/alpha", "biosig/ates", "biosig/atf", "biosig/axg", "biosig/axona", "biosig/bci2000", "biosig/bdf", "biosig/brainvision", "biosig/ced", "biosig/ced-smr", "biosig/cfwb", "biosig/demg", "biosig/ebs", "biosig/edf", "biosig/embla", "biosig/etg4000", "biosig/fef", "biosig/fiff", "biosig/galileo", "biosig/gdf", "biosig/heka", "biosig/igorpro", "biosig/ishne", "biosig/mfer", "biosig/nev", "biosig/nex1", "biosig/plexon", "biosig/scpecg", "biosig/sigif", "biosig/sigma", "biosig/synergy", "biosig/tms32", "biosig/tmsilog", "biosig/unipro", "biosig/walter-graphtek", "biosig/wcp"]}, "message": {"label": "邮件消息", "desc": "如: 邮件消息文件", "mimes": ["message/news", "message/rfc822", "message/x-gnu-rmail"]}, "rinex": {"label": "导航定位", "desc": "如: 经纬度坐标", "mimes": ["rinex/broadcast", "rinex/clock", "rinex/meteorological", "rinex/navigation", "rinex/observation"]}, "office": {"label": "office/办公软件文件", "desc": "如: ms office办公软件", "mimes": ["application/vnd.oasis.opendocument.chart", "application/vnd.oasis.opendocument.chart-template", "application/vnd.oasis.opendocument.database", "application/vnd.oasis.opendocument.formula", "application/vnd.oasis.opendocument.formula-template", "application/vnd.oasis.opendocument.graphics", "application/vnd.oasis.opendocument.graphics-template", "application/vnd.oasis.opendocument.image", "application/vnd.oasis.opendocument.image-template", "application/vnd.oasis.opendocument.presentation", "application/vnd.oasis.opendocument.presentation-template", "application/vnd.oasis.opendocument.spreadsheet", "application/vnd.oasis.opendocument.spreadsheet-template", "application/vnd.oasis.opendocument.text", "application/vnd.oasis.opendocument.text-master", "application/vnd.oasis.opendocument.text-template", "application/vnd.oasis.opendocument.text-web", "application/vnd.openofficeorg.extension", "application/vnd.lotus-1-2-3", "application/vnd.lotus-wordpro", "application/vnd.ms-excel", "application/vnd.ms-htmlhelp", "application/vnd.ms-powerpoint", "application/x-ms-reader", "application/x-mswrite", "application/msword", "application/vnd.ms-excel", "application/vnd.ms-powerpoint", "application/vnd.ms-project", "application/vnd.ms-publisher", "application/vnd.ms-works", "application/vnd.ms-works-db", "application/vnd.softmaker.planmaker", "application/vnd.softmaker.presentations", "application/vnd.stardivision.cal", "application/vnd.stardivision.chart", "application/vnd.stardivision.draw", "application/vnd.stardivision.impress", "application/vnd.stardivision.math", "application/vnd.stardivision.writer", "application/vnd.stardivision.writer-global", "application/vnd.visio", "application/vnd.wordperfect", "application/x-bentley-dgn", "application/x-corel-cph", "application/x-corel-gal", "application/x-corelpresentations", "application/x-hwp", "application/x-msbinder", "application/x-ms-cag", "application/x-msi", "application/x-ms-info", "application/x-ms-jumplist", "application/x-ms-mdz", "application/x-ms-msg", "application/x-ms-oft", "application/x-ms-rra", "application/x-ms-srs", "application/x-ms-thumbnail", "application/x-ole-storage", "application/x-starcalc", "application/x-starchart", "application/x-starimpress", "application/x-starmath", "application/x-star-sdv", "application/x-starwriter", "application/x-starwriter-global", "application/x-wine-extension-msp", "application/x-pocket-word", "application/x-sc", "application/vnd.ms-outlook", "application/winhelp", "application/vnd-ms-works", "application/vnd.pagemaker", "application/x-freemind", "application/x-freeplane", "application/x-ichitaro4", "application/x-ichitaro5", "application/x-ichitaro6", "application/x-quark-xpress-3", "application/x-scribus", "application/x-stargallery-sdg", "application/x-stargallery-thm", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.openxmlformats-officedocument.presentationml.presentation"]}, "pdf": {"label": "pdf等矢量文字图形文件", "desc": "如: pdf文件", "mimes": ["application/pdf", "application/vnd.fdf", "application/postscript", "application/vnd.corel-draw"]}, "signature": {"label": "加密/签名文件", "desc": "如: 数字签名文件", "mimes": ["application/x-aes-encrypted", "application/pgp-keys", "application/pgp-encrypted", "application/pgp-keys", "application/pgp-signature"]}, "database": {"label": "数据库文件", "desc": "如: .dbf等数据库文件", "mimes": ["application/x-dbase-index", "application/x-dbase-prf", "application/x-dbf", "application/x-dbm", "application/x-dbt", "application/x-fpt", "application/x-gdbm", "application/x-msaccess", "application/x-ms-ese", "application/x-ms-sdb", "application/x-tokyocabinet-btree", "application/x-tokyocabinet-fixed", "application/x-tokyocabinet-hash", "application/x-tokyocabinet-table", "application/vnd.sqlite3", "chemical/x-pdb"]}, "compressed": {"label": "压缩文件", "desc": "如: .zip、.rar等", "mimes": ["application/epub+zip", "application/java-archive", "application/x-ace-compressed", "application/x-acronis-tib", "application/x-arc", "application/x-archive", "application/x-compress-cazip", "application/x-compress-ftcomp", "application/x-compress-j", "application/x-compress-jar", "application/x-compress-ttcomp", "application/x-cpio", "application/x-cpio#encoding:swapped", "application/x-dc42-floppy-image", "application/x-dzip", "application/x-eet", "application/x-fzip", "application/x-gtar", "application/x-installshield", "application/x-installshield-compress-szdd", "application/x-ios-app", "application/x-lha", "application/x-lzh-compressed", "application/x-ms-compress-kwaj", "application/x-ms-compress-sz", "application/x-ms-compress-szdd", "application/x-novell-compress", "application/x-rar", "application/x-tar", "application/x-ustar", "application/x-vnd.corel.designer.document+zip", "application/x-vnd.corel.draw.document+zip", "application/x-vnd.corel.draw.template+zip", "application/x-vnd.corel.symbol.library+zip", "application/x-vnd.corel.zcf.designer.document+zip", "application/x-vnd.corel.zcf.draw.document+zip", "application/x-vnd.corel.zcf.draw.template+zip", "application/x-vnd.corel.zcf.pattern+zip", "application/x-vnd.corel.zcf.symbol.library+zip", "application/x-xar", "application/x-xbmc-xbt", "application/x-zoo", "application/zip", "application/x-gzip", "application/gzip", "application/x-7z-compressed", "application/x-bzip", "application/x-bzip2", "application/x-compress", "application/x-lrzip", "application/x-lz4", "application/x-lzip", "application/x-lzma", "application/x-zip", "application/vnd.ms-cab-compressed", "application/x-lha", "application/zip", "application/warc", "application/x-ia-arc", "application/x-innosetup", "application/x-innosetup-msg", "application/x-installshield-ins", "application/x-xz", "application/x-iso9660-image"]}}
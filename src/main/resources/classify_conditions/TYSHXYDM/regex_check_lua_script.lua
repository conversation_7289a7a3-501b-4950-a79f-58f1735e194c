version = 1
function check(str)
    str = string.upper(str)
    -- check length
    local length = string.len(str)
    if 18 ~= length then
        return 0
    end

    local checksum = 0;
    local weights = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 }
    local checksums = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'T', 'U', 'W', 'X', 'Y' }

    local i = 1
    while i < length do
        local ch = string.sub(str, i, i)
        for k, v in ipairs(checksums) do
            if ch == v then
                checksum = (k - 1) * weights[i] + checksum
            end
        end
        i = i + 1
    end

    checksum = 31 - checksum % 31
    if checksum == 31 then
        checksum = 0
    end

    if checksums[checksum + 1] ~= string.sub(str, length) then
        return 0
    end

    -- success
    return 1
end
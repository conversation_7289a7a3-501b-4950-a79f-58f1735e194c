version = 1
function check(str)
    str = string.upper(str)
    -- check length
    local length = string.len(str)
    if 18 ~= length then
        return 0
    end

    -- check province
    local province = string.sub(str, 1, 6)
    -- print( province )

    -- check birthday
    local birthday = string.sub(str, 7, 14)
    local y = tonumber(string.sub(birthday, 1, 4))
    local m = tonumber(string.sub(birthday, 5, 6))
    local d = tonumber(string.sub(birthday, 7, 8))
    local curYear = tonumber(os.date("%Y", os.time()))
    if (curYear - y >= 14 or curYear - y < 0 or m < 1 or m > 12 or d < 1 or d > 32) then
        return 0
    end

    checksum = 0;
    local weights = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 }

    local i = 1
    while i < length do
        local digit = tonumber(string.sub(str, i, i)) * weights[i]
        checksum = checksum + digit
        i = i + 1
    end

    checksum = checksum % 11

    local checksums = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' }
    if checksums[checksum + 1] ~= string.sub(str, length) then
        return 0
    end

    -- success
    return 1
end
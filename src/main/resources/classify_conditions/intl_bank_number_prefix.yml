regex: (?<!\w|\d\.)\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}|\d{4}[- ]?\d{6}[- ]?\d{5}(?!\w|\.\d)
default_lens:
    - 13
    - 16
    - 18
    - 19
lua_template: |
    version = 1
    function check(str)
        local prefixes = { #value# }
        local lens = { #lens# }
        return checkBankNumber(str, prefixes, lens)
    end
banks:
    -   name: 国际-银行卡[Visa]
        code: visa
        lens:
            - 13
            - 16
            - 19
        data:
            - "40"
            - "41"
            - "42"
            - "43"
            - "44"
            - "45"
            - "46"
            - "47"
            - "48"
            - "49"
    -   name: 国际-银行卡[MasterCard]
        code: master_card
        lens:
            - 16
        data:
            - "51"
            - "52"
            - "53"
            - "54"
            - "55"
            - "22"
            - "23"
            - "24"
            - "25"
            - "26"
            - "270"
            - "271"
            - "2720"
    -   name: 国际-银行卡[Maestro]
        code: maestro_card
        lens:
            - 16
        data:
            - "50"
            - "56"
            - "57"
            - "58"
            - "67"
    -   name: 国际-银行卡[AmericanExpress]
        code: american_express
        lens:
            - 15
        data:
            - "34"
            - "37"
    -   name: 国际-银行卡[<PERSON>ver]
        code: discover
        lens:
            - 16
            - 18
        data:
            - "6011"
            - "622"
            - "644"
            - "649"
            - "65"
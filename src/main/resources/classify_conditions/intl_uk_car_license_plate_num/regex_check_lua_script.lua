version = 1
function check(str)
    if #str ~= 16 then
        logDebug("长度不正确")
        return 0
    end

    local surname = str:sub(1, 5)
    if #surname < 5 then
        surname = surname .. string.rep("9", 5 - #surname)
    end

    local year_tens = str:sub(6, 6)
    if not year_tens:match("%d") then
        logDebug("年份十位无效")
        return 0
    end

    local month = tonumber(str:sub(7, 8))
    local is_female = false

    if month > 12 then
        is_female = true
        month = month - 50
    end

    if month < 1 or month > 12 then
        logDebug("月份无效")
        return 0
    end

    local day = tonumber(str:sub(9, 10))
    if day < 1 or day > 31 then
        logDebug("日期无效: " .. day)
        return 0
    end

    local year_units = str:sub(11, 11)
    if not year_units:match("%d") then
        logDebug("年份个位无效")
        return 0
    end

    local random_digit = str:sub(14, 14)
    if not random_digit:match("%d") then
        logDebug("随机数字无效")
        return 0
    end

    local checksum = str:sub(15, 16)
    if not checksum:match("^[A-Za-z0-9]*$") then
        logDebug("校验位无效")
        return 0
    end

    return 1
end

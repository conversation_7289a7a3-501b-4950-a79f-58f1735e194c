version = 1
function check(str)
    str = string.upper(str)
    -- check length
    local length = string.len(str)
    if 17 ~= length then
        return 0
    end

    local ch_check = string.sub(str, 9, 9)
    checksum = 0;
    local weights = { 8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2 }
    local map_ch = { ["A"] = 1, ["B"] = 2, ["C"] = 3, ["D"] = 4, ["E"] = 5, ["F"] = 6, ["G"] = 7, ["H"] = 8, ["J"] = 1, ["K"] = 2, ["L"] = 3, ["M"] = 4, ["N"] = 5, ["P"] = 7, ["R"] = 9, ["S"] = 2, ["T"] = 3, ["U"] = 4, ["V"] = 5, ["W"] = 6, ["X"] = 7, ["Y"] = 8, ["Z"] = 9 }

    local i = 1
    while i < length + 1 do
        if i ~= 9 then
            local ch = string.sub(str, i, i)
            local digit = tonumber(ch)
            if digit then
                checksum = checksum + digit * weights[i]
            else
                for k, v in pairs(map_ch) do
                    if k == ch then
                        checksum = v * weights[i] + checksum
                    end
                end
            end
        end
        i = i + 1
    end

    checksum = checksum % 11
    if ch_check == 'X' and checksum == 10 then
        return 1
    end

    if tonumber(ch_check) == checksum then
        return 1
    end
    -- failed
    return 0
end
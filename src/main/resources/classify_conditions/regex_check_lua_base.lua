function logDebug(message)
    -- Get the DEBUG_MODE environment variable
    local debugMode = os.getenv("LUA_DEBUG")
    -- Check if DEBUG_MODE is set to "true"
    if debugMode == "true" then
        print(message)
    end
end
-- 下一行不能删除，加载时会删除此行上面的内容，并删除注释和logDebug行
-- base_start
function split(istr, sep)
    if sep == nil then
        sep = ",%s*"
    end
    local t = {}
    local pattern = "([^" .. sep .. "]+)"
    for str in string.gmatch(istr, pattern) do
        -- 检查 str 是否为空字符串，确保非空才插入结果表
        if str ~= "" then
            table.insert(t, str)
        end
    end
    return t
end

function startsWith(cardNumber, prefixes)
    for _, prefix in ipairs(prefixes) do
        -- Check if the cardNumber starts with the current prefix
        if cardNumber:sub(1, #prefix) == prefix then
            logDebug("card number prefix found " .. prefix)
            return true
        end
    end
    return false
end

function luhnCheck(cardNumber)
    local sum = 0
    local shouldDouble = false
    -- 从右到左遍历字符串
    for i = #cardNumber, 1, -1 do
        -- 获取当前位字符，并转换为数字
        local digit = tonumber(cardNumber:sub(i, i))
        if not digit then
            logDebug("card number has not digit " .. digit)
            return false
        end
        if shouldDouble then
            digit = digit * 2
            -- 如果结果是两位数，则减去9相当于将个位与十位数字相加
            if digit > 9 then
                digit = digit - 9
            end
        end

        sum = sum + digit
        shouldDouble = not shouldDouble
    end
    -- Luhn算法的规则是总和必须能被10整除
    logDebug("card number luhn sum " .. sum)
    return sum % 10 == 0
end

function checkBankNumber(str, prefixes, lens)
    
    -- trim space
    str = string.gsub(str, "[^%d]", "")
    -- check length
    local length = string.len(str)
    
    local foundLen = -1
    for _, v in ipairs(lens) do
        if length == v then
            foundLen = v
        end
    end
    if length ~= foundLen then
        logDebug("card number length is " .. length)
        return -1
    end
    
    -- check prefix
    if startsWith(str, prefixes) == false then
        logDebug("card number prefix miss")
        return -2
    end
    -- check luhn checksum
    if luhnCheck(str) == false then
        logDebug("card number luhn check fail")
        return -3
    end
    -- success
    return 1
end

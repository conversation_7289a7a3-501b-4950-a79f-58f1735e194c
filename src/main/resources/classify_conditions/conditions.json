[{"regexs": ["(?<!\\w|\\d\\.)(?:\\+86)?[- ]?(?:13[0-9]|14[5-9]|15[0-35-9]|166|17[1-8]|18[0-9]|19[89])[- ]?\\d{4}[- ]?\\d{4}(?!\\w|\\.\\d)|(?<![Ａ-Ｚａ-ｚ０-９])(?:\\+８６)?[- ]?(?:１３[０-９]|１４[５-９]|１５[０-３５-９]|１６６|１７[１-８]|１８[０-９]|１９[８９])[- ]?[０-９]{4}[- ]?[０-９]{4}(?![Ａ-Ｚａ-ｚ０-９])"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（中国大陆）]", "builtInCode": "phone_num_sc", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)(?:\\+852)?[- ]?[5689]\\d{7}(?!\\w|\\.\\d)"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（香港）]", "builtInCode": "phone_num_hk", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)(?:\\+886[- ]?9\\d{8}|09\\d{8})(?!\\w|\\.\\d)"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（台湾）]", "builtInCode": "phone_num_tw", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<![０-９])(?:\\+８６)?[- ]?(?:１３[０-９]|１４[５-９]|１５[０-３５-９]|１６６|１７[１-８]|１８[０-９]|１９[１８９])[- ]?[０-９]{4}[- ]?[０-９]{4}(?![０-９])|(?<!\\d)(?:\\+86)?[- ]?(?:13[0-9]|14[5-9]|15[0-35-9]|166|17[1-8]|18[0-9]|19[189])[- ]?\\d{4}[- ]?\\d{4}(?!\\d)"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[手机号码（中国大陆）-宽]", "builtInCode": "phone_num_sc_simple", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)(?:010|02\\d|0[3-9]\\d{2})-[2-9]\\d{6,7}(?!\\w|\\.\\d)|(?<!\\w|\\d\\.)\\((?:010|02\\d|0[3-9]\\d{2})\\)[2-9]\\d{6,7}(?!\\w|\\.\\d)|(?<!\\w|\\d\\.|[\\)*\\+-/])[2-9]\\d{6,7}(?!\\w|\\.\\d)"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[座机号码]", "builtInCode": "phone_num_fixed_line", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?:北京|天津|上海|重庆|河北|河南|云南|辽宁|黑龙江|湖南|湖北|江苏|山东|新疆|安徽|浙江|江西|广西|甘肃|陕西|山西|内蒙古|吉林|福建|贵州|广东|青海|四川|宁夏|海南|西藏|香港|澳门|台湾)[^，。：\"\"、]{1,15}?[盟州市区县][^，。：\"\"、]{1,15}?(?:[镇旗路村弄楼]|小区)[^，。：\"\"、]{1,15}?(?:\\d|[一二三四五六七八九十])[号室组]"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[通讯地址]", "builtInCode": "address", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?:赵|钱|孙|李|周|吴|郑|王|冯|陈|褚|卫|蒋|沈|韩|杨|朱|秦|尤|许|何|吕|施|张|孔|曹|严|华|金|魏|陶|姜|戚|谢|邹|喻|柏|水|窦|章|云|苏|潘|葛|奚|范|彭|郎|鲁|韦|昌|马|苗|凤|花|方|俞|任|袁|柳|酆|鲍|史|唐|费|廉|岑|薛|雷|贺|倪|汤|滕|殷|罗|毕|郝|邬|安|常|乐|于|时|傅|皮|卞|齐|康|伍|元|余|卜|顾|孟|平|黄|和|穆|萧|尹|姚|邵|湛|汪|祁|毛|禹|狄|米|贝|明|臧|计|伏|成|戴|谈|宋|茅|庞|熊|纪|舒|屈|项|祝|董|梁|杜|阮|蓝|闵|席|季|麻|强|贾|路|娄|危|江|童|颜|郭|梅|盛|林|刁|钟|徐|邱|骆|高|夏|蔡|田|樊|胡|凌|霍|虞|万|支|柯|昝|管|卢|莫|白|房|裘|缪|干|解|应|宗|丁|宣|贲|邓|郁|单|杭|洪|包|诸|左|石|崔|吉|钮|龚|程|嵇|邢|滑|裴|陆|荣|翁|荀|羊|于|惠|甄|曲|家|封|芮|羿|储|靳|汲|邴|糜|松|井|段|富|巫|乌|焦|巴|弓|牧|隗|山|谷|车|侯|宓|蓬|全|郗|班|仰|秋|仲|伊|宫|宁|仇|栾|暴|甘|钭|历|戎|祖|武|符|刘|景|詹|束|龙|叶|幸|司|韶|郜|黎|蓟|溥|印|宿|白|怀|蒲|邰|从|鄂|索|咸|籍|赖|卓|蔺|屠|蒙|池|乔|阳|郁|胥|能|苍|双|闻|莘|党|翟|谭|贡|劳|逄|姬|申|扶|堵|冉|宰|郦|雍|却|璩|桑|桂|濮|牛|寿|通|边|扈|燕|冀|姓|浦|尚|农|温|别|庄|晏|柴|瞿|阎|充|慕|连|茹|习|宦|艾|鱼|容|向|古|易|慎|戈|廖|庾|终|暨|居|衡|步|都|耿|满|弘|匡|国|文|寇|广|禄|阙|东|欧|殳|沃|利|蔚|越|夔|隆|师|巩|厍|聂|晁|勾|敖|融|冷|訾|辛|阚|那|简|饶|空|曾|毋|沙|乜|养|鞠|须|丰|巢|关|蒯|相|查|后|荆|红|游|竺|权|逮|盍|益|桓|公|万俟|司马|上官|欧阳|夏侯|诸葛|闻人|东方|赫连|皇甫|尉迟|公羊|澹台|公冶|宗政|濮阳|淳于|单于|太叔|申屠|公孙|仲孙|轩辕|令狐|徐离|宇文|长孙|慕容|司徒|司空)[一-龥]{1,2}"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[姓名（误报较高，请慎用）]", "builtInCode": "hum_name", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)(?:11|12|13|14|15|21|22|23|31|32|33|34|35|36|37|41|42|43|44|45|46|50|51|52|53|54|61|62|63|64|65)\\d{5}[0-4][347][01234]|[012](?:11|12|13|14|15|21|22|23|31|32|33|34|35|36|37|41|42|43|44|45|46|50|51|52|53|54|61|62|63|64|65)\\d{7}11(?!\\w|\\.\\d)"], "builtInName": "其他[增值税发票号]", "builtInCode": "vat_num", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w)(?:[GgEeSs]\\d{8}|[PpSs]\\d{7})(?!\\w)"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[大陆护照号]", "builtInCode": "china_rpc_num", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)[A-F0-9a-f]{2}(?:[-:][A-Fa-f0-9]{2}){5}(?!\\w|\\.\\d)"], "builtInName": "其他[MAC地址]", "builtInCode": "mac", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["[\\w-]{1,32}@[\\w\\.-]+(?:\\.[a-zA-Z]{2,3})"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[邮件地址]", "builtInCode": "mail_addr", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w)[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}[A-Z]{1}(?:[DF]{1}[0-9]{5}|[0-9]{5}[DF]{1})(?!\\w)"], "builtInName": "其他[新能源车牌号]", "builtInCode": "ev_car_license_plate_num", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w)[CcWw]\\d{8}(?!\\w)"], "builtInName": "中华人民共和国网络安全法-公民个人信息类[港澳通行证号]", "builtInCode": "hk_macao_pass_num", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\.)(?:25[0-4]|2[0-4]\\d|1\\d{2}|[1-9]\\d|[1-9])\\.(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[0-9]\\d|[0-9])\\.){2}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|[1-9])(?!\\w|\\.)"], "builtInName": "其他[IPv4]", "builtInCode": "ipv4", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)E[ABCDEFGHJKLMNPQRSTUVWXYZ][0-9]{7}(?!\\w|\\.\\d)"], "builtInName": "国际-护照[中国-普通-深红色]", "builtInCode": "intl_cn_passport_normal", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)[A-Z]{1}[0-9]{8}(?!\\w|\\.\\d)"], "builtInName": "国际-护照[美国]", "builtInCode": "intl_us_passport", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)[A-Z]{1}\\d{8,9}(?!\\w|\\.\\d)"], "builtInName": "国际-美国绿卡", "builtInCode": "intl_us_green_card", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)([A-Za-z0-9]{8,15}|[A-Za-z0-9](?:[- ]?[A-Za-z0-9]){7,14})(?!\\w|\\.\\d)"], "builtInName": "国际-驾照[美国-纽约]", "builtInCode": "intl_car_license_plate_us_ny", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)([A-Z][0-9]{7,14}|\\d{8,15})(?!\\w|\\.\\d)"], "builtInName": "国际-出生证明[美国-纽约]", "builtInCode": "intl_birth_cert_us_ny", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)07[1-5|7-9]\\d{8}(?!\\w|\\.\\d)"], "builtInName": "国际-手机号码[英国-伦敦-本地]", "builtInCode": "intl_phone_uk_london_local", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)\\+?44\\s*7[1-5|7-9]\\d{2}\\s*\\d{6}(?!\\w|\\.\\d)"], "builtInName": "国际-手机号码[英国-伦敦-国际]", "builtInCode": "intl_phone_uk_london_intl", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)[A-CEGHJ-PR-TW-Z][A-NP-Z](?: ?\\d{2}){3} ?[A-D ](?!\\w|\\.\\d)"], "builtInName": "国际-英国-国民保险号码", "builtInCode": "intl_uk_nino", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(^|\\s+)London.*(EC|WC|N|NW|E|W|SE|SW)\\d[A-Z0-9 ]{3,50}"], "builtInName": "国际-通讯地址[英国-伦敦]", "builtInCode": "intl_address_uk_london", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)(?:(?:\\+1|1)[ -.]?)?(?:\\(?\\s*(212|646|718|347|917|332|929)\\s*\\)?[ -]?)?(?:[2-9]\\d{2})[ -]?\\d{4}(?!\\w|\\.\\d)"], "builtInName": "国际-手机号码[美国-纽约]", "builtInCode": "intl_phone_num_us_ny", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["\\d+\\s+[a-zA-Z\\s]+,\\s*(?:brooklyn|manhattan|queens|bronx|staten island|new york),\\s*ny\\s*\\d{5}"], "builtInName": "国际-通讯地址[美国-纽约]", "builtInCode": "intl_address_us_ny", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)[\\+\\(]?\\s*852\\s*[\\\\)\\-]?\\s*[456789]\\d{3}\\s*-?\\s*\\d{4}(?!\\w|\\.\\d)"], "builtInName": "国际-手机号码[香港]", "builtInCode": "intl_phone_num_hk", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)[A-Z]{1,2}\\d{6}(?:\\(?[A-Z0-9]\\)?)?(?!\\w|\\.\\d)"], "builtInName": "国际-身份证[香港]", "builtInCode": "intl_id_card_hk", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["((香港|香港岛|九龍|新界|中環|銅鑼灣|旺角|尖沙咀|油麻地|沙田|觀塘|離島).{1,20}){1,3}(.{1,10}?(道|街|圍|里|大廈|廣場|中心|商場|號|舖|楼|樓|层|室)){2,10}"], "builtInName": "国际-通讯地址[香港-中文]", "builtInCode": "intl_address_hk_chinese", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(\\b(Flat|Unit|Shop|Road|Rd|St|Centre|Tower|Building)\\b.{1,50}){2,10}(.{1,50}(Kowloon|Central|Tsim\\sSha\\sTsui|Wan\\sChai|Admiralty|Hung\\sHom|Hong\\sKong)\\b){1,5}"], "builtInName": "国际-通讯地址[香港-英语]", "builtInCode": "intl_address_hk_eng", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)\\+?\\s*(853)?\\s*(6\\d{7}|8\\d{7}|28\\d{6})(?!\\w|\\.\\d)"], "builtInName": "国际-手机号码[澳门]", "builtInCode": "intl_phone_mo", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["(?<!\\w|\\d\\.)[125679]\\d{6}[0-9X](?!\\w|\\.\\d)"], "builtInName": "国际-身份证[澳门]", "builtInCode": "intl_id_card_mo", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["((澳門|氹仔|路環|半島|黑沙環|祐漢|筷子基).{1,20}){1,3}(.{1,10}(大馬路|街|路|圍|巷|號|座|樓|室|大厦|廣場|中心|花園)){2,10}"], "builtInName": "国际-通讯地址[澳门-中文]", "builtInCode": "intl_address_mo_chinese", "conditionType": "Content", "contentCondition": "Regex"}, {"regexs": ["((Hac\\s*Sa|Avenida|Rua|Caminho|Travessa|Largo|Edifício|Centro|Praça|Jardim|Número|Andar|No|\\/r|Unit).{1,50}){2,10}((Macau|Macaut|Taipa|Coloane|Península|Hac\\s*Sa|Yat\\s*Yuen|Koi\\s*Kei))"], "builtInName": "国际-通讯地址[澳门-葡文]", "builtInCode": "intl_address_mo_portuguese", "conditionType": "Content", "contentCondition": "Regex"}]
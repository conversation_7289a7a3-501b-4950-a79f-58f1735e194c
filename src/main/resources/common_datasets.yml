-   name: 邮箱
    type: Regex
    code: regexp_mail
    values:
        - '\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*'

-   name: 手机号（严格模式）
    type: Regex
    code: regexp_phone_number_strict
    values:
        - '(?<!(\d|\.))(?:\+?86)?1(?:3\d{3}|5[^4\D]\d{2}|8\d{3}|7(?:[0-35-9]\d{2}|4(?:0\d|1[0-2]|9\d))|9[0-35-9]\d{2}|6[2567]\d{2}|4(?:(?:10|4[01])\d{3}|[68]\d{4}|[579]\d{2}))\d{6}(?!(\d|\.))'
-   name: 手机号
    type: Regex
    code: regexp_phone_number
    values:
        - '(?:\+?86)?1(?:3\d{3}|5[^4\D]\d{2}|8\d{3}|7(?:[0-35-9]\d{2}|4(?:0\d|1[0-2]|9\d))|9[0-35-9]\d{2}|6[2567]\d{2}|4(?:(?:10|4[01])\d{3}|[68]\d{4}|[579]\d{2}))\d{6}'

-   name: 固定电话（严格模式）
    type: Regex
    code: regexp_tel_number_strict
    values:
        - '(?<!(\d|\.))(^0\d{2}-?\d{8}$)|(^0\d{3}-?\d{7}$)|(^0\d2-?\d{8}$)|(^0\d3-?\d{7}$)(?!(\d|\.))'


-   name: 固定电话
    type: Regex
    code: regexp_tel_number
    values:
        - '(^0\d{2}-?\d{8}$)|(^0\d{3}-?\d{7}$)|(^0\d2-?\d{8}$)|(^0\d3-?\d{7}$)'

-   name: 银行卡号（严格模式）
    type: Regex
    code: regexp_bank_card_number_strict
    values:
        - '(?<!(\d|\.))((?:62|88|99|19\d{2}|4\d{3}|5[1-5]\d{2}|6\d{3}|81\d{2})\d{10}|62\d{12,17})(?!(\d|\.))'

-   name: 银行卡号
    type: Regex
    code: regexp_bank_card_number
    values:
        - '((?:62|88|99|19\d{2}|4\d{3}|5[1-5]\d{2}|6\d{3}|81\d{2})\d{10}|62\d{12,17})'

-   name: 统一社会信用代码（严格模式）
    type: Regex
    code: regexp_TYSHXYDM_strict
    values:
        - '(?<![\dA-Za-z])([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})(?![\dA-Za-z])'

-   name: 统一社会信用代码
    type: Regex
    code: regexp_TYSHXYDM
    values:
        - '([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})'

-   name: 日期
    type: Regex
    code: regexp_date
    desc: "匹配格式：2022-01-01"
    values:
        - '\d{4}-\d{1,2}-\d{1,2}'

-   name: 地址
    type: Regex
    code: regexp_address
    values:
        - '([^（），。,.,\s\t\r\n：()]{1,20}(省|市|区|州|国|县|镇)){2,5}([^（），。,.,\s\t\r\n：()]{1,50}(线|号|路|院|道|米|厅|街|楼|队|组|段|村|区|城|镇|碑)){1,5}'

-   name: 日期时间
    type: Regex
    code: regexp_datetime
    desc: "匹配格式：2022-01-01 12:00:00"
    values:
        - '\d{4}-\d{1,2}-\d{1,2}\s\d{2}:\d{2}:\d{2}'

-   name: 帐户名
    type: Regex
    code: regexp_common_username
    desc: 英文字母开头加数字下划线组成的4到18位帐户名
    values:
        - '[a-zA-Z][a-zA-Z0-9_]{4,18}'

-   name: 网址(URL)
    type: Regex
    code: regexp_url
    values:
        - '[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:[0-9]{1,5})?[-a-zA-Z0-9()@:%_\\\+\.~#?&//=]*'

-   name: IPv4地址
    type: Regex
    code: regexp_ipv4
    values:
        - '((2[0-4]\d|25[0-5]|[01]?\d\d?)\.){3}(2[0-4]\d|25[0-5]|[01]?\d\d?)'

-   name: 身份证
    type: Regex
    code: id_card
    values:
        - '([1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx])|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3})'

-   name: 车牌号
    type: Regex
    code: regexp_car_num
    values:
        - '(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]\s*[A-Z]\s*(([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))'

-   name: 任意扩展名
    type: FileExt
    code: file_ext_any
    values:
        - '.any.extension'

-   name: 源代码
    type: FileExt
    code: file_ext_source_code
    values:
        - '.java'
        - '.go'
        - '.js'
        - '.css'
        - '.html'
        - '.c'
        - '.h'
        - '.hpp'
        - '.cpp'
        - '.py'

-   name: 配置文件
    type: FileExt
    code: file_ext_config
    values:
        - '.yml'
        - '.yaml'
        - '.json'
        - '.xml'
        - '.properties'

-   name: 根目录
    type: FSPath
    code: fspath_root
    values:
        - '/'

-   name: 家目录
    type: FSPath
    code: fspath_home
    values:
        - '/home'


-   name: 挂载目录
    type: FSPath
    code: fspath_mount
    values:
        - '/media'
        - '/mnt'
        - '/cdrom'
        - '/floppy'

-   name: 系统目录
    type: FSPath
    code: fspath_sys
    values:
        - '/bin'
        - '/boot'
        - '/dev'
        - '/etc'
        - '/lib'
        - '/lost+found'
        - '/proc'
        - '/sys'
        - '/lib'
        - '/lib32'
        - '/lib64'
        - '/srv'
        - '/opt'
        - '/sbin'
        - '/usr'
        - '/var'
        - '/backup'
        - '/recovery'

-   name: MTP
    type: GVFSType
    code: gvfs_mtp
    values:
        - 'gvfsd-mtp'
        - 'gvfs-mtp-volume-monitor'
-   name: PTP
    type: GVFSType
    code: gvfs_gphoto2
    values:
        - 'gvfsd-gphoto2'
        - 'gvfs-gphoto2-volume-monitor'

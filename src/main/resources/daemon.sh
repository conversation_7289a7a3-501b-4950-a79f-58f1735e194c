#!/usr/bin/env bash

name="pddp_server"
exec="exec ./bin/pddp-server"
image="pddp-server:latest"
run_args="--net host ${RUN_ARGS}"


build_image(){
    cd docker
    docker build -t $image --force-rm --pull --rm .
}

docker_restart(){
    echo Restart docker container ${name}
    docker restart ${name}
}


docker_start(){
    echo Start docker container ${name}
    docker start ${name}
}

docker_stop(){
    echo Stop docker container ${name}
    docker stop ${name}
}

docker_rm(){
    echo Remove docker container ${name}
    docker rm ${name}
}

docker_run(){
    docker_stop
    docker_rm
    echo Run docker container ${name}
    docker run --name $name -id --restart unless-stopped -v `pwd`:/opt/$name ${run_args} -e APP_MAX_MEM_GB="${APP_MAX_MEM_GB}" -e JAVA_OPTS="${JAVA_OPTS}" ${image} /opt/${name}/daemon.sh docker_entrypoint ${exec}
}

docker_entrypoint(){
    export LANG=zh_CN.UTF-8
    export LANGUAGE=zh_CN.UTF-8
    export LC_ALL=zh_CN.UTF-8
#    export DISABLED_SCHEDULED=true
    cd /opt/${name}/
    test -d logs || mkdir logs
    eval $*
}

eval $*

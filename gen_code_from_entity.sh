#!/usr/bin/env bash
packageName="com.vsimtone.pddp.server"
packageDir="$(echo $packageName | tr '.' '/')"
entityDir="src/main/kotlin/${packageDir}/entities"
repoDir="src/main/kotlin/${packageDir}/repositories"
ctrlDir="src/main/kotlin/${packageDir}/controllers"
serviceDir="src/main/kotlin/${packageDir}/services"

function newAdminController() {
  name="$1"
  filePath="${ctrlDir}/${name}Controller.kt"
  test -f $filePath && return 0
  echo Create $filePath
  cat >"$filePath" <<EOF
package ${packageName}.controllers

import ${packageName}.entities.${name}
import ${packageName}.entities.Q${name}
import ${packageName}.services.${name}Service
import ${packageName}.beans.RestResp
import ${packageName}.beans.PageAttr
import ${packageName}.json.JsonViews
import ${packageName}.consts.Permissions
import com.querydsl.core.BooleanBuilder
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.security.web.csrf.DefaultCsrfToken
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpSession

@RestController("${name}Controller")
@RequestMapping("${name}")
class ${name}Controller : BaseController() {

    @Autowired
    lateinit var service: ${name}Service

    @RequestMapping("")
    @Secured(Permissions.${name}_VIEW)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?
    ): RestResp {
        val a = Q${name}.a
        val queries = BooleanBuilder()
        if (search != null){
        }
        return jsonOut(service.findAll(queries, pageAttr));
    }

    @RequestMapping("{id:\\\\d+}")
    @Secured(Permissions.${name}_VIEW)
    fun view(@PathVariable("id") id: Long): RestResp {
        return jsonOut(service.findById(id));
    }

    @RequestMapping("{id:\\\\d+}/delete", method = [RequestMethod.POST])
    @Secured(Permissions.${name}_EDIT)
    @Transactional
    fun delete(@PathVariable("id") id: Long): RestResp {
        val data = service.findById(id);
        service.delete(data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(Permissions.${name}_EDIT)
    @Transactional
    fun edit(@ModelAttribute("form") form: ${name}): RestResp {
        var data: ${name};
        if (form.id != null) {
            data = service.findById(form.id!!);
        } else {
            data = ${name}();
        }
        service.save(data);
        return jsonOut(data.id);
    }


}
EOF
}

function newRepo() {
  name="$1"
  filePath="${repoDir}/${name}Repository.kt"
  test -f $filePath && return 0
  echo Create $filePath
  cat >"$filePath" <<EOF
package ${packageName}.repositories

import ${packageName}.entities.${name}
import org.springframework.stereotype.Repository

@Repository
interface ${name}Repository : BaseRepository<${name}> {

}
EOF
}

function newService() {
  name="$1"
  filePath="${serviceDir}/${name}Service.kt"
  test -f $filePath && return 0
  echo Create $filePath
  cat >"$filePath" <<EOF
package ${packageName}.services

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import ${packageName}.entities.${name}
import ${packageName}.repositories.${name}Repository

@Service
class ${name}Service : BaseCrudService<${name},${name}Repository>(Q${name}.a) {
}
EOF
}

name=$1
if [[ "$name" != "" ]]; then
  newRepo "$name"
  newService "$name"
  newAdminController "$name"
fi
